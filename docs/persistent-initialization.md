# Persistent Initialization System

## Overview

The Persistent Initialization System prevents redundant syncs on every page load or refresh by using localStorage to track sync state and timing. This significantly improves app performance, especially on slower devices like iPads.

## Key Components

### 1. PersistentInitializationManager

The core component that manages initialization state using localStorage.

**Key Features:**
- Tracks last sync time and success status
- Monitors page load count since last sync
- Stores data version and local data availability
- Configurable TTL and sync intervals
- Session-aware tracking

**Configuration Options:**
- `initializationTTL`: How long to consider initialization valid (default: 30 minutes)
- `minSyncInterval`: Minimum time between automatic syncs (default: 5 minutes)
- `maxPageLoadsBeforeSync`: Force sync after N page loads (default: 10)
- `maxDataAge`: Force sync if data is older than this (default: 2 hours)

### 2. SyncConfigManager

Provides predefined sync behavior profiles and runtime configuration management.

**Available Profiles:**
- **Default**: Balanced performance and reliability
- **Performance**: Optimized for speed, less frequent syncs
- **Reliability**: Frequent syncs, maximum data consistency
- **Development**: Debug-friendly with detailed logging
- **Offline-First**: Minimal syncing, maximum offline capability

### 3. Integration with Sync Managers

Both `SyncManager` and `SelectiveSyncManager` now use the persistent initialization system:

- Check `shouldSkipSync()` before performing initialization
- Mark sync start/completion/failure appropriately
- Provide force sync capabilities for manual operations

## Usage

### Basic Usage

The system works automatically once integrated. No manual intervention required for normal operation.

```typescript
// The sync managers automatically use persistent initialization
const syncManager = new SyncManager(db);
// Will skip sync if recently initialized

// Force sync when needed
await syncManager.forceSync();
```

### Configuration

```typescript
// Get sync configuration manager
const configManager = databaseService.getSyncConfigManager();

// Change sync profile
configManager.setProfile('performance');

// Update specific settings
configManager.updateConfig({
  initializationTTL: 60 * 60 * 1000, // 1 hour
  maxPageLoadsBeforeSync: 20
});

// Auto-detect optimal profile
const profile = configManager.autoDetectProfile();
```

### Manual Control

```typescript
// Force sync bypassing persistent checks
await databaseService.forceSync();

// Reset persistent state
databaseService.resetPersistentState();

// Get current state
const persistentManager = databaseService.getPersistentInitManager();
const state = persistentManager?.getState();
```

## UI Integration

### Sync Settings Dialog

A new `SyncSettingsDialog` component provides user-friendly access to:
- Sync profile selection
- Persistent state viewing
- Manual sync operations
- State reset capabilities

Access via the Settings button next to the sync button in Front of House page.

### Manual Sync Override

The sync button in the UI now uses `forceSync()` to ensure manual sync requests always execute, bypassing persistent initialization checks.

## Benefits

### Performance Improvements
- **Faster app startup**: Skips unnecessary syncs on page refresh
- **Reduced server load**: Fewer redundant sync requests
- **Better user experience**: Immediate app responsiveness

### Smart Sync Logic
- **Time-based**: Respects minimum intervals and TTL
- **Usage-based**: Forces sync after multiple page loads
- **Data-aware**: Syncs when no local data available
- **Context-aware**: Invalidates cache on festival changes

### Configurable Behavior
- **Environment-specific**: Different profiles for dev/prod
- **Device-adaptive**: Auto-detects low-power devices
- **User-controllable**: Manual override capabilities

## Technical Details

### Storage Keys
- `ithink_welfare_initialization_state`: Main persistent state
- `ithink_welfare_sync_config`: Extended sync configuration
- `ithink_welfare_sync_profile`: Current sync profile
- `ithink_welfare_session_id`: Session tracking (sessionStorage)

### State Structure
```typescript
interface InitializationState {
  isInitialized: boolean;
  lastSyncTime: number;
  lastSuccessfulSync: number;
  dataVersion: string;
  syncCount: number;
  hasLocalData: boolean;
  festivalId?: string;
  userAgent: string;
  sessionId: string;
}
```

### Error Handling
- Graceful degradation on localStorage errors
- Validation of stored state structure
- Fallback to default configuration
- Non-blocking error recovery

## Migration

The system is backward compatible and requires no migration. Existing sync behavior continues to work, with the persistent initialization system providing additional optimization.

## Testing

Run the test suite:
```bash
npm test -- persistent-initialization-manager.test.ts
```

The tests cover:
- Initial state management
- Sync lifecycle tracking
- Skip sync logic
- Configuration persistence
- State persistence
- Cache invalidation
- Festival ID handling

## Monitoring

The system provides detailed logging with `[PERSISTENT INIT]` prefix for debugging and monitoring sync behavior.

## Future Enhancements

Potential improvements:
- Sync analytics and metrics
- Network-aware sync scheduling
- Background sync optimization
- Cross-tab sync coordination
- Server-side sync hints
