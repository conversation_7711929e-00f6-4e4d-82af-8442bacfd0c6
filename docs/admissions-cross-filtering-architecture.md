# Admissions Reports Cross-Filtering System & Long Press Interaction Architecture

**Version:** 1.0  
**Date:** February 7, 2025  
**Status:** Design Specification  

## Executive Summary

This document outlines the comprehensive architectural design for transforming the admissions reports from isolated chart interactions to a coordinated cross-filtering system with dual interaction modes:

- **Click Interaction**: Applies filters across all charts with configurable intersection/union logic
- **Long Press Interaction (500ms+)**: Opens context-aware detailed modals specific to each chart type

## Table of Contents

1. [Current State Analysis](#current-state-analysis)
2. [Requirements & Objectives](#requirements--objectives)
3. [Proposed Architecture](#proposed-architecture)
4. [State Management Layer](#state-management-layer)
5. [Enhanced Chart Components](#enhanced-chart-components)
6. [Interaction System](#interaction-system)
7. [Filter UI Components](#filter-ui-components)
8. [Context-Aware Modal System](#context-aware-modal-system)
9. [Performance Optimization](#performance-optimization)
10. [Accessibility & UX](#accessibility--ux)
11. [Implementation Strategy](#implementation-strategy)
12. [Technical Dependencies](#technical-dependencies)
13. [Data Flow Architecture](#data-flow-architecture)

## Current State Analysis

### Existing Components

| Component | File | Current Functionality | Limitations |
|-----------|------|----------------------|-------------|
| **AdmissionsCharts** | `src/components/reports/admissions/AdmissionsCharts.tsx` | 3 isolated charts (Time of Day, Admissions by Reason, Age Distribution) | No cross-chart coordination |
| **useReportData** | `src/hooks/useReportData.ts` | Data fetching with festival/site filtering | No filter state management |
| **reportDataProcessing** | `src/utils/reportDataProcessing.ts` | Data transformation utilities | No cross-filtering logic |
| **useLongPress** | `src/hooks/useLongPress.ts` | 500ms threshold interaction hook | Not integrated with charts |
| **RecordDetailsModal** | `src/components/reports/shared/RecordDetailsModal.tsx` | Existing modal system | Not context-aware |

### Current Chart Structure

```typescript
// Current AdmissionsCharts.tsx structure
- Time of Day Chart (BarChart) - HAS click handlers
- Admissions by Reason Chart (BarChart) - NO click handlers  
- Age Distribution Chart (PieChart) - NO click handlers
```

### Identified Issues

1. **Isolation**: Charts operate independently with no coordination
2. **Inconsistent Interactions**: Only Time of Day chart has click handlers
3. **Modal Conflicts**: Current click-to-modal conflicts with desired filtering
4. **No Visual Feedback**: No indication of active filters or filter state
5. **Performance**: No optimization for large datasets with filtering

## Requirements & Objectives

### Functional Requirements

1. **Cross-Filtering System**
   - Click any chart element to apply filters across all charts
   - Support multiple simultaneous filters (time + reason + age)
   - Configurable filter mode: Intersection (AND) vs Union (OR)
   - Visual feedback for active filters

2. **Long Press Interactions**
   - 500ms threshold to distinguish from regular clicks
   - Context-aware modals based on chart type:
     - Time Chart → Hourly patient list
     - Reason Chart → Patients grouped by reason
     - Age Chart → Patients grouped by age

3. **User Interface**
   - Filter chips/badges showing active filters
   - Clear filter functionality
   - Filter mode toggle (AND/OR)
   - Responsive design for mobile/tablet

### Non-Functional Requirements

1. **Performance**: Handle large datasets (1000+ records) efficiently
2. **Accessibility**: WCAG 2.1 AA compliance
3. **Maintainability**: Clean, modular architecture
4. **Extensibility**: Easy to add new chart types or filter dimensions

## Proposed Architecture

### System Overview

```mermaid
graph TB
    A[AdmissionsCharts Container] --> B[Filter Control Panel]
    A --> C[Enhanced Chart Components]
    A --> D[Modal System]
    
    B --> B1[Filter Mode Toggle]
    B --> B2[Active Filter Chips]
    B --> B3[Clear All Button]
    
    C --> C1[Time of Day Chart]
    C --> C2[Admissions by Reason Chart]
    C --> C3[Age Distribution Chart]
    
    D --> D1[Time Detail Modal]
    D --> D2[Reason Detail Modal]
    D --> D3[Age Detail Modal]
    
    E[useAdmissionsFilters Hook] --> A
    E --> F[Filter State Management]
    E --> G[Data Processing]
    E --> H[Performance Optimization]
```

## State Management Layer

### Core Hook: `useAdmissionsFilters`

```typescript
interface FilterState {
  timeFilters: string[];      // ["10:00-11:00", "14:00-15:00"]
  reasonFilters: string[];    // ["Substance Use", "Mental Health"]
  ageFilters: string[];       // ["18-25", "26-35"]
  filterMode: 'AND' | 'OR';   // Intersection vs Union
}

interface FilterActions {
  addFilter: (type: FilterType, value: string) => void;
  removeFilter: (type: FilterType, value: string) => void;
  clearFilters: () => void;
  clearFilterType: (type: FilterType) => void;
  toggleFilterMode: () => void;
}

interface FilteredData {
  filteredAdmissions: AdmissionDocument[];
  timeData: TimeOfDayData[];
  reasonData: ChartData[];
  ageData: ChartData[];
  filterSummary: {
    totalRecords: number;
    filteredRecords: number;
    activeFilterCount: number;
  };
}

const useAdmissionsFilters = (
  rawData: AdmissionDocument[]
): [FilterState, FilterActions, FilteredData] => {
  // Implementation details
};
```

### State Flow Diagram

```mermaid
stateDiagram-v2
    [*] --> NoFilters
    NoFilters --> SingleFilter: Add Filter
    SingleFilter --> MultipleFilters: Add Filter
    SingleFilter --> NoFilters: Remove Filter
    MultipleFilters --> SingleFilter: Remove Filter
    MultipleFilters --> MultipleFilters: Add/Remove Filter
    MultipleFilters --> NoFilters: Clear All
    
    SingleFilter --> SingleFilter: Toggle Mode
    MultipleFilters --> MultipleFilters: Toggle Mode
```

## Enhanced Chart Components

### Chart Enhancement Pattern

```typescript
interface EnhancedChartProps {
  data: ChartData[];
  onFilterSelect: (filterType: FilterType, value: string) => void;
  onLongPress: (filterType: FilterType, value: string, data: any[]) => void;
  activeFilters: string[];
  isFiltered: boolean;
  filterMode: 'AND' | 'OR';
}

interface ChartInteractionConfig {
  filterType: FilterType;
  threshold: number; // 500ms for long press
  enableClick: boolean;
  enableLongPress: boolean;
}
```

### Chart Component Architecture

```mermaid
graph LR
    A[Chart Component] --> B[Enhanced Recharts Wrapper]
    B --> C[useLongPress Hook]
    B --> D[Click Handler]
    B --> E[Data Filtering]
    B --> F[Visual Feedback]
    
    C --> G[Long Press: Open Modal]
    D --> H[Click: Apply Filter]
    E --> I[Filter Data Updates]
    F --> J[Highlight Active Filters]
```

### Implementation Pattern

```typescript
const EnhancedBarChart: React.FC<EnhancedChartProps> = ({
  data,
  onFilterSelect,
  onLongPress,
  activeFilters,
  filterType
}) => {
  const chartInteraction = useChartInteraction({
    onFilter: (value) => onFilterSelect(filterType, value),
    onDetail: (value) => onLongPress(filterType, value, getDetailData(value)),
    threshold: 500
  });

  return (
    <ResponsiveContainer>
      <BarChart data={data}>
        <Bar dataKey="count" onClick={chartInteraction.handleClick}>
          {data.map((entry, index) => (
            <Cell 
              key={`cell-${index}`}
              fill={getBarColor(entry, activeFilters)}
              {...chartInteraction.getProps(entry.key)}
            />
          ))}
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  );
};
```

## Interaction System

### Dual Interaction Handler

```typescript
interface ChartInteractionHook {
  handleClick: (event: any) => void;
  getProps: (value: string) => object;
  isLongPressing: boolean;
}

const useChartInteraction = (config: {
  onFilter: (value: string) => void;
  onDetail: (value: string, data: any[]) => void;
  threshold: number;
}): ChartInteractionHook => {
  const [isLongPressing, setIsLongPressing] = useState(false);
  
  const longPress = useLongPress({
    onLongPress: (value) => {
      setIsLongPressing(false);
      config.onDetail(value, getDetailData(value));
    },
    onClick: (value) => {
      if (!isLongPressing) {
        config.onFilter(value);
      }
    },
    threshold: config.threshold
  });
  
  return {
    handleClick: longPress.onClick,
    getProps: longPress.getProps,
    isLongPressing
  };
};
```

### Interaction Flow

```mermaid
sequenceDiagram
    participant U as User
    participant C as Chart Component
    participant I as Interaction Handler
    participant F as Filter System
    participant M as Modal System
    
    U->>C: Mouse Down / Touch Start
    C->>I: Start Interaction Timer
    
    alt Short Press (< 500ms)
        U->>C: Mouse Up / Touch End
        I->>F: Apply Filter
        F->>C: Update All Charts
    else Long Press (>= 500ms)
        I->>M: Open Context Modal
        M->>U: Display Detail View
    end
```

## Filter UI Components

### Filter Control Panel Architecture

```mermaid
graph TB
    A[Filter Control Panel] --> B[Filter Mode Toggle]
    A --> C[Active Filter Chips]
    A --> D[Clear All Button]
    A --> E[Filter Summary]
    
    B --> B1[AND Mode Icon & Label]
    B --> B2[OR Mode Icon & Label]
    B --> B3[Toggle Animation]
    
    C --> C1[Time Filter Chips]
    C --> C2[Reason Filter Chips]
    C --> C3[Age Filter Chips]
    
    C1 --> C4[Individual Remove Buttons]
    C2 --> C5[Individual Remove Buttons]
    C3 --> C6[Individual Remove Buttons]
    
    E --> E1[Record Count Display]
    E --> E2[Filter Status Indicator]
```

### Component Specifications

#### FilterModeToggle Component
```typescript
interface FilterModeToggleProps {
  mode: 'AND' | 'OR';
  onToggle: () => void;
  disabled?: boolean;
}

// Visual Design:
// AND Mode: Intersection icon (⋂) + "Match All Filters"
// OR Mode: Union icon (⋃) + "Match Any Filter"
```

#### FilterChip Component
```typescript
interface FilterChipProps {
  label: string;
  filterType: FilterType;
  onRemove: () => void;
  color?: 'primary' | 'secondary' | 'default';
}

// Visual Design:
// - Material-UI Chip with delete icon
// - Color-coded by filter type (time=blue, reason=green, age=orange)
// - Hover effects and animations
```

#### FilterSummary Component
```typescript
interface FilterSummaryProps {
  totalRecords: number;
  filteredRecords: number;
  activeFilterCount: number;
  filterMode: 'AND' | 'OR';
}

// Display Format:
// "Showing 45 of 120 records (3 filters active, AND mode)"
```

## Context-Aware Modal System

### Modal Router Architecture

```mermaid
graph TB
    A[Long Press Event] --> B[Modal Router]
    B --> C{Chart Type}
    
    C -->|Time Chart| D[TimeDetailModal]
    C -->|Reason Chart| E[ReasonDetailModal]
    C -->|Age Chart| F[AgeDetailModal]
    
    D --> D1[Hourly Patient List]
    D --> D2[Time-based Statistics]
    D --> D3[Timeline Visualization]
    
    E --> E1[Reason-grouped Patients]
    E --> E2[Severity Indicators]
    E --> E3[Reason Analysis Charts]
    
    F --> F1[Age-grouped Patients]
    F --> F2[Age Demographics]
    F --> F3[Age Distribution Analysis]
```

### Modal Content Specifications

#### TimeDetailModal
```typescript
interface TimeDetailModalProps {
  timeSlot: string; // "10:00-11:00"
  patients: AdmissionDocument[];
  onClose: () => void;
}

// Content Structure:
// - Header: Time slot and patient count
// - Patient list with admission details
// - Timeline visualization
// - Export functionality
```

#### ReasonDetailModal
```typescript
interface ReasonDetailModalProps {
  reason: string; // "Substance Use"
  patients: AdmissionDocument[];
  onClose: () => void;
}

// Content Structure:
// - Header: Reason and patient count
// - Grouped patient list by severity
// - Reason-specific statistics
// - Related reasons analysis
```

#### AgeDetailModal
```typescript
interface AgeDetailModalProps {
  ageGroup: string; // "18-25"
  patients: AdmissionDocument[];
  onClose: () => void;
}

// Content Structure:
// - Header: Age group and patient count
// - Patient demographics
// - Age-related statistics
// - Comparative analysis
```

## Performance Optimization

### Data Processing Strategy

```typescript
const useOptimizedFiltering = (rawData: AdmissionDocument[]) => {
  // Memoized base transformations
  const baseChartData = useMemo(() => ({
    timeData: getAdmissionsByTimeOfDay(rawData),
    reasonData: getAdmissionsByReason(rawData),
    ageData: getAgeDistribution(rawData)
  }), [rawData]);
  
  // Filtered data with intersection/union logic
  const filteredData = useMemo(() => 
    applyFilters(baseChartData, activeFilters, filterMode),
    [baseChartData, activeFilters, filterMode]
  );
  
  // Performance metrics
  const performanceMetrics = useMemo(() => ({
    processingTime: Date.now() - startTime,
    recordCount: filteredData.length,
    filterComplexity: calculateFilterComplexity(activeFilters)
  }), [filteredData, activeFilters]);
  
  return { filteredData, performanceMetrics };
};
```

### Optimization Techniques

1. **Memoization Strategy**
   - Cache expensive data transformations
   - Memoize filter results by filter signature
   - Use React.memo for chart components

2. **Debouncing & Throttling**
   - Debounce rapid filter updates (300ms)
   - Throttle chart re-renders during interactions
   - Batch state updates for multiple filters

3. **Virtual Scrolling**
   - Implement for large patient lists in modals
   - Use react-window for performance
   - Lazy load modal content

4. **Data Indexing**
   - Pre-index data by common filter dimensions
   - Use Map/Set for O(1) lookups
   - Cache filter results in memory

### Performance Monitoring

```typescript
interface PerformanceMetrics {
  filterProcessingTime: number;
  chartRenderTime: number;
  modalLoadTime: number;
  memoryUsage: number;
}

const usePerformanceMonitoring = () => {
  // Implementation for tracking performance metrics
  // Integration with browser performance APIs
  // Alerts for performance degradation
};
```

## Accessibility & UX

### Accessibility Features

1. **Keyboard Navigation**
   - Tab through chart elements
   - Arrow keys for chart navigation
   - Enter/Space for activation
   - Escape to close modals

2. **Screen Reader Support**
   - ARIA labels for all interactive elements
   - Live regions for filter updates
   - Descriptive chart data announcements
   - Modal focus management

3. **Visual Accessibility**
   - High contrast mode support
   - Color-blind friendly palettes
   - Focus indicators
   - Reduced motion options

### UX Enhancements

1. **Loading States**
   - Skeleton screens during data processing
   - Progress indicators for large datasets
   - Smooth transitions between states

2. **Error Handling**
   - Graceful degradation for filter failures
   - User-friendly error messages
   - Retry mechanisms

3. **Responsive Design**
   - Mobile-optimized touch interactions
   - Tablet-friendly layouts
   - Adaptive chart sizing

4. **Animation & Feedback**
   - Smooth filter transitions
   - Visual feedback for interactions
   - Micro-animations for state changes

### Accessibility Implementation

```typescript
const AccessibleChartElement = ({ data, onInteraction }) => {
  return (
    <div
      role="button"
      tabIndex={0}
      aria-label={`${data.label}: ${data.count} admissions`}
      aria-describedby="chart-instructions"
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          onInteraction(data);
        }
      }}
      onClick={() => onInteraction(data)}
    >
      {/* Chart element content */}
    </div>
  );
};
```

## Implementation Strategy

### Phase 1: Core Infrastructure (Week 1-2)
**Objective**: Establish foundation for filtering system

**Tasks**:
1. Create `useAdmissionsFilters` hook
2. Implement filter state management
3. Build basic filter UI components
4. Add TypeScript interfaces

**Deliverables**:
- `hooks/useAdmissionsFilters.ts`
- `components/filters/FilterControlPanel.tsx`
- `components/filters/FilterChip.tsx`
- `components/filters/FilterModeToggle.tsx`
- `types/filters.ts`

**Success Criteria**:
- Filter state management working
- Basic UI components functional
- TypeScript compilation successful

### Phase 2: Chart Enhancement (Week 3-4)
**Objective**: Enhance charts with dual interactions

**Tasks**:
1. Enhance existing chart components
2. Implement dual interaction system
3. Add visual feedback for filters
4. Integrate with filter state

**Deliverables**:
- Enhanced `AdmissionsCharts.tsx`
- `hooks/useChartInteraction.ts`
- `utils/chartInteractionUtils.ts`
- Visual filter indicators

**Success Criteria**:
- Click interactions apply filters
- Long press detection working
- Visual feedback functional
- Cross-chart coordination active

### Phase 3: Modal System (Week 5-6)
**Objective**: Implement context-aware modals

**Tasks**:
1. Create context-aware modal components
2. Implement long press modal triggers
3. Add detailed data views
4. Integrate with existing modal system

**Deliverables**:
- `components/modals/TimeDetailModal.tsx`
- `components/modals/ReasonDetailModal.tsx`
- `components/modals/AgeDetailModal.tsx`
- `utils/modalContentUtils.ts`

**Success Criteria**:
- Long press opens appropriate modals
- Context-aware content displayed
- Modal navigation functional
- Data accuracy verified

### Phase 4: Polish & Testing (Week 7-8)
**Objective**: Optimize performance and ensure quality

**Tasks**:
1. Performance optimization
2. Accessibility improvements
3. Comprehensive testing
4. User experience refinements

**Deliverables**:
- Performance optimizations
- Accessibility compliance
- Test suite
- Documentation updates

**Success Criteria**:
- Performance benchmarks met
- WCAG 2.1 AA compliance
- All tests passing
- User acceptance criteria met

## Technical Dependencies

### Required Updates

1. **Recharts Enhancement**
   - Enhanced event handlers for dual interactions
   - Custom cell components for visual feedback
   - Performance optimizations for large datasets

2. **Material-UI Components**
   - Filter chips with custom styling
   - Toggle components for filter mode
   - Modal enhancements for context-aware content

3. **React Hooks**
   - Custom hooks for state management
   - Performance optimization hooks
   - Interaction handling hooks

4. **TypeScript Definitions**
   - Extended type definitions for filter system
   - Chart interaction interfaces
   - Modal content type definitions

### New Utility Files

1. **`utils/filterUtils.ts`**
   ```typescript
   // Filter logic and data processing
   export const applyFilters = (data, filters, mode) => { /* */ };
   export const combineFilters = (filters, mode) => { /* */ };
   export const validateFilterState = (state) => { /* */ };
   ```

2. **`utils/chartInteractionUtils.ts`**
   ```typescript
   // Interaction handling utilities
   export const createInteractionHandler = (config) => { /* */ };
   export const getDetailData = (filterType, value, data) => { /* */ };
   export const calculateChartColors = (data, activeFilters) => { /* */ };
   ```

3. **`utils/modalContentUtils.ts`**
   ```typescript
   // Context-aware modal content generation
   export const generateTimeModalContent = (timeSlot, patients) => { /* */ };
   export const generateReasonModalContent = (reason, patients) => { /* */ };
   export const generateAgeModalContent = (ageGroup, patients) => { /* */ };
   ```

### Package Dependencies

No new external dependencies required. The implementation leverages existing packages:

- **React 19.1.0**: Core framework
- **@mui/material 7.0.2**: UI components
- **recharts 2.15.3**: Chart library
- **date-fns 4.1.0**: Date utilities
- **lodash 4.17.21**: Utility functions
- **TypeScript 5.8.3**: Type safety

## Data Flow Architecture

### Complete Data Flow Sequence

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Filter UI
    participant H as useAdmissionsFilters
    participant C as Chart Components
    participant M as Modal System
    participant D as Data Layer
    
    Note over U,D: Initial Load
    U->>H: Component Mount
    H->>D: Fetch Raw Data
    D->>H: Return Admission Data
    H->>C: Provide Processed Data
    C->>U: Render Charts
    
    Note over U,D: Filter Application
    U->>C: Click Chart Element
    C->>H: Add Filter
    H->>H: Update Filter State
    H->>D: Process Filtered Data
    D->>H: Return Filtered Results
    H->>C: Update All Charts
    H->>UI: Update Filter UI
    UI->>U: Show Filter Chips
    
    Note over U,D: Long Press Interaction
    U->>C: Long Press Chart Element
    C->>M: Open Context Modal
    M->>D: Request Detail Data
    D->>M: Return Detail Data
    M->>U: Display Modal
    
    Note over U,D: Filter Management
    U->>UI: Toggle Filter Mode
    UI->>H: Update Filter Mode
    H->>D: Reprocess Data
    D->>H: Return Updated Results
    H->>C: Update Charts
    
    U->>UI: Remove Filter Chip
    UI->>H: Remove Filter
    H->>D: Reprocess Data
    D->>H: Return Updated Results
    H->>C: Update Charts
    H->>UI: Update Filter UI
```

### State Management Flow

```mermaid
graph TD
    A[Raw Admission Data] --> B[useAdmissionsFilters Hook]
    B --> C[Filter State]
    B --> D[Processed Data]
    
    C --> C1[Active Filters]
    C --> C2[Filter Mode AND/OR]
    C --> C3[Filter History]
    
    D --> D1[Filtered Admissions]
    D --> D2[Time Chart Data]
    D --> D3[Reason Chart Data]
    D --> D4[Age Chart Data]
    D --> D5[Filter Summary]
    
    E[User Interactions] --> F[Filter Actions]
    F --> F1[Add Filter]
    F --> F2[Remove Filter]
    F --> F3[Clear Filters]
    F --> F4[Toggle Mode]
    
    F1 --> C
    F2 --> C
    F3 --> C
    F4 --> C
    
    C --> G[Data Reprocessing]
    G --> D
    
    D --> H[Chart Updates]
    D --> I[UI Updates]
```

## Conclusion

This architectural specification provides a comprehensive blueprint for implementing a sophisticated cross-filtering system with dual interaction modes for the admissions reports. The design prioritizes:

- **User Experience**: Intuitive interactions with clear visual feedback
- **Performance**: Optimized for large datasets with efficient state management
- **Accessibility**: Full WCAG 2.1 AA compliance
- **Maintainability**: Clean, modular architecture with TypeScript safety
- **Extensibility**: Easy to extend with new chart types or filter dimensions

The phased implementation approach ensures manageable development cycles while delivering incremental value. The architecture leverages existing technologies and patterns within the codebase, minimizing risk and complexity.

---

**Next Steps**: This specification should be reviewed by the development team and stakeholders before proceeding to implementation. Any feedback or modifications should be incorporated before beginning Phase 1 development.