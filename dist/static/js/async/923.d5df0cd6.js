"use strict";(self.webpackChunkithinc_welfare=self.webpackChunkithinc_welfare||[]).push([["923"],{33327:function(e,s,l){l.r(s),l.d(s,{default:()=>q});var a=l(85893),n=l(67294),r=l(33991),i=l(54757),c=l(89126),d=l(88957),t=l(46560),o=l(61215),h=l(7230),x=l(95438),j=l(21183),u=l(52104),Z=l(98913),m=l(60583),g=l(60187),p=l(38953),b=l(64889),v=l(30925),f=l(81839),R=l(73892),y=l(1156),C=l(48346),S=l(73876),A=l(56099),P=l(13319),w=l(17616),U=l(39467),E=l(14540),k=l(97454),K=l(83502),I=l(3065),N=l(37231);let D=e=>{let{children:s,value:l,index:n,...i}=e;return(0,a.jsx)("div",{role:"tabpanel",hidden:l!==n,id:`access-tabpanel-${n}`,"aria-labelledby":`access-tab-${n}`,...i,children:l===n&&(0,a.jsx)(r.Z,{sx:{p:3},children:s})})},M=[{id:"dashboard",name:"Dashboard"},{id:"admissions",name:"Admissions"},{id:"new-admission",name:"New Admission"},{id:"front-of-house",name:"Front of House"},{id:"lost-property",name:"Lost Property"},{id:"sensory-hub",name:"Sensory Hub"},{id:"reports",name:"Reports"},{id:"shifts",name:"Shifts"},{id:"knowledge-base-view",name:"Knowledge Base (View)"},{id:"knowledge-base-edit",name:"Knowledge Base (Edit)"},{id:"festival-management",name:"Festival Management"},{id:"feedback-management",name:"Feedback Management"},{id:"access-management",name:"Access Management"}],q=()=>{let{isAdmin:e}=(0,N.a)(),[s,l]=(0,n.useState)(0),[q,B]=(0,n.useState)([]),[W,F]=(0,n.useState)([]),[H,L]=(0,n.useState)(!0),[T,$]=(0,n.useState)(!1),[_,V]=(0,n.useState)(!1),[Y,z]=(0,n.useState)(null),[G,J]=(0,n.useState)(null),[O,Q]=(0,n.useState)(""),[X,ee]=(0,n.useState)(""),[es,el]=(0,n.useState)(I.K.USER),[ea,en]=(0,n.useState)(""),[er,ei]=(0,n.useState)(I.K.USER),[ec,ed]=(0,n.useState)(!1),[et,eo]=(0,n.useState)(""),[eh,ex]=(0,n.useState)("success");(0,n.useEffect)(()=>{ej()},[]);let ej=async()=>{L(!0);try{let e=await K.R.getAllPageAccess(),s=await K.R.getAllUserRoles();B(e),F(s)}catch(e){ef("Error loading data","error")}finally{L(!1)}},eu=e=>{e?(z(e),Q(e.pageId),ee(e.pageName),el(e.requiredRole)):(z(null),Q(""),ee(""),el(I.K.USER)),$(!0)},eZ=e=>{e?(J(e),en(e.email),ei(e.role)):(J(null),en(""),ei(I.K.USER)),V(!0)},em=()=>{$(!1)},eg=()=>{V(!1)},ep=async()=>{try{await K.R.setPageAccess(O,X,es),em(),ef("Page access saved successfully","success"),ej()}catch(e){ef("Error saving page access","error")}},eb=async()=>{try{await K.R.setUserRole(ea,er),eg(),ef("User role saved successfully","success"),ej()}catch(e){ef("Error saving user role","error")}},ev=async e=>{try{await K.R.deleteUserRole(e),ef("User role deleted successfully","success"),ej()}catch(e){ef("Error deleting user role","error")}},ef=(e,s)=>{eo(e),ex(s),ed(!0)},eR=()=>{ed(!1)};return e?(0,a.jsxs)(r.Z,{sx:{p:3},children:[(0,a.jsx)(i.Z,{variant:"h4",gutterBottom:!0,children:"Access Management"}),(0,a.jsxs)(c.Z,{sx:{width:"100%",mb:2},children:[(0,a.jsxs)(d.Z,{value:s,onChange:(e,s)=>{l(s)},indicatorColor:"primary",textColor:"primary",centered:!0,children:[(0,a.jsx)(t.Z,{label:"Page Access"}),(0,a.jsx)(t.Z,{label:"User Roles"})]}),H?(0,a.jsx)(r.Z,{sx:{display:"flex",justifyContent:"center",p:3},children:(0,a.jsx)(o.Z,{})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(D,{value:s,index:0,children:[(0,a.jsx)(r.Z,{sx:{mb:2,display:"flex",justifyContent:"flex-end"},children:(0,a.jsx)(h.Z,{variant:"contained",color:"primary",onClick:()=>eu(),children:"Add Page Access"})}),(0,a.jsx)(x.Z,{children:(0,a.jsxs)(j.Z,{children:[(0,a.jsx)(u.Z,{children:(0,a.jsxs)(Z.Z,{children:[(0,a.jsx)(m.Z,{children:"Page ID"}),(0,a.jsx)(m.Z,{children:"Page Name"}),(0,a.jsx)(m.Z,{children:"Required Role"}),(0,a.jsx)(m.Z,{children:"Actions"})]})}),(0,a.jsxs)(g.Z,{children:[q.map(e=>(0,a.jsxs)(Z.Z,{children:[(0,a.jsx)(m.Z,{children:e.pageId}),(0,a.jsx)(m.Z,{children:e.pageName}),(0,a.jsx)(m.Z,{children:e.requiredRole}),(0,a.jsx)(m.Z,{children:(0,a.jsx)(p.Z,{color:"primary",onClick:()=>eu(e),children:(0,a.jsx)(E.Z,{})})})]},e.pageId)),0===q.length&&(0,a.jsx)(Z.Z,{children:(0,a.jsx)(m.Z,{colSpan:4,align:"center",children:"No page access settings found"})})]})]})})]}),(0,a.jsxs)(D,{value:s,index:1,children:[(0,a.jsx)(r.Z,{sx:{mb:2,display:"flex",justifyContent:"flex-end"},children:(0,a.jsx)(h.Z,{variant:"contained",color:"primary",onClick:()=>eZ(),children:"Add User Role"})}),(0,a.jsx)(x.Z,{children:(0,a.jsxs)(j.Z,{children:[(0,a.jsx)(u.Z,{children:(0,a.jsxs)(Z.Z,{children:[(0,a.jsx)(m.Z,{children:"Email"}),(0,a.jsx)(m.Z,{children:"Role"}),(0,a.jsx)(m.Z,{children:"Actions"})]})}),(0,a.jsxs)(g.Z,{children:[W.map(e=>(0,a.jsxs)(Z.Z,{children:[(0,a.jsx)(m.Z,{children:e.email}),(0,a.jsx)(m.Z,{children:e.role}),(0,a.jsxs)(m.Z,{children:[(0,a.jsx)(p.Z,{color:"primary",onClick:()=>eZ(e),children:(0,a.jsx)(E.Z,{})}),(0,a.jsx)(p.Z,{color:"error",onClick:()=>ev(e.email),children:(0,a.jsx)(k.Z,{})})]})]},e.email)),0===W.length&&(0,a.jsx)(Z.Z,{children:(0,a.jsx)(m.Z,{colSpan:3,align:"center",children:"No user roles found"})})]})]})})]})]})]}),(0,a.jsxs)(b.Z,{open:T,onClose:em,children:[(0,a.jsx)(v.Z,{children:Y?"Edit Page Access":"Add Page Access"}),(0,a.jsxs)(f.Z,{children:[(0,a.jsxs)(R.Z,{fullWidth:!0,margin:"normal",disabled:!!Y,children:[(0,a.jsx)(y.Z,{id:"page-select-label",children:"Page"}),(0,a.jsx)(C.Z,{labelId:"page-select-label",value:O,onChange:e=>{let s=e.target.value,l=M.find(e=>e.id===s);l&&(Q(l.id),ee(l.name))},label:"Page",children:M.map(e=>(0,a.jsx)(S.Z,{value:e.id,children:e.name},e.id))})]}),(0,a.jsx)(A.Z,{margin:"normal",label:"Page Name",fullWidth:!0,value:X,onChange:e=>ee(e.target.value),disabled:!!Y}),(0,a.jsxs)(R.Z,{fullWidth:!0,margin:"normal",children:[(0,a.jsx)(y.Z,{id:"role-select-label",children:"Required Role"}),(0,a.jsxs)(C.Z,{labelId:"role-select-label",value:es,onChange:e=>el(e.target.value),label:"Required Role",children:[(0,a.jsx)(S.Z,{value:I.K.ADMIN,children:"Admin"}),(0,a.jsx)(S.Z,{value:I.K.PARTNER,children:"Partner"}),(0,a.jsx)(S.Z,{value:I.K.USER,children:"User"}),(0,a.jsx)(S.Z,{value:I.K.PUBLIC,children:"Public"})]})]})]}),(0,a.jsxs)(P.Z,{children:[(0,a.jsx)(h.Z,{onClick:em,children:"Cancel"}),(0,a.jsx)(h.Z,{onClick:ep,color:"primary",children:"Save"})]})]}),(0,a.jsxs)(b.Z,{open:_,onClose:eg,children:[(0,a.jsx)(v.Z,{children:G?"Edit User Role":"Add User Role"}),(0,a.jsxs)(f.Z,{children:[(0,a.jsx)(A.Z,{margin:"normal",label:"Email",fullWidth:!0,value:ea,onChange:e=>en(e.target.value),disabled:!!G}),(0,a.jsxs)(R.Z,{fullWidth:!0,margin:"normal",children:[(0,a.jsx)(y.Z,{id:"user-role-select-label",children:"Role"}),(0,a.jsxs)(C.Z,{labelId:"user-role-select-label",value:er,onChange:e=>ei(e.target.value),label:"Role",children:[(0,a.jsx)(S.Z,{value:I.K.ADMIN,children:"Admin"}),(0,a.jsx)(S.Z,{value:I.K.PARTNER,children:"Partner"}),(0,a.jsx)(S.Z,{value:I.K.USER,children:"User"}),(0,a.jsx)(S.Z,{value:I.K.PUBLIC,children:"Public"})]})]})]}),(0,a.jsxs)(P.Z,{children:[(0,a.jsx)(h.Z,{onClick:eg,children:"Cancel"}),(0,a.jsx)(h.Z,{onClick:eb,color:"primary",children:"Save"})]})]}),(0,a.jsx)(w.Z,{open:ec,autoHideDuration:6e3,onClose:eR,children:(0,a.jsx)(U.Z,{onClose:eR,severity:eh,sx:{width:"100%"},children:et})})]}):(0,a.jsxs)(r.Z,{sx:{p:3},children:[(0,a.jsx)(i.Z,{variant:"h4",children:"Access Denied"}),(0,a.jsx)(i.Z,{variant:"body1",children:"You do not have permission to access this page."})]})}}}]);