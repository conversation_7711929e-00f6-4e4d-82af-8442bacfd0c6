"use strict";(self.webpackChunkithinc_welfare=self.webpackChunkithinc_welfare||[]).push([["675"],{44090:function(e,t,n){n.d(t,{T:()=>l});var r=n(67294);let l=e=>{let{onLongPress:t,onClick:n,threshold:l=500,captureEvent:s=!0}=e,i=(0,r.useRef)(null),a=(0,r.useRef)(!1),o=(0,r.useRef)(null),[c,u]=(0,r.useState)(!1),d=(0,r.useCallback)(()=>{i.current&&(clearTimeout(i.current),i.current=null),u(!1),a.current=!1,o.current=null},[]),m=(0,r.useCallback)(e=>{d(),o.current={x:"touches"in e?e.touches[0]?.clientX:e.clientX,y:"touches"in e?e.touches[0]?.clientY:e.clientY},s&&e.preventDefault(),u(!0),i.current=window.setTimeout(()=>{a.current=!0,u(!1),t(e)},l)},[t,l,s,d]),x=(0,r.useCallback)(function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],r=a.current;d(),t&&!r&&n&&n(e)},[n,d]),h=(0,r.useCallback)(e=>{if(!o.current||!i.current)return;let t="touches"in e?e.touches[0]?.clientX:e.clientX,n="touches"in e?e.touches[0]?.clientY:e.clientY,r=Math.abs(t-o.current.x),l=Math.abs(n-o.current.y);(r>10||l>10)&&d()},[d,10]),p=(0,r.useCallback)(e=>{m(e)},[m]),b=(0,r.useCallback)(e=>{x(e,!0)},[x]),g=(0,r.useCallback)(e=>{x(e,!1)},[x]);(0,r.useCallback)(e=>{h(e)},[h]);let f=(0,r.useCallback)(e=>{m(e)},[m]),y=(0,r.useCallback)(e=>{x(e,!0)},[x]),Z=(0,r.useCallback)(e=>{h(e)},[h]);return(0,r.useEffect)(()=>()=>{d()},[d]),{onMouseDown:p,onMouseUp:b,onMouseLeave:g,onTouchStart:f,onTouchEnd:y,onTouchMove:Z,isLongPressing:c}}},92226:function(e,t,n){n.r(t),n.d(t,{FrontOfHousePage:()=>B});var r=n(85893),l=n(67294),s=n(83502),i=n(5214),a=n(59326),o=n(60630),c=n(69326),u=n(64889),d=n(30925),m=n(81839),x=n(33991),h=n(54757),p=n(56099),b=n(13319),g=n(7230);let f=e=>{let{open:t,onClose:n,onConfirm:s,itemName:i,label:a}=e,[o,c]=(0,l.useState)("1"),[f,y]=(0,l.useState)(1),[Z,j]=(0,l.useState)(null);(0,l.useEffect)(()=>{t&&(c("1"),y(1),j(null))},[t]);let C=()=>{if(""===o)return void j("Please enter a valid number");let e=parseInt(o,10);!Z&&e>0&&(s(e),n())};return(0,r.jsxs)(u.Z,{open:t,onClose:n,PaperProps:{sx:{width:"90%",maxWidth:"400px",borderRadius:2}},children:[(0,r.jsxs)(d.Z,{sx:{pb:1,color:"primary.main"},children:["Add ",a," Items"]}),(0,r.jsxs)(m.Z,{children:[(0,r.jsx)(x.Z,{sx:{mb:2},children:(0,r.jsxs)(h.Z,{variant:"body2",color:"text.secondary",children:["Enter the number of ",a," items to add:"]})}),(0,r.jsx)(p.Z,{autoFocus:!0,margin:"dense",label:"Quantity",type:"number",fullWidth:!0,variant:"outlined",value:o,onChange:e=>{let t=e.target.value;if(c(t),""===t)return void j(null);let n=parseInt(t,10);isNaN(n)?j("Please enter a valid number"):n<1?j("Please enter a number greater than 0"):n>100?j("Maximum quantity is 100"):(j(null),y(n))},onKeyDown:e=>{"Enter"===e.key&&(e.preventDefault(),C())},error:!!Z,helperText:Z||"Enter the number of items to add",inputProps:{inputMode:"numeric",pattern:"[0-9]*",sx:{fontSize:"1.2rem",padding:"12px"}},sx:{"& input[type=number]::-webkit-inner-spin-button, & input[type=number]::-webkit-outer-spin-button":{"-webkit-appearance":"none",margin:0},"& input[type=number]":{"-moz-appearance":"textfield"}}})]}),(0,r.jsxs)(b.Z,{sx:{p:2,pt:1},children:[(0,r.jsx)(g.Z,{onClick:n,color:"primary",variant:"outlined",sx:{mr:1,px:3,py:1},children:"Cancel"}),(0,r.jsx)(g.Z,{onClick:C,color:"primary",variant:"contained",disabled:!!Z,sx:{px:3,py:1},children:"Add Items"})]})]})};var y=n(44090),Z=n(32153),j=n(13400),C=n(61215),k=n(17047);let w=e=>{let{itemName:t,label:n,count:l,isUpdating:s,onItemClick:i,onLongPress:a,IconComponent:o}=e,c=(0,y.T)({onClick:()=>i(t),onLongPress:()=>a(t,n),threshold:500});return(0,r.jsx)(Z.Z,{...c,sx:{width:180,height:180,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",cursor:"pointer",opacity:s?.5:1,transition:"all 0.2s","&:hover":{boxShadow:3},userSelect:"none",touchAction:"none"},children:(0,r.jsxs)(j.Z,{spacing:2,alignItems:"center",children:[(0,r.jsxs)(x.Z,{sx:{width:80,height:80,display:"flex",alignItems:"center",justifyContent:"center",border:1,borderColor:"grey.200",borderRadius:1,bgcolor:"white",position:"relative"},children:[(0,r.jsx)(o,{sx:{fontSize:48,color:"#662D91"}}),s&&(0,r.jsx)(C.Z,{size:24,sx:{position:"absolute",top:-12,right:-12,color:"primary.main"}})]}),(0,r.jsxs)(j.Z,{alignItems:"center",spacing:1,children:[(0,r.jsx)(h.Z,{variant:"body2",sx:{fontWeight:500,textAlign:"center",whiteSpace:"pre-line",lineHeight:"tight"},children:n}),(0,r.jsx)(k.Z,{label:l,sx:{bgcolor:"purple.100",color:"purple.600",fontWeight:"bold",fontSize:"0.875rem"}})]})]})})};var S=n(99371),v=n(89707),z=n(54384),I=n(8242),R=n(63839),W=n(57526),P=n(59224),D=n(24665),E=n(40697),F=n(43570),N=n(42137),H=n(63659),M=n(24472),U=n(39776),A=n(12550),T=n(92291),_=n(64286),G=n(58308),L=n(98106),O=n(60488),X=n(39467),Y=n(89126),q=n(23279),$=n.n(q);let B=()=>{let{activeFestival:e}=(0,i.C)(),{activeSiteLocation:t}=(0,a.C)(),[n,u]=(0,l.useState)(!0),[d,m]=(0,l.useState)([]),[p,b]=(0,l.useState)(null),[y,Z]=(0,l.useState)(null),[j,q]=(0,l.useState)(!1),[B,K]=(0,l.useState)(!1),[Q,J]=(0,l.useState)(""),[V,ee]=(0,l.useState)(!1),[et,en]=(0,l.useState)(null);(0,l.useEffect)(()=>{let e=async()=>{try{let e=await s.R.hasPendingChanges();K(e)}catch(e){}},t=s.R.addSyncListener(()=>{e()});return e(),()=>{try{t()}catch(e){}}},[]);let er=(0,l.useCallback)(async()=>{if(!e){m([]),u(!1);return}try{u(!0),Z(null);let n=await s.R.getItemCountsByFestival(e._id),r=t?n.filter(e=>e.siteLocationId===t.id):n;m(r)}catch(e){Z("Failed to load counts")}finally{u(!1)}},[e,t]),el=(0,l.useCallback)(async()=>{if(!j)try{q(!0),Z(null),J("syncing"),await s.R.manualSync(),await er(),J("synced")}catch(e){Z("Failed to sync with server"),J("error")}finally{q(!1)}},[j,er]),es=(0,l.useCallback)($()(async function(n){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;if(e)try{await s.R.addOrUpdateItemCount(n,e._id,t?.id,r),await er()}catch(e){Z("Failed to update count")}finally{b(null)}},300),[e,t,er]),ei=(0,l.useCallback)(t=>{e&&p!==t&&(b(t),Z(null),es(t,1))},[e,p,es]),ea=(0,l.useCallback)((e,t)=>{en({name:e,label:t}),ee(!0)},[]),eo=(0,l.useCallback)(e=>{et&&e>0&&(b(et.name),Z(null),es(et.name,e))},[et,es]);(0,l.useEffect)(()=>{er()},[er]);let ec=(0,l.useCallback)(e=>{let t=d.find(e=>{if(!e.timestamp)return!1;let t=new Date(e.timestamp),n=new Date;return t.toDateString()===n.toDateString()});return t&&t[e]||0},[d]),eu=(0,l.useCallback)((e,t,n)=>(0,r.jsx)(w,{itemName:e,label:n,count:ec(e),isUpdating:p===e,onItemClick:ei,onLongPress:ea,IconComponent:t}),[p,ei,ec,ea]),ed=[{field:"item",headerName:"Item",flex:1},{field:"count",headerName:"Count",width:120,renderCell:e=>(0,r.jsx)(k.Z,{label:e.row.count,sx:{bgcolor:"purple.100",color:"purple.600",fontWeight:"bold",fontSize:"0.875rem"}})},{field:"lastUpdated",headerName:"Last Updated",flex:1,renderCell:e=>e.row.lastUpdated?(0,o.WU)((0,c.D)(e.row.lastUpdated),"yyyy-MM-dd HH:mm:ss"):"N/A"}],em=d.flatMap(e=>Object.entries(e).filter(e=>{let[t,n]=e;return"number"==typeof n&&n>0}).map(t=>{let[n,r]=t;return{id:`${e._id}_${n}`,item:n,count:r,lastUpdated:e.timestamp||null}})),ex=t?`Front of House - ${"arena"===t.type?"Arena":"Campsite"}`:"Front of House";return(0,r.jsxs)(L.Z,{maxWidth:"lg",sx:{py:4},children:[(0,r.jsxs)(x.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:4},children:[(0,r.jsx)(h.Z,{variant:"h4",sx:{fontWeight:"bold",color:"text.primary"},children:ex}),(0,r.jsx)(O.Z,{title:y||"",children:(0,r.jsx)(g.Z,{variant:"contained",color:y?"error":B?"warning":"primary",startIcon:j?(0,r.jsx)(C.Z,{size:20}):y?(0,r.jsx)(_.Z,{}):(0,r.jsx)(T.Z,{}),onClick:el,disabled:j,children:j?"Syncing...":y?"Sync Failed":B?"Changes Pending":"Sync Now"})})]}),y&&(0,r.jsx)(X.Z,{severity:"error",sx:{mb:3},children:y}),(0,r.jsxs)(A.Z,{container:!0,spacing:3,sx:{mb:4},children:[(0,r.jsx)(A.Z,{size:{xs:12,sm:6,md:4,lg:3},children:eu("Suncream",S.Z,"Suncream")}),(0,r.jsx)(A.Z,{size:{xs:12,sm:6,md:4,lg:3},children:eu("Poncho",v.Z,"Poncho")}),(0,r.jsx)(A.Z,{size:{xs:12,sm:6,md:4,lg:3},children:eu("Water",z.Z,"Water")}),(0,r.jsx)(A.Z,{size:{xs:12,sm:6,md:4,lg:3},children:eu("SanitaryProducts",I.Z,"Sanitary\nProducts")}),(0,r.jsx)(A.Z,{size:{xs:12,sm:6,md:4,lg:3},children:eu("Earplugs",R.Z,"Earplugs")}),(0,r.jsx)(A.Z,{size:{xs:12,sm:6,md:4,lg:3},children:eu("Condoms",W.Z,"Condoms")}),(0,r.jsx)(A.Z,{size:{xs:12,sm:6,md:4,lg:3},children:eu("ChildrensWristbands",P.Z,"Children's\nWristbands")}),(0,r.jsx)(A.Z,{size:{xs:12,sm:6,md:4,lg:3},children:eu("GeneralWristbands",D.Z,"General\nWristbands")}),(0,r.jsx)(A.Z,{size:{xs:12,sm:6,md:4,lg:3},children:eu("Charging",E.Z,"Charging")}),(0,r.jsx)(A.Z,{size:{xs:12,sm:6,md:4,lg:3},children:eu("Sanitizer",F.Z,"Sanitizer")}),(0,r.jsx)(A.Z,{size:{xs:12,sm:6,md:4,lg:3},children:eu("ToiletRoll",N.Z,"Toilet Roll")}),(0,r.jsx)(A.Z,{size:{xs:12,sm:6,md:4,lg:3},children:eu("GeneralEnqs",H.Z,"General\nEnquiries")}),(0,r.jsx)(A.Z,{size:{xs:12,sm:6,md:4,lg:3},children:eu("HotWater",M.Z,"Hot Water")}),(0,r.jsx)(A.Z,{size:{xs:12,sm:6,md:4,lg:3},children:eu("RestAndRecuperation",U.Z,"Rest and\nRecuperation")})]}),(0,r.jsxs)(Y.Z,{elevation:3,sx:{bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,p:3},children:[(0,r.jsx)(x.Z,{sx:{display:"flex",alignItems:"center",mb:2},children:(0,r.jsx)(h.Z,{variant:"h6",color:"primary",children:"Items Given Out"})}),(0,r.jsx)(x.Z,{sx:{height:400,width:"100%"},children:n?(0,r.jsx)(x.Z,{sx:{p:3},children:(0,r.jsx)(h.Z,{children:"Loading items..."})}):(0,r.jsx)(G._,{rows:em,columns:ed,density:"compact",disableRowSelectionOnClick:!0,sx:{"& .MuiDataGrid-cell":{fontSize:"0.875rem"},border:"none",bgcolor:"background.paper",backdropFilter:"blur(8px)"}})})]}),(0,r.jsx)(f,{open:V,onClose:()=>ee(!1),onConfirm:eo,itemName:et?.name||"",label:et?.label||""})]})}}}]);