"use strict";(self.webpackChunkithinc_welfare=self.webpackChunkithinc_welfare||[]).push([["102"],{43119:function(e,t,r){r.d(t,{n:()=>x});var i=r(85893);r(67294);var s=r(64889),l=r(30925),n=r(81839),o=r(33991),a=r(56099),d=r(39467),c=r(13319),u=r(7230),h=r(54757);let x=e=>{let{showReAdmitModal:t,showConfirmReAdmit:r,showConfirmDischarge:x,reAdmitLocation:m,reAdmitNotes:p,dischargeNotes:b,dischargeTime:j,bayStatus:g,isSubmitting:y,onReAdmitLocationChange:f,onReAdmitNotesChange:v,onDischargeNotesChange:w,onDischargeTimeChange:Z,onCloseReAdmit:C,onCloseConfirmReAdmit:S,onCloseConfirmDischarge:D,onConfirmReAdmit:k,onConfirmDischarge:A,onShowConfirmReAdmit:T}=e;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(s.Z,{open:t,onClose:C,children:[(0,i.jsx)(l.Z,{children:"Re-Admit Patient"}),(0,i.jsx)(n.Z,{children:(0,i.jsxs)(o.Z,{sx:{pt:2,width:"100%",minWidth:300},children:[(0,i.jsx)(a.Z,{fullWidth:!0,type:"number",label:"Location (Bay Number)",value:m,onChange:e=>f(Number(e.target.value)),sx:{mb:2}}),g&&(0,i.jsx)(d.Z,{severity:g.isOccupied?"error":"success",sx:{mb:2},children:g.message}),(0,i.jsx)(a.Z,{fullWidth:!0,multiline:!0,rows:3,label:"Additional Notes",value:p,onChange:e=>v(e.target.value)})]})}),(0,i.jsxs)(c.Z,{children:[(0,i.jsx)(u.Z,{onClick:C,children:"Cancel"}),(0,i.jsx)(u.Z,{onClick:T,variant:"contained",disabled:!m||(g?.isOccupied??!1),children:"Re-Admit"})]})]}),(0,i.jsxs)(s.Z,{open:r,onClose:S,children:[(0,i.jsx)(l.Z,{children:"Confirm Re-Admission"}),(0,i.jsx)(n.Z,{children:(0,i.jsx)(h.Z,{children:"Are you sure you want to re-admit this patient?"})}),(0,i.jsxs)(c.Z,{children:[(0,i.jsx)(u.Z,{onClick:S,children:"Cancel"}),(0,i.jsx)(u.Z,{onClick:k,variant:"contained",color:"primary",children:"Confirm"})]})]}),(0,i.jsxs)(s.Z,{open:x,onClose:D,children:[(0,i.jsx)(l.Z,{children:"Discharge Patient"}),(0,i.jsx)(n.Z,{children:(0,i.jsxs)(o.Z,{sx:{pt:2,width:"100%",minWidth:300},children:[(0,i.jsx)(a.Z,{fullWidth:!0,type:"datetime-local",label:"Discharge Time",value:j,onChange:e=>Z(e.target.value),sx:{mb:2},InputLabelProps:{shrink:!0}}),(0,i.jsx)(a.Z,{fullWidth:!0,multiline:!0,rows:3,label:"Discharge Notes",value:b,onChange:e=>w(e.target.value),required:!0,error:!b,helperText:b?"":"Discharge notes are required"})]})}),(0,i.jsxs)(c.Z,{children:[(0,i.jsx)(u.Z,{onClick:D,children:"Cancel"}),(0,i.jsx)(u.Z,{onClick:A,variant:"contained",color:"primary",disabled:y||!b,children:y?"Discharging...":"Confirm"})]})]})]})}},44090:function(e,t,r){r.d(t,{T:()=>s});var i=r(67294);let s=e=>{let{onLongPress:t,onClick:r,threshold:s=500,captureEvent:l=!0}=e,n=(0,i.useRef)(null),o=(0,i.useRef)(!1),a=(0,i.useRef)(null),[d,c]=(0,i.useState)(!1),u=(0,i.useCallback)(()=>{n.current&&(clearTimeout(n.current),n.current=null),c(!1),o.current=!1,a.current=null},[]),h=(0,i.useCallback)(e=>{u(),a.current={x:"touches"in e?e.touches[0]?.clientX:e.clientX,y:"touches"in e?e.touches[0]?.clientY:e.clientY},l&&e.preventDefault(),c(!0),n.current=window.setTimeout(()=>{o.current=!0,c(!1),t(e)},s)},[t,s,l,u]),x=(0,i.useCallback)(function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],i=o.current;u(),t&&!i&&r&&r(e)},[r,u]),m=(0,i.useCallback)(e=>{if(!a.current||!n.current)return;let t="touches"in e?e.touches[0]?.clientX:e.clientX,r="touches"in e?e.touches[0]?.clientY:e.clientY,i=Math.abs(t-a.current.x),s=Math.abs(r-a.current.y);(i>10||s>10)&&u()},[u,10]),p=(0,i.useCallback)(e=>{h(e)},[h]),b=(0,i.useCallback)(e=>{x(e,!0)},[x]),j=(0,i.useCallback)(e=>{x(e,!1)},[x]);(0,i.useCallback)(e=>{m(e)},[m]);let g=(0,i.useCallback)(e=>{h(e)},[h]),y=(0,i.useCallback)(e=>{x(e,!0)},[x]),f=(0,i.useCallback)(e=>{m(e)},[m]);return(0,i.useEffect)(()=>()=>{u()},[u]),{onMouseDown:p,onMouseUp:b,onMouseLeave:j,onTouchStart:g,onTouchEnd:y,onTouchMove:f,isLongPressing:d}}},37090:function(e,t,r){r.r(t),r.d(t,{ReportsPage:()=>tc});var i,s=r(85893),l=r(67294),n=r(33991),o=r(39467),a=r(5214),d=r(83502),c=r(78181),u=r(69326),h=r(7360),x=r(59326);let m=(e,t)=>{let[r,i]=(0,l.useState)({admissions:[],itemCounts:[],lostPropertyItems:[],sensoryHubVisits:[]}),[s,n]=(0,l.useState)(!0),[o,a]=(0,l.useState)(null),{activeSiteLocation:m}=(0,x.C)();return(0,l.useEffect)(()=>{(async()=>{if(!e)return n(!1);n(!0);try{let[t,r,s,l]=await Promise.all([d.R.getAdmissionsByFestival(e),d.R.getItemCountsByFestival(e),d.R.getLostPropertyItems(),d.R.getSensoryHubVisitsByFestival(e)]),n=m?t.filter(e=>e.siteLocationId===m.id):t,o=m?r.filter(e=>e.siteLocationId===m.id):r,c=s.filter(t=>{let r=t.festivalId===e;return m?r&&t.siteLocationId===m.id:r}),u=m?l.filter(e=>e.siteLocationId===m.id):l;i({admissions:n||[],itemCounts:o||[],lostPropertyItems:c||[],sensoryHubVisits:u||[]}),a(null)}catch(e){a(e instanceof Error?e.message:"An error occurred while fetching data"),i({admissions:[],itemCounts:[],lostPropertyItems:[],sensoryHubVisits:[]})}finally{n(!1)}})()},[e,m]),{data:(0,l.useMemo)(()=>s||!r?{admissions:[],itemCounts:[],lostPropertyItems:[],sensoryHubVisits:[]}:"all"===t?r:{admissions:r.admissions.filter(e=>e.Attended&&(0,c.K)((0,u.D)(e.Attended),(0,h.b)((0,u.D)(t)))),itemCounts:r.itemCounts.filter(e=>e.timestamp&&(0,c.K)((0,u.D)(e.timestamp),(0,h.b)((0,u.D)(t)))),lostPropertyItems:r.lostPropertyItems.filter(e=>e.timeFound&&(0,c.K)((0,u.D)(e.timeFound),(0,h.b)((0,u.D)(t)))),sensoryHubVisits:r.sensoryHubVisits.filter(e=>e.visitTimestamp&&(0,c.K)((0,u.D)(e.visitTimestamp),(0,h.b)((0,u.D)(t))))},[r,t,s]),loading:s,error:o}};var p=r(61215),b=r(89126),j=r(98106),g=r(54757),y=r(60488),f=r(38953),v=r(41994),w=r(9599),Z=r(73892),C=r(48346),S=r(73876),D=r(13894),k=r(60630);let A=e=>{let{startDate:t,endDate:r,selectedDay:i,onChange:l}=e;return(0,s.jsx)(Z.Z,{size:"small",sx:{minWidth:200,ml:2,"& .MuiOutlinedInput-root":{bgcolor:"background.paper","&:hover .MuiOutlinedInput-notchedOutline":{borderColor:"primary.main"}},"& .MuiSelect-select":{py:1}},children:(0,s.jsxs)(C.Z,{value:i,onChange:e=>{l(e.target.value)},displayEmpty:!0,variant:"outlined",children:[(0,s.jsx)(S.Z,{value:"all",children:"All Days"}),(0,D.D)({start:(0,u.D)(t),end:(0,u.D)(r)}).map((e,t)=>(0,s.jsxs)(S.Z,{value:e.toISOString(),children:["Day ",t+1," - ",(0,k.WU)(e,"MMM d, yyyy")]},e.toISOString()))]})})};var T=r(88957),R=r(46560);let I=e=>{let{activeTab:t,onTabChange:r}=e;return(0,s.jsx)(n.Z,{sx:{borderBottom:1,borderColor:"divider"},children:(0,s.jsxs)(T.Z,{value:t,onChange:(e,t)=>{r(t)},"aria-label":"report tabs",textColor:"primary",indicatorColor:"primary",sx:{"& .MuiTab-root":{textTransform:"none",minWidth:120,fontSize:"0.875rem",fontWeight:500,color:"text.secondary","&.Mui-selected":{color:"primary.main"}}},children:[(0,s.jsx)(R.Z,{label:"Admissions",value:"admissions",sx:{borderRadius:"8px 8px 0 0"}}),(0,s.jsx)(R.Z,{label:"Front of House",value:"frontOfHouse",sx:{borderRadius:"8px 8px 0 0"}}),(0,s.jsx)(R.Z,{label:"Lost Property",value:"lostProperty",sx:{borderRadius:"8px 8px 0 0"}}),(0,s.jsx)(R.Z,{label:"Sensory Hub",value:"sensoryHub",sx:{borderRadius:"8px 8px 0 0"}})]})})};var F=r(39764),M=r(8837);let N=["#662D91","#E0338C","#8E44AD","#E74C3C"],P=["Suncream","Poncho","Water","SanitaryProducts","Earplugs","Condoms","ChildrensWristbands","GeneralWristbands","Charging","Sanitizer","ToiletRoll","GeneralEnqs"],L=e=>{let t=Array.from({length:24},(e,t)=>({hour:t,count:0,label:(0,k.WU)(new Date().setHours(t,0,0,0),"ha")}));return e.forEach(e=>{let r=(0,M.p)((0,u.D)(e.Attended)),i=t.findIndex(e=>e.hour===r);-1!==i&&t[i].count++}),t},E=e=>Object.entries(e.reduce((e,t)=>(e[t.ReasonCategory]=(e[t.ReasonCategory]||0)+1,e),{})).map(e=>{let[t,r]=e;return{reason:t,count:r}}),B=e=>Object.entries(e.reduce((e,t)=>{let r=10*Math.floor((t.Age||0)/10),i=`${r}-${r+9}`;return e[i]=(e[i]||0)+1,e},{})).map(e=>{let[t,r]=e;return{ageGroup:t,count:r}}),G=e=>Object.entries(e.reduce((e,t)=>(P.forEach(r=>{e[r]=(e[r]||0)+(t[r]||0)}),e),{})).map(e=>{let[t,r]=e;return{type:t,count:r}}).sort((e,t)=>t.count-e.count),$=e=>{let t=Array.from({length:24},(e,t)=>({hour:t,count:0,label:(0,k.WU)(new Date().setHours(t,0,0,0),"ha")}));return e.forEach(e=>{if(!e.timeFound)return;let r=(0,M.p)((0,u.D)(e.timeFound)),i=t.findIndex(e=>e.hour===r);-1!==i&&t[i].count++}),t},O=e=>Object.entries(e.reduce((e,t)=>(e[t.category]=(e[t.category]||0)+1,e),{})).map(e=>{let[t,r]=e;return{category:t,count:r}}).sort((e,t)=>t.count-e.count),W=e=>Object.entries(e.reduce((e,t)=>{let r=t.itemReturned?"Returned":"Pending";return e[r]=(e[r]||0)+1,e},{})).map(e=>{let[t,r]=e;return{status:t,count:r}}),H=e=>Object.entries(e.reduce((e,t)=>{let r="look_around"===t.purpose?"Look Around":"Use Service";return e[r]=(e[r]||0)+1,e},{})).map(e=>{let[t,r]=e;return{purpose:t,count:r}}),z=e=>Object.entries(e.reduce((e,t)=>{let r="crew"===t.userType?"Crew":"Public";return e[r]=(e[r]||0)+1,e},{})).map(e=>{let[t,r]=e;return{userType:t,count:r}}),V=e=>Object.entries(e.filter(e=>"crew"===e.userType&&e.teamName).reduce((e,t)=>{let r=t.teamName||"Unknown";return e[r]=(e[r]||0)+1,e},{})).map(e=>{let[t,r]=e;return{teamName:t,count:r}}).sort((e,t)=>t.count-e.count);var K=r(15858);let U=F.mM.create({page:{padding:30},header:{marginBottom:30,borderBottom:"2px solid #662D91",paddingBottom:10,alignItems:"center"},logo:{width:200,marginBottom:10},section:{marginBottom:20},title:{fontSize:18,marginBottom:10,color:"#662D91"},sectionSubtitle:{fontSize:14,marginBottom:5,color:"#662D91"},row:{flexDirection:"row",paddingVertical:3},cell:{flex:1},text:{fontSize:12},footer:{position:"absolute",bottom:30,left:30,right:30,textAlign:"center",color:"#662D91",fontSize:10,borderTop:"1px solid #662D91",paddingTop:10}}),_=e=>{let{admissions:t,itemCounts:r,lostPropertyItems:i,sensoryHubVisits:l}=e,n=E(t),o=B(t),a=G(r),d=W(i),c=H(l),u=z(l),h=V(l),x=new Date().toLocaleDateString();return(0,s.jsx)(F.BB,{children:(0,s.jsxs)(F.T3,{size:"A4",style:U.page,children:[(0,s.jsx)(F.G7,{style:U.header,children:(0,s.jsx)(F.Ee,{src:"/ithinklogo.png",style:U.logo})}),(0,s.jsxs)(F.G7,{style:U.section,children:[(0,s.jsx)(F.xv,{style:U.title,children:"Admissions Report"}),(0,s.jsx)(F.xv,{style:U.sectionSubtitle,children:"Admission Reasons"}),n.map((e,t)=>(0,s.jsxs)(F.G7,{style:U.row,children:[(0,s.jsx)(F.xv,{style:[U.cell,U.text],children:e.reason}),(0,s.jsx)(F.xv,{style:[U.cell,U.text],children:e.count})]},t)),(0,s.jsx)(F.xv,{style:[U.sectionSubtitle,{marginTop:10}],children:"Age Distribution"}),o.map((e,t)=>(0,s.jsxs)(F.G7,{style:U.row,children:[(0,s.jsx)(F.xv,{style:[U.cell,U.text],children:e.ageGroup}),(0,s.jsx)(F.xv,{style:[U.cell,U.text],children:e.count})]},t))]}),(0,s.jsxs)(F.G7,{style:U.section,children:[(0,s.jsx)(F.xv,{style:U.title,children:"Front of House Report"}),a.map((e,t)=>(0,s.jsxs)(F.G7,{style:U.row,children:[(0,s.jsx)(F.xv,{style:[U.cell,U.text],children:e.type}),(0,s.jsx)(F.xv,{style:[U.cell,U.text],children:e.count})]},t))]}),(0,s.jsxs)(F.G7,{style:U.section,children:[(0,s.jsx)(F.xv,{style:U.title,children:"Lost Property Report"}),d.map((e,t)=>(0,s.jsxs)(F.G7,{style:U.row,children:[(0,s.jsx)(F.xv,{style:[U.cell,U.text],children:e.status}),(0,s.jsx)(F.xv,{style:[U.cell,U.text],children:e.count})]},t))]}),(0,s.jsxs)(F.G7,{style:U.section,children:[(0,s.jsx)(F.xv,{style:U.title,children:"Sensory Hub Report"}),(0,s.jsx)(F.xv,{style:U.sectionSubtitle,children:"Visit Purpose"}),c.map((e,t)=>(0,s.jsxs)(F.G7,{style:U.row,children:[(0,s.jsx)(F.xv,{style:[U.cell,U.text],children:e.purpose}),(0,s.jsx)(F.xv,{style:[U.cell,U.text],children:e.count})]},t)),(0,s.jsx)(F.xv,{style:[U.sectionSubtitle,{marginTop:10}],children:"User Type"}),u.map((e,t)=>(0,s.jsxs)(F.G7,{style:U.row,children:[(0,s.jsx)(F.xv,{style:[U.cell,U.text],children:e.userType}),(0,s.jsx)(F.xv,{style:[U.cell,U.text],children:e.count})]},t)),h.length>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(F.xv,{style:[U.sectionSubtitle,{marginTop:10}],children:"Crew Teams"}),h.map((e,t)=>(0,s.jsxs)(F.G7,{style:U.row,children:[(0,s.jsx)(F.xv,{style:[U.cell,U.text],children:e.teamName}),(0,s.jsx)(F.xv,{style:[U.cell,U.text],children:e.count})]},t))]}),(0,s.jsxs)(F.xv,{style:[U.sectionSubtitle,{marginTop:10}],children:["Total Visits: ",l.length]})]}),(0,s.jsxs)(F.xv,{style:U.footer,children:["Generated on ",x," • iTHINK Welfare System"]})]})})},q=e=>{let{admissions:t,itemCounts:r,lostPropertyItems:i,sensoryHubVisits:l,open:o,onClose:a}=e;return(0,s.jsx)(K.Z,{open:o,onClose:a,"aria-labelledby":"pdf-preview-modal",sx:{display:"flex",alignItems:"center",justifyContent:"center"},children:(0,s.jsx)(n.Z,{sx:{width:"90%",height:"90%",bgcolor:"background.paper"},children:(0,s.jsx)(F.Z$,{style:{width:"100%",height:"100%"},children:(0,s.jsx)(_,{admissions:t,itemCounts:r,lostPropertyItems:i,sensoryHubVisits:l})})})})},Y=async function(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],l=(0,s.jsx)(_,{admissions:e,itemCounts:t,lostPropertyItems:r,sensoryHubVisits:i}),n=await (0,F.eA)(l).toBlob(),o=URL.createObjectURL(n),a=document.createElement("a");a.href=o,a.download=`welfare-report-${new Date().toISOString().split("T")[0]}.pdf`,a.click(),URL.revokeObjectURL(o)},Q=e=>{let{festival:t,selectedDay:r,onDayChange:i,activeTab:a,onTabChange:d,loading:c,error:u,children:h,data:x}=e,[m,Z]=(0,l.useState)(!1),C=async()=>{x&&await Y(x.admissions,x.itemCounts,x.lostPropertyItems,x.sensoryHubVisits)};return c?(0,s.jsx)(n.Z,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"200px",children:(0,s.jsx)(p.Z,{})}):u?(0,s.jsx)(n.Z,{p:2,children:(0,s.jsxs)(o.Z,{severity:"error",children:["Error: ",u]})}):(0,s.jsx)(b.Z,{elevation:0,sx:{minHeight:"100vh",bgcolor:"background.paper",backdropFilter:"blur(8px)",py:4},children:(0,s.jsxs)(j.Z,{maxWidth:"xl",children:[(0,s.jsxs)(n.Z,{mb:4,children:[(0,s.jsxs)(g.Z,{variant:"h4",color:"text.primary",gutterBottom:!0,children:["Reports for ",t.name]}),(0,s.jsxs)(n.Z,{display:"flex",alignItems:"center",gap:2,mt:1,children:[(0,s.jsxs)(g.Z,{variant:"body2",color:"text.secondary",children:[new Date(t.startDate).toLocaleDateString()," - ",new Date(t.endDate).toLocaleDateString()]}),(0,s.jsx)(A,{startDate:t.startDate,endDate:t.endDate,selectedDay:r,onChange:i})]})]}),(0,s.jsxs)(n.Z,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[(0,s.jsx)(g.Z,{variant:"h5",color:"primary",children:"Reports"}),(0,s.jsxs)(n.Z,{display:"flex",alignItems:"center",gap:2,children:[(0,s.jsx)(I,{activeTab:a,onTabChange:d}),(0,s.jsxs)(n.Z,{children:[(0,s.jsx)(y.Z,{title:"Preview PDF",children:(0,s.jsx)(f.Z,{color:"primary",onClick:()=>Z(!0),disabled:!x,children:(0,s.jsx)(v.Z,{})})}),(0,s.jsx)(y.Z,{title:"Download PDF",children:(0,s.jsx)(f.Z,{color:"primary",onClick:C,disabled:!x,children:(0,s.jsx)(w.Z,{})})})]})]})]}),h,x&&(0,s.jsx)(q,{admissions:x.admissions,itemCounts:x.itemCounts,lostPropertyItems:x.lostPropertyItems,sensoryHubVisits:x.sensoryHubVisits,open:m,onClose:()=>Z(!1)})]})})};var X=r(12550),J=r(29009),ee=r(94831),et=r(14195),er=r(3023),ei=r(75358),es=r(62722),el=r(64963),en=r(43815),eo=r(90722),ea=r(34354),ed=r(46208),ec=((i={}).TIME="TIME",i.REASON="REASON",i.AGE="AGE",i),eu=r(44090),eh=r(90271),ex=r(86001),em=r(62403);let ep=e=>{let{isVisible:t,size:r=40,color:i="primary",position:o={top:"50%",left:"50%"},duration:a=500,zIndex:d=1e3}=e,c=(0,eh.Z)(),u=l.useMemo(()=>{let e="50%"===o.left?"-50%":"0",t="50%"===o.top?"-50%":"0";return`translate(${e}, ${t})`},[o.left,o.top]),h=l.useMemo(()=>{switch(i){case"primary":return c.palette.primary.main;case"secondary":return c.palette.secondary.main;default:return c.palette.text.primary}},[i,c.palette]);return(0,s.jsx)(ex.Z,{in:t,timeout:{enter:150,exit:300},unmountOnExit:!0,children:(0,s.jsxs)(n.Z,{sx:{position:"absolute",top:o.top,left:o.left,right:o.right,bottom:o.bottom,transform:u,zIndex:d,pointerEvents:"none",display:"flex",alignItems:"center",justifyContent:"center",width:r,height:r,borderRadius:"50%",backgroundColor:(0,em.Fq)(c.palette.background.paper,.9),boxShadow:c.shadows[4],border:`2px solid ${(0,em.Fq)(h,.2)}`},children:[(0,s.jsx)(p.Z,{size:r-12,thickness:4,sx:{color:h,"& .MuiCircularProgress-circle":{strokeLinecap:"round"}},variant:"indeterminate"}),(0,s.jsx)(n.Z,{sx:{position:"absolute",width:6,height:6,borderRadius:"50%",backgroundColor:h,opacity:.8}})]})})},eb=e=>(t,r)=>{let i=ej(t,e.filterType);i&&e.onFilterSelect(e.filterType,i)},ej=(e,t)=>{if(!e.activePayload||0===e.activePayload.length)return null;let r=e.activePayload[0].payload;switch(t){case ec.TIME:if("label"in r)return r.label;return e.activeLabel||null;case ec.REASON:if("reason"in r)return r.reason;return e.activeLabel||null;case ec.AGE:if("ageGroup"in r)return r.ageGroup;return e.activeLabel||null;default:return e.activeLabel||null}},eg=(e,t,r)=>{let i=1===r?"record":"records";switch(e){case ec.TIME:return`${r} ${i} admitted during ${t}`;case ec.REASON:return`${r} ${i} with reason: ${t}`;case ec.AGE:return`${r} ${i} in age group: ${t}`;default:return`${r} ${i} for ${t}`}},ey=e=>{let{admissions:t,filterState:r,filterActions:i,filteredData:l,onLongPressModal:n}=e,o={filterType:ec.TIME,onFilterSelect:(e,t)=>{i.addFilter(e,t)},onLongPress:(e,t,r)=>{n(r,t,e)},getFilteredData:e=>t.filter(t=>{if(!t.Attended)return!1;let r=new Date(t.Attended).getHours();return`${r.toString().padStart(2,"0")}:00-${(r+1).toString().padStart(2,"0")}:00`===e})},a={filterType:ec.REASON,onFilterSelect:(e,t)=>{i.addFilter(e,t)},onLongPress:(e,t,r)=>{n(r,t,e)},getFilteredData:e=>t.filter(t=>t.ReasonCategory===e)},d={filterType:ec.AGE,onFilterSelect:(e,t)=>{i.addFilter(e,t)},onLongPress:(e,t,r)=>{n(r,t,e)},getFilteredData:e=>t.filter(t=>{if(!t.Age)return!1;let r=t.Age,[i,s]=e.split("-").map(Number);return r>=i&&r<=s})},c=(0,eu.T)({onLongPress:e=>{n(o.getFilteredData(""),"",ec.TIME)},threshold:500}),u=(0,eu.T)({onLongPress:e=>{n(a.getFilteredData(""),"",ec.REASON)},threshold:500}),h=(0,eu.T)({onLongPress:e=>{n(d.getFilteredData(""),"",ec.AGE)},threshold:500}),x=eb(o),m=eb(a),p=eb(d);return(0,s.jsxs)(X.Z,{container:!0,spacing:3,children:[(0,s.jsx)(X.Z,{size:{xs:12},children:(0,s.jsxs)(b.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,position:"relative"},children:[(0,s.jsx)(g.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Admissions by Time of Day"}),(0,s.jsx)("div",{style:{height:300,width:"100%"},onMouseDown:c.onMouseDown,onMouseUp:c.onMouseUp,onMouseLeave:c.onMouseLeave,onTouchStart:c.onTouchStart,onTouchEnd:c.onTouchEnd,onTouchMove:c.onTouchMove,children:(0,s.jsx)(J.h,{children:(0,s.jsxs)(ee.v,{data:L(l.filteredAdmissions),onClick:x,children:[(0,s.jsx)(et.q,{strokeDasharray:"3 3"}),(0,s.jsx)(er.K,{dataKey:"label",interval:2}),(0,s.jsx)(ei.B,{}),(0,s.jsx)(es.u,{formatter:e=>[e,"Admissions"]}),(0,s.jsx)(el.$,{dataKey:"count",fill:"#662D91",name:"Admissions",cursor:"pointer",children:L(l.filteredAdmissions).map((e,t)=>(0,s.jsx)(en.b,{fill:e.count>0?"#662D91":"#E0E0E0"},`cell-${t}`))})]})})}),c.isLongPressing&&(0,s.jsx)(ep,{isVisible:c.isLongPressing,position:{top:"50%",left:"50%"}})]})}),(0,s.jsx)(X.Z,{size:{xs:12,md:6},children:(0,s.jsxs)(b.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,height:"100%",position:"relative"},children:[(0,s.jsx)(g.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Admissions by Reason"}),(0,s.jsx)("div",{style:{height:300,width:"100%"},onMouseDown:u.onMouseDown,onMouseUp:u.onMouseUp,onMouseLeave:u.onMouseLeave,onTouchStart:u.onTouchStart,onTouchEnd:u.onTouchEnd,onTouchMove:u.onTouchMove,children:(0,s.jsx)(J.h,{children:(0,s.jsxs)(ee.v,{data:E(l.filteredAdmissions),onClick:m,children:[(0,s.jsx)(et.q,{strokeDasharray:"3 3"}),(0,s.jsx)(er.K,{dataKey:"reason"}),(0,s.jsx)(ei.B,{}),(0,s.jsx)(es.u,{}),(0,s.jsx)(eo.D,{}),(0,s.jsx)(el.$,{dataKey:"count",name:"Count",cursor:"pointer",children:E(l.filteredAdmissions).map((e,t)=>(0,s.jsx)(en.b,{fill:N[t%N.length]},`cell-${t}`))})]})})}),u.isLongPressing&&(0,s.jsx)(ep,{isVisible:u.isLongPressing,position:{top:"50%",left:"50%"}})]})}),(0,s.jsx)(X.Z,{size:{xs:12,md:6},children:(0,s.jsxs)(b.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,height:"100%",position:"relative"},children:[(0,s.jsx)(g.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Age Distribution"}),(0,s.jsx)("div",{style:{height:300,width:"100%"},onMouseDown:h.onMouseDown,onMouseUp:h.onMouseUp,onMouseLeave:h.onMouseLeave,onTouchStart:h.onTouchStart,onTouchEnd:h.onTouchEnd,onTouchMove:h.onTouchMove,children:(0,s.jsx)(J.h,{children:(0,s.jsxs)(ea.u,{onClick:p,children:[(0,s.jsx)(ed.b,{data:B(l.filteredAdmissions),dataKey:"count",nameKey:"ageGroup",cx:"50%",cy:"50%",outerRadius:100,label:!0,cursor:"pointer",children:B(l.filteredAdmissions).map((e,t)=>(0,s.jsx)(en.b,{fill:N[t%N.length]},`cell-${t}`))}),(0,s.jsx)(es.u,{}),(0,s.jsx)(eo.D,{})]})})}),h.isLongPressing&&(0,s.jsx)(ep,{isVisible:h.isLongPressing,position:{top:"50%",left:"50%"}})]})})]})};var ef=r(58308),ev=r(82400),ew=r(7230),eZ=r(86202);let eC=e=>{let{admissions:t,selectedItems:r,onSelectAll:i,onSelectItem:o,onDelete:a,onDischarge:d}=e,[c,u]=(0,l.useState)(!1),[h,x]=(0,l.useState)(null),m=e=>{x(e),u(!0)},p=[{field:"name",headerName:"Name",flex:1,sortable:!0,filterable:!0,renderCell:e=>{let t=e.row;return`${t.FirstName||""} ${t.Surname||""}`.trim()||"N/A"},valueGetter:e=>{if(!e||!e.row||"object"!=typeof e.row)return"n/a";let t=e.row;return`${t.FirstName||""} ${t.Surname||""}`.trim().toLowerCase()||"n/a"}},{field:"Age",headerName:"Age",width:100,sortable:!0,filterable:!0,valueGetter:e=>{if(!e||!e.row||"object"!=typeof e.row)return;let t=e.row.Age;return"number"==typeof t?t:void 0},renderCell:e=>{let t=e.row;return"number"==typeof t.Age?t.Age:"N/A"}},{field:"ReferredBy",headerName:"Referred By",flex:1,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.ReferredBy||"n/a",renderCell:e=>e.row.ReferredBy||"N/A"},{field:"ReasonCategory",headerName:"Reason",flex:1,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.ReasonCategory||"n/a",renderCell:e=>e.row.ReasonCategory||"N/A"},{field:"SubstanceUsed",headerName:"Substances",flex:1,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.SubstanceUsed?.join(", ").toLowerCase()||"n/a",renderCell:e=>{let t=e.row;return t.SubstanceUsed?.join(", ")||"N/A"}},{field:"Attended",headerName:"Admitted",flex:1,sortable:!0,filterable:!0,type:"dateTime",valueGetter:e=>e&&e.row&&"object"==typeof e.row&&"Attended"in e.row&&e.row.Attended?new Date(e.row.Attended):null,renderCell:e=>{if(!e||!e.row||"object"!=typeof e.row||!("Attended"in e.row))return"N/A";let t=e.row;return t.Attended?new Date(t.Attended).toLocaleString():"N/A"}},{field:"BaysOrChairs",headerName:"Bay/Chair",width:100,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.BaysOrChairs||"n/a",renderCell:e=>e.row.BaysOrChairs||"N/A"},{field:"Location",headerName:"Location",width:100,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.Location||"n/a",renderCell:e=>e.row.Location||"N/A"},{field:"actions",headerName:"Actions",width:120,sortable:!1,filterable:!1,renderCell:e=>{let t=e.row;return t.InBayNow&&d?(0,s.jsx)(ew.Z,{variant:"contained",color:"primary",size:"small",onClick:e=>{e.stopPropagation(),d(t)},children:"Discharge"}):null}}];return(0,s.jsxs)(b.Z,{elevation:3,sx:{bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,p:3},children:[(0,s.jsxs)(n.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[(0,s.jsx)(g.Z,{variant:"h6",color:"primary",children:"Raw Data"}),r.length>0&&(0,s.jsxs)(ew.Z,{variant:"contained",color:"error",onClick:a,children:["Delete Selected (",r.length,")"]})]}),(0,s.jsx)("div",{style:{height:400,width:"100%"},children:(0,s.jsx)(ef._,{rows:t||[],columns:p,getRowId:e=>e._id,checkboxSelection:!0,disableRowSelectionOnClick:!0,rowSelectionModel:{type:"include",ids:new Set(r)},onRowSelectionModelChange:e=>{let t=Array.from(e.ids).map(e=>e.toString());t.forEach(e=>{r.includes(e)||o(e)}),r.forEach(e=>{t.includes(e)||o(e)})},density:"compact",slots:{toolbar:ev.n},slotProps:{toolbar:{showQuickFilter:!0,quickFilterProps:{debounceMs:300}}},initialState:{sorting:{sortModel:[{field:"Attended",sort:"desc"}]}},onRowClick:e=>m(e.row),sx:{"& .MuiDataGrid-cell":{fontSize:"0.875rem"},"& .MuiDataGrid-toolbarContainer":{padding:"8px 24px"},"& .MuiDataGrid-toolbar":{"& .MuiFormControl-root":{width:"100%",maxWidth:"300px"}},"& .MuiDataGrid-row":{cursor:"pointer"},border:"none",bgcolor:"background.paper",backdropFilter:"blur(8px)"}})}),(0,s.jsx)(eZ.k,{open:c,onClose:()=>{u(!1)},record:h,recordType:"admission"})]})};var eS=r(43119);let eD={timeFilters:[],reasonFilters:[],ageFilters:[],filterMode:"AND"},ek=()=>{let[e,t]=(0,l.useState)(eD),r=(0,l.useCallback)((e,r)=>{t(t=>{let i=eA(e),s=t[i];return s.includes(r)?t:{...t,[i]:[...s,r]}})},[]),i=(0,l.useCallback)((e,r)=>{t(t=>{let i=eA(e);return{...t,[i]:t[i].filter(e=>e!==r)}})},[]),s=(0,l.useCallback)(()=>{t(eD)},[]),n=(0,l.useCallback)(e=>{t(t=>({...t,[eA(e)]:[]}))},[]),o=(0,l.useCallback)(()=>{t(e=>({...e,filterMode:"AND"===e.filterMode?"OR":"AND"}))},[]),a=(0,l.useCallback)((t,r)=>e[eA(t)].includes(r),[e]),d=(0,l.useMemo)(()=>[...e.timeFilters,...e.reasonFilters,...e.ageFilters],[e]),c=(0,l.useMemo)(()=>d.length>0,[d]),u=(0,l.useMemo)(()=>({time:e.timeFilters.length,reason:e.reasonFilters.length,age:e.ageFilters.length,total:d.length}),[e,d]),h=(0,l.useCallback)(t=>e[eA(t)],[e]),x=(0,l.useCallback)((t,r)=>({totalRecords:t,filteredRecords:r,activeFilterCount:u.total,filterMode:e.filterMode}),[u.total,e.filterMode]),m=(0,l.useCallback)(()=>{t(eD)},[]),p=(0,l.useCallback)(e=>{t(t=>({...t,filterMode:e}))},[]),b=(0,l.useCallback)((e,r)=>{t(t=>{let i=eA(e),s=t[i],l=r.filter(e=>!s.includes(e));return{...t,[i]:[...s,...l]}})},[]),j=(0,l.useCallback)(()=>({time:e.timeFilters.join(","),reason:e.reasonFilters.join(","),age:e.ageFilters.join(","),mode:e.filterMode}),[e]);return{filterState:e,filterActions:{addFilter:r,removeFilter:i,clearFilters:s,clearFilterType:n,toggleFilterMode:o,isFilterActive:a},addFilters:b,resetFilters:m,setFilterMode:p,activeFilters:d,hasActiveFilters:c,filterCounts:u,getFiltersForType:h,createFilterSummary:x,getSerializableState:j,restoreFromSerializable:(0,l.useCallback)(e=>{t({timeFilters:e.time?e.time.split(",").filter(Boolean):[],reasonFilters:e.reason?e.reason.split(",").filter(Boolean):[],ageFilters:e.age?e.age.split(",").filter(Boolean):[],filterMode:e.mode||"AND"})},[])}};function eA(e){switch(e){case ec.TIME:return"timeFilters";case ec.REASON:return"reasonFilters";case ec.AGE:return"ageFilters";default:throw Error(`Unknown filter type: ${e}`)}}var eT=r(71618),eR=r(64889),eI=r(30925),eF=r(74542),eM=r(81839),eN=r(17047),eP=r(95438),eL=r(21183),eE=r(52104),eB=r(98913),eG=r(60583),e$=r(60187),eO=r(13319),eW=r(56604),eH=r(4176),ez=r(41606),eV=r(89425);let eK=e=>new Intl.DateTimeFormat("en-GB",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}).format(e),eU=e=>new Intl.DateTimeFormat("en-GB",{hour:"2-digit",minute:"2-digit"}).format(e),e_=e=>{if(e.Age&&e.Age>0)return e.Age;if(e.DOB){let t=new Date,r=new Date(e.DOB),i=t.getFullYear()-r.getFullYear(),s=t.getMonth()-r.getMonth();return(s<0||0===s&&t.getDate()<r.getDate())&&i--,i}return 0},eq=e=>`${e.FirstName} ${e.Surname}`.trim(),eY=e=>new Date(e.Attended),eQ=e=>{switch(e){case ec.TIME:return(0,s.jsx)(eW.Z,{});case ec.REASON:return(0,s.jsx)(eH.Z,{});case ec.AGE:return(0,s.jsx)(ez.Z,{});default:return(0,s.jsx)(eH.Z,{})}},eX=e=>{switch(e){case ec.TIME:return"primary";case ec.REASON:return"secondary";case ec.AGE:default:return"default"}},eJ=e=>{let{open:t,onClose:r,data:i,filterValue:l,filterType:o}=e,a=(0,eh.Z)(),d=(0,eT.Z)(a.breakpoints.down("md")),c=eg(o,l,i.length),u=eQ(o),h=eX(o);return(0,s.jsxs)(eR.Z,{open:t,onClose:r,maxWidth:"lg",fullWidth:!0,fullScreen:d,PaperProps:{sx:{minHeight:d?"100vh":"60vh",maxHeight:d?"100vh":"90vh"}},children:[(0,s.jsxs)(eI.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",pb:1},children:[(0,s.jsxs)(n.Z,{sx:{display:"flex",alignItems:"center",gap:1},children:[u,(0,s.jsx)(g.Z,{variant:"h6",component:"div",children:c})]}),(0,s.jsx)(f.Z,{onClick:r,size:"small","aria-label":"Close modal",children:(0,s.jsx)(eV.Z,{})})]}),(0,s.jsx)(eF.Z,{}),(0,s.jsxs)(eM.Z,{sx:{p:0},children:[(0,s.jsx)(n.Z,{sx:{p:2,pb:1},children:(0,s.jsx)(eN.Z,{icon:u,label:`Filter: ${l}`,color:h,variant:"outlined",size:"small"})}),(0,s.jsx)(eP.Z,{component:b.Z,elevation:0,sx:{maxHeight:d?"calc(100vh - 200px)":"60vh","& .MuiTableCell-root":{borderBottom:`1px solid ${a.palette.divider}`}},children:(0,s.jsxs)(eL.Z,{stickyHeader:!0,"aria-label":"Filtered admissions table",children:[(0,s.jsx)(eE.Z,{children:(0,s.jsxs)(eB.Z,{children:[(0,s.jsx)(eG.Z,{children:(0,s.jsx)(g.Z,{variant:"subtitle2",fontWeight:"bold",children:"Name"})}),(0,s.jsx)(eG.Z,{children:(0,s.jsx)(g.Z,{variant:"subtitle2",fontWeight:"bold",children:"Age"})}),(0,s.jsx)(eG.Z,{children:(0,s.jsx)(g.Z,{variant:"subtitle2",fontWeight:"bold",children:"Admission Time"})}),(0,s.jsx)(eG.Z,{children:(0,s.jsx)(g.Z,{variant:"subtitle2",fontWeight:"bold",children:"Reason"})}),!d&&(0,s.jsx)(eG.Z,{children:(0,s.jsx)(g.Z,{variant:"subtitle2",fontWeight:"bold",children:"Date"})})]})}),(0,s.jsx)(e$.Z,{children:i.map(e=>(0,s.jsxs)(eB.Z,{hover:!0,sx:{"&:nth-of-type(odd)":{backgroundColor:a.palette.action.hover}},children:[(0,s.jsx)(eG.Z,{children:(0,s.jsx)(g.Z,{variant:"body2",children:eq(e)})}),(0,s.jsx)(eG.Z,{children:(0,s.jsx)(g.Z,{variant:"body2",children:e_(e)})}),(0,s.jsx)(eG.Z,{children:(0,s.jsx)(g.Z,{variant:"body2",children:eU(eY(e))})}),(0,s.jsx)(eG.Z,{children:(0,s.jsx)(eN.Z,{label:e.ReasonCategory,size:"small",variant:"outlined",sx:{fontSize:"0.75rem",height:"24px"}})}),!d&&(0,s.jsx)(eG.Z,{children:(0,s.jsx)(g.Z,{variant:"body2",color:"text.secondary",children:eK(eY(e))})})]},e._id))})]})}),0===i.length&&(0,s.jsxs)(n.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"200px",textAlign:"center",p:3},children:[(0,s.jsx)(g.Z,{variant:"h6",color:"text.secondary",gutterBottom:!0,children:"No admissions found"}),(0,s.jsx)(g.Z,{variant:"body2",color:"text.secondary",children:"There are no admission records matching the selected filter criteria."})]})]}),(0,s.jsx)(eF.Z,{}),(0,s.jsxs)(eO.Z,{sx:{p:2},children:[(0,s.jsxs)(g.Z,{variant:"body2",color:"text.secondary",sx:{flexGrow:1},children:["Showing ",i.length," ",1===i.length?"record":"records"]}),(0,s.jsx)(ew.Z,{onClick:r,variant:"contained",children:"Close"})]})]})},e0=e=>{let{admissions:t,selectedItems:r,onSelectAll:i,onSelectItem:n,onDelete:o,onUpdateAdmission:a}=e,{filterState:d,filterActions:c}=ek(),[u,h]=(0,l.useState)(!1),[x,m]=(0,l.useState)(null),[p,b]=(0,l.useState)(!1),[j,g]=(0,l.useState)(null),[y,f]=(0,l.useState)(""),[v,w]=(0,l.useState)(new Date().toISOString().slice(0,16)),[Z,C]=(0,l.useState)(!1),S=(0,l.useMemo)(()=>(function(e,t){let r=function(e,t){let{timeFilters:r,reasonFilters:i,ageFilters:s,filterMode:l}=t;return 0===r.length&&0===i.length&&0===s.length?e:e.filter(e=>{var t,n,o;let a=0===r.length||function(e,t){let r=e.Attended||e.createdAt;if(!r)return!1;let i=new Date(r).getHours();return t.some(e=>{let t=function(e){if(e.includes("-")){let[t,r]=e.split("-");return{start:parseInt(t.split(":")[0]),end:parseInt(r.split(":")[0])}}{let t=parseInt(e);return{start:t,end:t+1}}}(e);return i>=t.start&&i<t.end})}(e,r),d=0===i.length||(t=e,i.includes(t.ReasonCategory)),c=0===s.length||(n=e,o=s,!!n.Age&&o.some(e=>{let t=function(e){if(e.includes("-")){let[t,r]=e.split("-");return{min:parseInt(t),max:parseInt(r)}}if(e.includes("+"))return{min:parseInt(e.replace("+","")),max:999};{let t=parseInt(e);return{min:t,max:t}}}(e);return n.Age>=t.min&&n.Age<=t.max}));if("AND"===l)return a&&d&&c;{let e=r.length>0,t=i.length>0,l=s.length>0;return!!e&&a||!!t&&d||!!l&&c}})}(e,t),i=function(e){let t={};for(let e=0;e<24;e++)t[e]=0;return e.forEach(e=>{let r=e.Attended||e.createdAt;if(!r)return;let i=new Date(r).getHours();t[i]++}),Object.entries(t).map(e=>{let[t,r]=e;return{hour:parseInt(t),count:r,label:`${t.padStart(2,"0")}:00`}})}(r),s=function(e){let t={};return e.forEach(e=>{let r=e.ReasonCategory;t[r]=(t[r]||0)+1}),Object.entries(t).map(e=>{let[t,r]=e;return{reason:t,count:r}})}(r),l=function(e){let t=[{label:"Under 18",min:0,max:17},{label:"18-25",min:18,max:25},{label:"26-35",min:26,max:35},{label:"36-45",min:36,max:45},{label:"46-55",min:46,max:55},{label:"56-65",min:56,max:65},{label:"65+",min:66,max:999}],r={};return t.forEach(e=>{r[e.label]=0}),e.forEach(e=>{if(e.Age){let i=t.find(t=>e.Age>=t.min&&e.Age<=t.max);i&&r[i.label]++}}),Object.entries(r).map(e=>{let[t,r]=e;return{ageRange:t,count:r}})}(r),n={totalRecords:e.length,filteredRecords:r.length,activeFilterCount:t.timeFilters.length+t.reasonFilters.length+t.ageFilters.length,filterMode:t.filterMode};return{filteredAdmissions:r,timeData:i,reasonData:s,ageData:l,filterSummary:n}})(t,d),[t,d]),D=async()=>{if(j&&a&&y.trim()){C(!0);try{let e={...j,DischargeTime:v,AdditionalNotes:[...j.AdditionalNotes,{timestamp:v,note:`Discharge Notes: ${y}`,author:"Staff"}],History:[...j.History,{timestamp:v,action:"Discharged",details:y,author:"Staff"}],InBayNow:!1,status:"discharged"};await a(e),k()}catch(e){}finally{C(!1)}}},k=()=>{b(!1),g(null),f(""),w(new Date().toISOString().slice(0,16))};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(X.Z,{container:!0,spacing:3,children:[(0,s.jsx)(X.Z,{size:{xs:12},children:(0,s.jsx)(ey,{admissions:t,filterState:d,filterActions:c,filteredData:S,onLongPressModal:(e,t,r)=>{m({data:e,filterValue:t,filterType:r}),h(!0)}})}),(0,s.jsx)(X.Z,{size:{xs:12},children:(0,s.jsx)(eC,{admissions:S.filteredAdmissions,selectedItems:r,onSelectAll:i,onSelectItem:n,onDelete:o,onDischarge:e=>{g(e),w(new Date().toISOString().slice(0,16)),f(""),b(!0)}})})]}),x&&(0,s.jsx)(eJ,{open:u,onClose:()=>{h(!1),m(null)},data:x.data,filterValue:x.filterValue,filterType:x.filterType}),(0,s.jsx)(eS.n,{showReAdmitModal:!1,showConfirmReAdmit:!1,showConfirmDischarge:p,reAdmitLocation:0,reAdmitNotes:"",dischargeNotes:y,dischargeTime:v,bayStatus:null,isSubmitting:Z,onReAdmitLocationChange:()=>{},onReAdmitNotesChange:()=>{},onDischargeNotesChange:f,onDischargeTimeChange:w,onCloseReAdmit:()=>{},onCloseConfirmReAdmit:()=>{},onCloseConfirmDischarge:k,onConfirmReAdmit:()=>{},onConfirmDischarge:D,onShowConfirmReAdmit:()=>{}})]})},e1=e=>{let{itemCounts:t}=e,r=G(t);return(0,s.jsx)(X.Z,{container:!0,spacing:3,children:(0,s.jsx)(X.Z,{size:{xs:12},children:(0,s.jsxs)(b.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2},children:[(0,s.jsx)(g.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Items by Type"}),(0,s.jsx)("div",{style:{height:300,width:"100%"},children:(0,s.jsx)(J.h,{children:(0,s.jsxs)(ee.v,{data:r,children:[(0,s.jsx)(et.q,{strokeDasharray:"3 3"}),(0,s.jsx)(er.K,{dataKey:"type"}),(0,s.jsx)(ei.B,{}),(0,s.jsx)(es.u,{}),(0,s.jsx)(eo.D,{}),(0,s.jsx)(el.$,{dataKey:"count",name:"Count",children:r.map((e,t)=>(0,s.jsx)(en.b,{fill:N[t%N.length]},`cell-${t}`))})]})})})]})})})},e2=["Sanitizer","ToiletRoll","Suncream","Poncho","Earplugs","Condoms","ChildrensWristbands","GeneralWristbands","Water","Charging","SanitaryProducts","GeneralEnqs"],e3=e=>{let{itemCounts:t,selectedItems:r,onSelectAll:i,onSelectItem:o,onDelete:a}=e,[d,c]=(0,l.useState)(!1),[u,h]=(0,l.useState)(null),x=e=>{h(e),c(!0)},m=[{field:"items",headerName:"Items",flex:2,renderCell:e=>{let t=e.row;return t?e2.filter(e=>t[e]).map(e=>`${e}: ${t[e]}`).join(", "):""}},{field:"totalCount",headerName:"Total Count",flex:1,renderCell:e=>{let t=e.row;return t?e2.reduce((e,r)=>e+(t[r]||0),0):0}},{field:"createdAt",headerName:"Timestamp",flex:1,renderCell:e=>{let t=e.row;return t.createdAt?new Date(t.createdAt).toLocaleString():"N/A"}}];return(0,s.jsxs)(b.Z,{elevation:3,sx:{bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,p:3},children:[(0,s.jsxs)(n.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[(0,s.jsx)(g.Z,{variant:"h6",color:"primary",children:"Raw Data"}),r.length>0&&(0,s.jsxs)(ew.Z,{variant:"contained",color:"error",onClick:a,children:["Delete Selected (",r.length,")"]})]}),(0,s.jsx)("div",{style:{height:400,width:"100%"},children:(0,s.jsx)(ef._,{rows:t||[],columns:m,getRowId:e=>e._id,checkboxSelection:!0,disableRowSelectionOnClick:!0,rowSelectionModel:{type:"include",ids:new Set(r)},onRowSelectionModelChange:e=>{let t=Array.from(e.ids).map(e=>e.toString());t.forEach(e=>{r.includes(e)||o(e)}),r.forEach(e=>{t.includes(e)||o(e)})},density:"compact",slots:{toolbar:ev.n},slotProps:{toolbar:{showQuickFilter:!0,quickFilterProps:{debounceMs:300}}},initialState:{sorting:{sortModel:[{field:"createdAt",sort:"desc"}]}},onRowClick:e=>x(e.row),sx:{"& .MuiDataGrid-cell":{fontSize:"0.875rem"},"& .MuiDataGrid-toolbarContainer":{padding:"8px 24px"},"& .MuiDataGrid-toolbar":{"& .MuiFormControl-root":{width:"100%",maxWidth:"300px"}},"& .MuiDataGrid-row":{cursor:"pointer"},border:"none",bgcolor:"background.paper",backdropFilter:"blur(8px)"}})}),(0,s.jsx)(eZ.k,{open:d,onClose:()=>{c(!1)},record:u,recordType:"itemCount"})]})},e6=e=>{let{itemCounts:t,selectedItems:r,onSelectAll:i,onSelectItem:l,onDelete:n}=e;return(0,s.jsxs)(X.Z,{container:!0,spacing:3,children:[(0,s.jsx)(X.Z,{size:{xs:12},children:(0,s.jsx)(e1,{itemCounts:t})}),(0,s.jsx)(X.Z,{size:{xs:12},children:(0,s.jsx)(e3,{itemCounts:t,selectedItems:r,onSelectAll:i,onSelectItem:l,onDelete:n})})]})},e8=e=>{let{items:t}=e,r=$(t),i=O(t),l=W(t);return(0,s.jsxs)(X.Z,{container:!0,spacing:3,children:[(0,s.jsx)(X.Z,{size:{xs:12},children:(0,s.jsxs)(b.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2},children:[(0,s.jsx)(g.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Lost Property by Time of Day"}),(0,s.jsx)("div",{style:{height:300,width:"100%"},children:(0,s.jsx)(J.h,{children:(0,s.jsxs)(ee.v,{data:r,children:[(0,s.jsx)(et.q,{strokeDasharray:"3 3"}),(0,s.jsx)(er.K,{dataKey:"label"}),(0,s.jsx)(ei.B,{}),(0,s.jsx)(es.u,{formatter:e=>[e,"Items"]}),(0,s.jsx)(el.$,{dataKey:"count",fill:"#662D91",name:"Items",children:r.map((e,t)=>(0,s.jsx)(en.b,{fill:e.count>0?"#662D91":"#E0E0E0"},`cell-${t}`))})]})})})]})}),(0,s.jsx)(X.Z,{size:{xs:12,md:6},children:(0,s.jsxs)(b.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,height:"100%"},children:[(0,s.jsx)(g.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Lost Property by Category"}),(0,s.jsx)("div",{style:{height:300,width:"100%"},children:(0,s.jsx)(J.h,{children:(0,s.jsxs)(ee.v,{data:i,children:[(0,s.jsx)(et.q,{strokeDasharray:"3 3"}),(0,s.jsx)(er.K,{dataKey:"category"}),(0,s.jsx)(ei.B,{}),(0,s.jsx)(es.u,{}),(0,s.jsx)(eo.D,{}),(0,s.jsx)(el.$,{dataKey:"count",name:"Count",children:i.map((e,t)=>(0,s.jsx)(en.b,{fill:N[t%N.length]},`cell-${t}`))})]})})})]})}),(0,s.jsx)(X.Z,{size:{xs:12,md:6},children:(0,s.jsxs)(b.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,height:"100%"},children:[(0,s.jsx)(g.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Lost Property Status"}),(0,s.jsx)("div",{style:{height:300,width:"100%"},children:(0,s.jsx)(J.h,{children:(0,s.jsxs)(ea.u,{children:[(0,s.jsx)(ed.b,{data:l,dataKey:"count",nameKey:"status",cx:"50%",cy:"50%",outerRadius:100,label:!0,children:l.map((e,t)=>(0,s.jsx)(en.b,{fill:N[t%N.length]},`cell-${t}`))}),(0,s.jsx)(es.u,{}),(0,s.jsx)(eo.D,{})]})})})]})})]})},e5=e=>{let{items:t,selectedItems:r,onSelectAll:i,onSelectItem:o,onDelete:a}=e,[d,c]=(0,l.useState)(!1),[u,h]=(0,l.useState)(null),x=e=>{h(e),c(!0)},m=[{field:"description",headerName:"Description",flex:2,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row?(e.row.description||"").toLowerCase():"",renderCell:e=>{let{row:t}=e;return t.description||"N/A"}},{field:"category",headerName:"Category",flex:1,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row?(e.row.category||"").toLowerCase():"",renderCell:e=>{let{row:t}=e;return t.category||"N/A"}},{field:"status",headerName:"Status",flex:1,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"claimed"===e.row.status?"claimed":"unclaimed",renderCell:e=>{let{row:t}=e;return"claimed"===t.status?"Claimed":"Unclaimed"}},{field:"timeFound",headerName:"Time Found",flex:1,sortable:!0,filterable:!0,type:"dateTime",valueGetter:e=>{if(!e||!e.row||"object"!=typeof e.row||!("timeFound"in e.row))return null;let t=e.row;return t.timeFound?new Date(t.timeFound):null},renderCell:e=>{let{row:t}=e;return t.timeFound?new Date(t.timeFound).toLocaleString():"N/A"}},{field:"whereFound",headerName:"Where Found",flex:1,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row?(e.row.whereFound||"").toLowerCase():"",renderCell:e=>{let{row:t}=e;return t.whereFound||"N/A"}}];return(0,s.jsxs)(b.Z,{elevation:3,sx:{bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,p:3},children:[(0,s.jsxs)(n.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[(0,s.jsx)(g.Z,{variant:"h6",color:"primary",children:"Raw Data"}),r.length>0&&(0,s.jsxs)(ew.Z,{variant:"contained",color:"error",onClick:a,children:["Delete Selected (",r.length,")"]})]}),(0,s.jsx)("div",{style:{height:400,width:"100%"},children:(0,s.jsx)(ef._,{rows:(t||[]).filter(Boolean),columns:m,getRowId:e=>e._id,checkboxSelection:!0,disableRowSelectionOnClick:!0,rowSelectionModel:{type:"include",ids:new Set(r)},onRowSelectionModelChange:e=>{let t=Array.from(e.ids).map(e=>e.toString());t.forEach(e=>{r.includes(e)||o(e)}),r.forEach(e=>{t.includes(e)||o(e)})},density:"compact",slots:{toolbar:ev.n},slotProps:{toolbar:{showQuickFilter:!0,quickFilterProps:{debounceMs:300}}},initialState:{sorting:{sortModel:[{field:"timeFound",sort:"desc"}]}},onRowClick:e=>x(e.row),sx:{"& .MuiDataGrid-cell":{fontSize:"0.875rem"},"& .MuiDataGrid-toolbarContainer":{padding:"8px 24px"},"& .MuiDataGrid-toolbar":{"& .MuiFormControl-root":{width:"100%",maxWidth:"300px"}},"& .MuiDataGrid-row":{cursor:"pointer"},border:"none",bgcolor:"background.paper",backdropFilter:"blur(8px)"}})}),(0,s.jsx)(eZ.k,{open:d,onClose:()=>{c(!1)},record:u,recordType:"lostProperty"})]})},e9=e=>{let{items:t,selectedItems:r,onSelectAll:i,onSelectItem:l,onDelete:n}=e;return(0,s.jsxs)(X.Z,{container:!0,spacing:3,children:[(0,s.jsx)(X.Z,{size:{xs:12},children:(0,s.jsx)(e8,{items:t})}),(0,s.jsx)(X.Z,{size:{xs:12},children:(0,s.jsx)(e5,{items:t,selectedItems:r,onSelectAll:i,onSelectItem:l,onDelete:n})})]})};var e4=r(62983),e7=r(11161),te=r(19410);let tt=e=>{let t=Array.from({length:24},(e,t)=>({hour:t,label:`${t.toString().padStart(2,"0")}:00`,count:0}));return e.forEach(e=>{if(e.visitTimestamp){let r=(0,M.p)((0,u.D)(e.visitTimestamp));t[r].count++}}),t},tr=e=>Object.entries(e.reduce((e,t)=>{let r="look_around"===t.purpose?"Look Around":"Use Service";return e[r]=(e[r]||0)+1,e},{})).map(e=>{let[t,r]=e;return{purpose:t,count:r}}),ti=e=>Object.entries(e.reduce((e,t)=>{let r="crew"===t.userType?"Crew":"Public";return e[r]=(e[r]||0)+1,e},{})).map(e=>{let[t,r]=e;return{userType:t,count:r}}),ts=e=>Object.entries(e.filter(e=>"crew"===e.userType&&e.teamName).reduce((e,t)=>{let r=t.teamName||"Unknown Team";return e[r]=(e[r]||0)+1,e},{})).map(e=>{let[t,r]=e;return{team:t,count:r}}),tl=e=>{let{visits:t}=e,[r,i]=(0,l.useState)(null),[o,a]=(0,l.useState)(null),[d,c]=(0,l.useState)(!1),[h,x]=(0,l.useState)(!1),m=e=>{a(e),x(!0)},p=null!==r?t.filter(e=>!!e.visitTimestamp&&(0,M.p)((0,u.D)(e.visitTimestamp))===r):[];return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(X.Z,{container:!0,spacing:3,children:[(0,s.jsx)(X.Z,{size:{xs:12},children:(0,s.jsxs)(b.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2},children:[(0,s.jsx)(g.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Visits by Time of Day"}),(0,s.jsx)("div",{style:{height:300,width:"100%"},children:(0,s.jsx)(J.h,{children:(0,s.jsxs)(ee.v,{data:tt(t),children:[(0,s.jsx)(et.q,{strokeDasharray:"3 3"}),(0,s.jsx)(er.K,{dataKey:"label",interval:2}),(0,s.jsx)(ei.B,{}),(0,s.jsx)(es.u,{formatter:e=>[e,"Visits"]}),(0,s.jsx)(el.$,{dataKey:"count",fill:"#662D91",name:"Visits",onClick:e=>{i(e.hour),c(!0)},cursor:"pointer",children:tt(t).map((e,t)=>(0,s.jsx)(en.b,{fill:e.count>0?"#662D91":"#E0E0E0"},`cell-${t}`))})]})})})]})}),(0,s.jsx)(X.Z,{size:{xs:12,md:6},children:(0,s.jsxs)(b.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,height:"100%"},children:[(0,s.jsx)(g.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Visits by Purpose"}),(0,s.jsx)("div",{style:{height:300,width:"100%"},children:(0,s.jsx)(J.h,{children:(0,s.jsxs)(ea.u,{children:[(0,s.jsx)(ed.b,{data:tr(t),dataKey:"count",nameKey:"purpose",cx:"50%",cy:"50%",outerRadius:100,label:!0,children:tr(t).map((e,t)=>(0,s.jsx)(en.b,{fill:N[t%N.length]},`cell-${t}`))}),(0,s.jsx)(es.u,{}),(0,s.jsx)(eo.D,{})]})})})]})}),(0,s.jsx)(X.Z,{size:{xs:12,md:6},children:(0,s.jsxs)(b.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,height:"100%"},children:[(0,s.jsx)(g.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Visits by User Type"}),(0,s.jsx)("div",{style:{height:300,width:"100%"},children:(0,s.jsx)(J.h,{children:(0,s.jsxs)(ea.u,{children:[(0,s.jsx)(ed.b,{data:ti(t),dataKey:"count",nameKey:"userType",cx:"50%",cy:"50%",outerRadius:100,label:!0,children:ti(t).map((e,t)=>(0,s.jsx)(en.b,{fill:N[t%N.length]},`cell-${t}`))}),(0,s.jsx)(es.u,{}),(0,s.jsx)(eo.D,{})]})})})]})}),ts(t).length>0&&(0,s.jsx)(X.Z,{size:{xs:12},children:(0,s.jsxs)(b.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2},children:[(0,s.jsx)(g.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Crew Visits by Team"}),(0,s.jsx)("div",{style:{height:300,width:"100%"},children:(0,s.jsx)(J.h,{children:(0,s.jsxs)(ee.v,{data:ts(t),children:[(0,s.jsx)(et.q,{strokeDasharray:"3 3"}),(0,s.jsx)(er.K,{dataKey:"team"}),(0,s.jsx)(ei.B,{}),(0,s.jsx)(es.u,{}),(0,s.jsx)(eo.D,{}),(0,s.jsx)(el.$,{dataKey:"count",name:"Visits",children:ts(t).map((e,t)=>(0,s.jsx)(en.b,{fill:N[t%N.length]},`cell-${t}`))})]})})})]})})]}),(0,s.jsxs)(eR.Z,{open:d,onClose:()=>c(!1),maxWidth:"md",fullWidth:!0,PaperProps:{sx:{borderRadius:2,bgcolor:"background.paper"}},children:[(0,s.jsxs)(eI.Z,{children:[(0,s.jsx)(g.Z,{variant:"h5",fontWeight:"bold",children:null!==r?`Visits at ${tt(t).find(e=>e.hour===r)?.label||""}`:"Visits"}),(0,s.jsxs)(g.Z,{variant:"subtitle1",color:"text.secondary",children:[p.length," ",1===p.length?"visit":"visits"," recorded"]})]}),(0,s.jsx)(eM.Z,{dividers:!0,children:p.length>0?(0,s.jsx)(e4.Z,{children:p.map((e,t)=>(0,s.jsxs)(l.Fragment,{children:[(0,s.jsx)(e7.ZP,{component:"button",onClick:()=>m(e),sx:{borderRadius:1,"&:hover":{bgcolor:"action.hover"},transition:"background-color 0.2s"},children:(0,s.jsx)(te.Z,{primary:(0,s.jsx)(g.Z,{variant:"subtitle1",fontWeight:"medium",children:"look_around"===e.purpose?"Look Around":"Use Service"}),secondary:(0,s.jsxs)(n.Z,{children:[(0,s.jsxs)(g.Z,{variant:"body2",component:"span",children:["crew"===e.userType?"Crew":"Public",e.teamName&&` - ${e.teamName}`]}),(0,s.jsx)(g.Z,{variant:"body2",component:"div",color:"text.secondary",children:new Date(e.visitTimestamp).toLocaleString()})]})})}),t<p.length-1&&(0,s.jsx)(eF.Z,{component:"li"})]},e._id||t))}):(0,s.jsx)(g.Z,{variant:"body1",align:"center",sx:{py:2},children:"No visit details available for this time period."})}),(0,s.jsx)(eO.Z,{children:(0,s.jsx)(ew.Z,{onClick:()=>c(!1),variant:"contained",children:"Close"})})]}),(0,s.jsxs)(eR.Z,{open:h,onClose:()=>x(!1),maxWidth:"sm",fullWidth:!0,PaperProps:{sx:{borderRadius:2,bgcolor:"background.paper"}},children:[(0,s.jsx)(eI.Z,{children:(0,s.jsx)(g.Z,{variant:"h5",fontWeight:"bold",children:"Visit Details"})}),(0,s.jsx)(eM.Z,{dividers:!0,children:o&&(0,s.jsxs)(n.Z,{children:[(0,s.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,s.jsx)("strong",{children:"Purpose:"})," ","look_around"===o.purpose?"Look Around":"Use Service"]}),(0,s.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,s.jsx)("strong",{children:"User Type:"})," ","crew"===o.userType?"Crew":"Public"]}),o.teamName&&(0,s.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,s.jsx)("strong",{children:"Team:"})," ",o.teamName]}),(0,s.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,s.jsx)("strong",{children:"Visit Time:"})," ",new Date(o.visitTimestamp).toLocaleString()]}),(0,s.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,s.jsx)("strong",{children:"Festival ID:"})," ",o.festivalId]}),o.siteLocationId&&(0,s.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,s.jsx)("strong",{children:"Site Location:"})," ",o.siteLocationId]})]})}),(0,s.jsx)(eO.Z,{children:(0,s.jsx)(ew.Z,{onClick:()=>x(!1),variant:"contained",children:"Close"})})]})]})},tn=e=>{let{visits:t,selectedItems:r,onSelectAll:i,onSelectItem:o,onDelete:a}=e,[d,c]=(0,l.useState)(!1),[u,h]=(0,l.useState)(null),x=e=>{h(e),c(!0)},m=()=>{c(!1)},p=[{field:"visitTimestamp",headerName:"Visit Time",flex:1,sortable:!0,filterable:!0,type:"dateTime",valueGetter:e=>e&&e.row&&"object"==typeof e.row&&"visitTimestamp"in e.row&&e.row.visitTimestamp?new Date(e.row.visitTimestamp):null,renderCell:e=>{if(!e||!e.row||"object"!=typeof e.row||!("visitTimestamp"in e.row))return"N/A";let t=e.row;return t.visitTimestamp?new Date(t.visitTimestamp).toLocaleString():"N/A"}},{field:"purpose",headerName:"Purpose",width:150,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.purpose||"n/a",renderCell:e=>{let t=e.row,r="look_around"===t.purpose?"Look Around":"Use Service";return(0,s.jsx)(eN.Z,{label:r,color:"use_service"===t.purpose?"primary":"default",variant:"outlined",size:"small"})}},{field:"userType",headerName:"User Type",width:120,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.userType||"n/a",renderCell:e=>{let t=e.row,r="crew"===t.userType?"Crew":"Public";return(0,s.jsx)(eN.Z,{label:r,color:"crew"===t.userType?"secondary":"default",variant:"filled",size:"small"})}},{field:"teamName",headerName:"Team",flex:1,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.teamName||"n/a",renderCell:e=>{let t=e.row;return t.teamName||("crew"===t.userType?"No Team":"N/A")}},{field:"festivalId",headerName:"Festival",width:120,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.festivalId||"n/a",renderCell:e=>{let t=e.row;return t.festivalId?t.festivalId.split("_").pop()||t.festivalId:"N/A"}},{field:"siteLocationId",headerName:"Site",width:100,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.siteLocationId||"n/a",renderCell:e=>e.row.siteLocationId||"N/A"}];return(0,s.jsxs)(b.Z,{elevation:3,sx:{bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,p:3},children:[(0,s.jsxs)(n.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[(0,s.jsx)(g.Z,{variant:"h6",color:"primary",children:"Raw Data"}),r.length>0&&(0,s.jsxs)(ew.Z,{variant:"contained",color:"error",onClick:a,children:["Delete Selected (",r.length,")"]})]}),(0,s.jsx)("div",{style:{height:400,width:"100%"},children:(0,s.jsx)(ef._,{rows:t||[],columns:p,getRowId:e=>e._id,checkboxSelection:!0,disableRowSelectionOnClick:!0,rowSelectionModel:{type:"include",ids:new Set(r)},onRowSelectionModelChange:e=>{let t=Array.from(e.ids).map(e=>e.toString());t.forEach(e=>{r.includes(e)||o(e)}),r.forEach(e=>{t.includes(e)||o(e)})},density:"compact",slots:{toolbar:ev.n},slotProps:{toolbar:{showQuickFilter:!0,quickFilterProps:{debounceMs:300}}},initialState:{sorting:{sortModel:[{field:"visitTimestamp",sort:"desc"}]}},onRowClick:e=>x(e.row),sx:{"& .MuiDataGrid-cell":{fontSize:"0.875rem"},"& .MuiDataGrid-toolbarContainer":{padding:"8px 24px"},"& .MuiDataGrid-toolbar":{"& .MuiFormControl-root":{width:"100%",maxWidth:"300px"}},"& .MuiDataGrid-row":{cursor:"pointer"},border:"none",bgcolor:"background.paper",backdropFilter:"blur(8px)"}})}),(0,s.jsxs)(eR.Z,{open:d,onClose:m,maxWidth:"sm",fullWidth:!0,PaperProps:{sx:{borderRadius:2,bgcolor:"background.paper"}},children:[(0,s.jsx)(eI.Z,{children:(0,s.jsx)(g.Z,{variant:"h5",fontWeight:"bold",children:"Sensory Hub Visit Details"})}),(0,s.jsx)(eM.Z,{dividers:!0,children:u&&(0,s.jsxs)(n.Z,{children:[(0,s.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,s.jsx)("strong",{children:"Purpose:"})," ","look_around"===u.purpose?"Look Around":"Use Service"]}),(0,s.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,s.jsx)("strong",{children:"User Type:"})," ","crew"===u.userType?"Crew":"Public"]}),u.teamName&&(0,s.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,s.jsx)("strong",{children:"Team:"})," ",u.teamName]}),(0,s.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,s.jsx)("strong",{children:"Visit Time:"})," ",new Date(u.visitTimestamp).toLocaleString()]}),(0,s.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,s.jsx)("strong",{children:"Festival ID:"})," ",u.festivalId]}),u.siteLocationId&&(0,s.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,s.jsx)("strong",{children:"Site Location:"})," ",u.siteLocationId]}),(0,s.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,s.jsx)("strong",{children:"Record ID:"})," ",u._id]}),u.createdAt&&(0,s.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,s.jsx)("strong",{children:"Created:"})," ",new Date(u.createdAt).toLocaleString()]})]})}),(0,s.jsx)(eO.Z,{children:(0,s.jsx)(ew.Z,{onClick:m,variant:"contained",children:"Close"})})]})]})},to=e=>{let{visits:t,selectedItems:r,onSelectAll:i,onSelectItem:l,onDelete:n}=e;return(0,s.jsxs)(X.Z,{container:!0,spacing:3,children:[(0,s.jsx)(X.Z,{size:{xs:12},children:(0,s.jsx)(tl,{visits:t})}),(0,s.jsx)(X.Z,{size:{xs:12},children:(0,s.jsx)(tn,{visits:t,selectedItems:r,onSelectAll:i,onSelectItem:l,onDelete:n})})]})};var ta=r(49696);let td=e=>{let{open:t,onClose:r,onConfirm:i,itemCount:l,itemType:a,loading:d=!1}=e;return(0,s.jsxs)(eR.Z,{open:t,onClose:r,maxWidth:"sm",fullWidth:!0,children:[(0,s.jsx)(eI.Z,{children:(0,s.jsxs)(n.Z,{display:"flex",alignItems:"center",gap:1,children:[(0,s.jsx)(ta.Z,{color:"warning"}),"Confirm Bulk Delete"]})}),(0,s.jsxs)(eM.Z,{children:[(0,s.jsx)(o.Z,{severity:"warning",sx:{mb:2},children:"This action cannot be undone"}),(0,s.jsxs)(g.Z,{variant:"body1",children:["Are you sure you want to delete ",(0,s.jsx)("strong",{children:l})," ",a," record",1!==l?"s":"","?"]}),(0,s.jsx)(g.Z,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"The selected records will be permanently removed from the system."})]}),(0,s.jsxs)(eO.Z,{children:[(0,s.jsx)(ew.Z,{onClick:r,disabled:d,children:"Cancel"}),(0,s.jsx)(ew.Z,{onClick:i,variant:"contained",color:"error",disabled:d,children:d?"Deleting...":`Delete ${l} Record${1!==l?"s":""}`})]})]})},tc=()=>{let{activeFestival:e}=(0,a.C)(),[t,r]=(0,l.useState)("all"),[i,c]=(0,l.useState)("admissions"),[u,h]=(0,l.useState)([]),[x,p]=(0,l.useState)(!1),[b,j]=(0,l.useState)(!1),{data:g,loading:y,error:f}=m(e?._id,t);if(!e)return(0,s.jsx)(n.Z,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"200px",children:(0,s.jsx)(o.Z,{severity:"info",children:"Please select a festival to view reports."})});let v=e=>{e&&(u.length===e.length?h([]):h(e.map(e=>e._id).filter(Boolean)))},w=e=>{h(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},Z=()=>{0!==u.length&&p(!0)},C=async()=>{if(0!==u.length){j(!0);try{switch(i){case"admissions":await d.R.bulkDeleteAdmissions(u);break;case"frontOfHouse":await d.R.bulkDeleteItems(u);break;case"lostProperty":await d.R.bulkDeleteLostProperty(u);break;case"sensoryHub":await d.R.bulkDeleteVisits(u)}h([]),p(!1)}catch(e){}finally{j(!1)}}},S=async e=>{try{let t={...e,type:"admission",documentType:"admission",syncStatus:"sync_pending",AdditionalNotes:e.AdditionalNotes||[],History:e.History||[],timestamp:e.timestamp||new Date().toISOString(),createdAt:e.createdAt||e.timestamp||new Date().toISOString(),updatedAt:new Date().toISOString()};await d.R.updateAdmission(t)}catch(e){}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(Q,{festival:e,selectedDay:t,onDayChange:r,activeTab:i,onTabChange:c,loading:y,error:f,data:g,children:(()=>{if(!g)return null;switch(i){case"admissions":return(0,s.jsx)(e0,{admissions:g.admissions||[],selectedItems:u,onSelectAll:v,onSelectItem:w,onDelete:Z,onUpdateAdmission:S});case"frontOfHouse":return(0,s.jsx)(e6,{itemCounts:g.itemCounts||[],selectedItems:u,onSelectAll:v,onSelectItem:w,onDelete:Z});case"lostProperty":return(0,s.jsx)(e9,{items:g.lostPropertyItems||[],selectedItems:u,onSelectAll:v,onSelectItem:w,onDelete:Z});case"sensoryHub":return(0,s.jsx)(to,{visits:g.sensoryHubVisits||[],selectedItems:u,onSelectAll:v,onSelectItem:w,onDelete:Z});default:return null}})()}),(0,s.jsx)(td,{open:x,onConfirm:C,onClose:()=>{p(!1)},itemCount:u.length,itemType:"admissions"===i?"admission":"frontOfHouse"===i?"item":"lostProperty"===i?"lost property item":"visit",loading:b})]})}},77761:function(){}}]);