/**
 * Tests for PersistentInitializationManager
 */

import { PersistentInitializationManager } from '../persistent-initialization-manager';

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    }
  };
})();

// Mock sessionStorage
const sessionStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    }
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
});

// Mock navigator
Object.defineProperty(window, 'navigator', {
  value: {
    userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
  }
});

describe('PersistentInitializationManager', () => {
  let manager: PersistentInitializationManager;

  beforeEach(() => {
    localStorageMock.clear();
    sessionStorageMock.clear();
    manager = new PersistentInitializationManager();
  });

  describe('Initial state', () => {
    it('should start with uninitialized state', () => {
      const state = manager.getState();
      expect(state.isInitialized).toBe(false);
      expect(state.hasLocalData).toBe(false);
      expect(state.syncCount).toBe(0);
      expect(state.lastSyncTime).toBe(0);
      expect(state.lastSuccessfulSync).toBe(0);
    });

    it('should require sync on first run', () => {
      expect(manager.shouldSkipSync()).toBe(false);
    });
  });

  describe('Sync lifecycle', () => {
    it('should track sync start', () => {
      const initialTime = Date.now();
      manager.markSyncStarted();
      
      const state = manager.getState();
      expect(state.syncCount).toBe(1);
      expect(state.lastSyncTime).toBeGreaterThanOrEqual(initialTime);
    });

    it('should track successful sync completion', () => {
      manager.markSyncStarted();
      manager.markSyncCompleted(true, 'v1.0');
      
      const state = manager.getState();
      expect(state.isInitialized).toBe(true);
      expect(state.hasLocalData).toBe(true);
      expect(state.dataVersion).toBe('v1.0');
      expect(state.syncCount).toBe(0); // Reset after successful sync
    });

    it('should track failed sync', () => {
      manager.markSyncStarted();
      manager.markSyncFailed();
      
      const state = manager.getState();
      expect(state.isInitialized).toBe(false);
      expect(state.syncCount).toBe(1); // Not reset on failure
    });
  });

  describe('Skip sync logic', () => {
    it('should skip sync when recently initialized', () => {
      manager.markSyncStarted();
      manager.markSyncCompleted(true);
      
      expect(manager.shouldSkipSync()).toBe(true);
    });

    it('should require sync when TTL expired', () => {
      manager.markSyncStarted();
      manager.markSyncCompleted(true);
      
      // Mock expired TTL by updating config
      manager.updateConfig({ initializationTTL: 0 });
      
      expect(manager.shouldSkipSync()).toBe(false);
    });

    it('should require sync when max page loads reached', () => {
      manager.markSyncStarted();
      manager.markSyncCompleted(true);
      
      // Simulate multiple page loads
      for (let i = 0; i < 15; i++) {
        manager.markSyncStarted();
      }
      
      expect(manager.shouldSkipSync()).toBe(false);
    });

    it('should require sync when no local data', () => {
      manager.markSyncStarted();
      manager.markSyncCompleted(false); // No data
      
      expect(manager.shouldSkipSync()).toBe(false);
    });
  });

  describe('Configuration management', () => {
    it('should update configuration', () => {
      const newConfig = { initializationTTL: 60000 };
      manager.updateConfig(newConfig);
      
      const config = manager.getConfig();
      expect(config.initializationTTL).toBe(60000);
    });

    it('should persist configuration to localStorage', () => {
      const newConfig = { initializationTTL: 60000 };
      manager.updateConfig(newConfig);
      
      // Create new manager to test persistence
      const newManager = new PersistentInitializationManager();
      const config = newManager.getConfig();
      expect(config.initializationTTL).toBe(60000);
    });
  });

  describe('State persistence', () => {
    it('should persist state to localStorage', () => {
      manager.markSyncStarted();
      manager.markSyncCompleted(true, 'v1.0');
      
      // Create new manager to test persistence
      const newManager = new PersistentInitializationManager();
      const state = newManager.getState();
      
      expect(state.isInitialized).toBe(true);
      expect(state.hasLocalData).toBe(true);
      expect(state.dataVersion).toBe('v1.0');
    });

    it('should handle corrupted localStorage gracefully', () => {
      // Corrupt the stored state
      localStorageMock.setItem('ithink_welfare_initialization_state', 'invalid json');
      
      // Should not throw and should use default state
      const newManager = new PersistentInitializationManager();
      const state = newManager.getState();
      
      expect(state.isInitialized).toBe(false);
    });
  });

  describe('Cache invalidation', () => {
    it('should invalidate cache', () => {
      manager.markSyncStarted();
      manager.markSyncCompleted(true);
      
      expect(manager.shouldSkipSync()).toBe(true);
      
      manager.invalidateCache();
      
      expect(manager.shouldSkipSync()).toBe(false);
    });

    it('should reset all state', () => {
      manager.markSyncStarted();
      manager.markSyncCompleted(true, 'v1.0');
      manager.updateConfig({ initializationTTL: 60000 });
      
      manager.reset();
      
      const state = manager.getState();
      const config = manager.getConfig();
      
      expect(state.isInitialized).toBe(false);
      expect(state.dataVersion).toBe('');
      expect(config.initializationTTL).toBe(30 * 60 * 1000); // Default value
    });
  });

  describe('Festival ID handling', () => {
    it('should invalidate cache when festival ID changes', () => {
      const managerWithFestival = new PersistentInitializationManager('festival1');
      managerWithFestival.markSyncStarted();
      managerWithFestival.markSyncCompleted(true);
      
      expect(managerWithFestival.shouldSkipSync()).toBe(true);
      
      // Create new manager with different festival ID
      const newManager = new PersistentInitializationManager('festival2');
      
      // Should require sync due to festival change
      expect(newManager.shouldSkipSync()).toBe(false);
    });
  });
});
