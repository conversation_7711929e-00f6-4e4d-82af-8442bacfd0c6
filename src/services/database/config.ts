import PouchDB from 'pouchdb-browser';
import PouchDBFind from 'pouchdb-find';

// Initialize PouchDB plugins
PouchDB.plugin(PouchDBFind);

// Database configuration types
interface DatabaseConfig {
  localName: string;
  remoteUrl: string;
  remoteName: string;
  username?: string;  // Basic auth for CouchDB
  password?: string;  // Basic auth for CouchDB
  selectiveSync?: SelectiveSyncConfig;
}

// Selective sync configuration
interface SelectiveSyncConfig {
  enabled: boolean;
  defaultFestivalScope?: string;
  documentTypeFilters: string[];
  locationFilters: string[];
  timeWindowDays: number;
  cacheSettings: {
    maxCacheSize: number;
    cacheExpirationHours: number;
    enablePersistentCache: boolean;
  };
  performanceSettings: {
    batchSize: number;
    maxConcurrentSyncs: number;
    indexHealthCheckInterval: number;
    volumeReductionTarget: number;
  };
  monitoringSettings: {
    enableMetrics: boolean;
    enablePerformanceTracking: boolean;
    logLevel: 'error' | 'warn' | 'info' | 'debug';
  };
}

// Default selective sync configuration
const defaultSelectiveSyncConfig: SelectiveSyncConfig = {
  enabled: false, // Disabled by default for gradual rollout
  documentTypeFilters: ['admission', 'festival', 'item', 'feedback', 'sensory_hub_visit'],
  locationFilters: [], // Empty means all locations
  timeWindowDays: 90, // 3 months of data
  cacheSettings: {
    maxCacheSize: 50 * 1024 * 1024, // 50MB cache limit
    cacheExpirationHours: 24, // 24 hour cache expiration
    enablePersistentCache: true
  },
  performanceSettings: {
    batchSize: 10, // Adaptive batch size starting point
    maxConcurrentSyncs: 3, // Maximum concurrent sync operations
    indexHealthCheckInterval: 300000, // 5 minutes
    volumeReductionTarget: 85 // Target 85% volume reduction
  },
  monitoringSettings: {
    enableMetrics: true,
    enablePerformanceTracking: true,
    logLevel: 'info'
  }
};

// Updated configuration - Using Cloudflare Worker proxy to resolve CORS issues
const defaultConfig: DatabaseConfig = {
  localName: 'ithinc_welfare',
  remoteUrl: 'https://database-proxy.brisflix.workers.dev/', // Worker proxy URL with CORS support
  remoteName: 'ithinc_welfare',
  // Note: Authentication is handled by the worker proxy, not directly by the app
  username: undefined,
  password: undefined,
  selectiveSync: defaultSelectiveSyncConfig
};

console.log('✅ Database config updated to use worker proxy for CORS compatibility');

// Current configuration
let currentConfig: DatabaseConfig = { ...defaultConfig };

// Load configuration from environment variables or secrets file
export const initializeConfig = async (): Promise<DatabaseConfig> => {
  // Try environment variables first
  if (process.env.ITHINC_DB_USERNAME && process.env.ITHINC_DB_PASSWORD) {
    console.log('Loading configuration from environment variables');
    currentConfig = {
      localName: process.env.ITHINC_DB_LOCAL_NAME || defaultConfig.localName,
      remoteUrl: process.env.ITHINC_DB_REMOTE_URL || defaultConfig.remoteUrl,
      remoteName: process.env.ITHINC_DB_REMOTE_NAME || defaultConfig.remoteName,
      username: process.env.ITHINC_DB_USERNAME,
      password: process.env.ITHINC_DB_PASSWORD,
      selectiveSync: {
        ...defaultSelectiveSyncConfig,
        enabled: process.env.REACT_APP_USE_SELECTIVE_SYNC === 'true'
      }
    };
    return currentConfig;
  }

  try {
    // If no environment variables, try secrets file as fallback
    const { secrets } = await import('../../config/secrets');
    
    // Check if secrets file has valid configuration
    if (secrets?.database?.username && secrets?.database?.password) {
      console.log('Loading configuration from secrets file');
      currentConfig = {
        localName: secrets.database.localName || defaultConfig.localName,
        remoteUrl: secrets.database.remoteUrl || defaultConfig.remoteUrl,
        remoteName: secrets.database.remoteName || defaultConfig.remoteName,
        username: secrets.database.username,
        password: secrets.database.password,
        selectiveSync: {
          ...defaultSelectiveSyncConfig,
          // Override with secrets file settings if available
          ...(secrets.database.selectiveSync || {})
        }
      };
      return currentConfig;
    }
  } catch (error) {
    // Log error but continue with defaults
    console.warn('Error loading secrets file:', error);
  }

  // If no valid configuration found, use defaults
  console.log('Using default configuration for direct CouchDB connection');
  return currentConfig;
};

// Get current configuration (synchronous)
export const getConfig = (): DatabaseConfig => currentConfig;

export const PRODUCTION_HOSTNAMES = [
  'welfare.brisflix.com',
  'ithink.brisflix.com',
  'www.ithink.brisflix.com',
  'ithink-welfare.brisflix.workers.dev',
  'localhost'
];

// PouchDB sync options - iPad-optimized for reliability
export const SYNC_OPTIONS = {
  live: false, // Disable continuous sync - only sync on demand
  retry: false, // Disable automatic retry
  ajax: {
    timeout: 10000, // INCREASED: From 3s to 10s to prevent premature timeouts
    withCredentials: true,
    cache: false, // Disable caching to avoid stale auth responses
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache'
    }
    // PERFORMANCE: Removed verbose logging to reduce overhead
  },
  batch_size: 10, // RATE LIMIT FIX: Reduced from 75 to prevent Cloudflare rate limiting on worker proxy
  batches_limit: 3, // RATE LIMIT FIX: Reduced from 5 to prevent overwhelming proxy. Total capacity: 30 concurrent requests.
  websocket: false, // Disable WebSocket
  heartbeat: false, // Disable heartbeat
  auto_compaction: true, // SYNC FIX: Enable compaction to prevent database bloat and conflicts
  revs_limit: 5, // Reduced from 100 to improve performance on iPads
  attachments: false
};

// Export database name for consistency
export const DB_NAME = 'ithinc_welfare';

// Constants for sync management - RELIABILITY OPTIMIZED
export const SYNC_CONSTANTS = {
  DEBOUNCE_DELAY: 20000, // Increased from 500ms to 20s to reduce sync frequency
  MAX_RETRY_ATTEMPTS: 3, // INCREASED: From 2 to 3 attempts for better reliability
  RETRY_DELAY_BASE: 1000, // INCREASED: From 500ms to 1s for more stable retries
  CONNECTION_TEST_INTERVAL: 300000, // Increased from 2min to 5min to reduce connection tests
  CHANGE_BATCH_TIMEOUT: 2000, // INCREASED: From 1s to 2s for more stable batching
  AUTH_RETRY_DELAY: 5000, // INCREASED: From 2s to 5s for auth retries
  AUTH_MAX_BACKOFF: 120000, // INCREASED: From 30s to 2min max backoff
  AUTH_SUPPRESSION_TIME: 300000, // INCREASED: From 1min to 5min suppression to prevent data loss appearance
  DATABASE_RESET_TIMEOUT: 15000, // INCREASED: From 10s to 15s timeout
  DATABASE_RESET_RETRY_ATTEMPTS: 3, // INCREASED: From 2 to 3 retry attempts
};

// Database reset configuration
export const DATABASE_RESET_CONFIG = {
  CONFIRMATION_REQUIRED: true, // Require confirmation before database reset
  BACKUP_BEFORE_RESET: false, // Whether to create backup before reset (not implemented)
  RESET_TIMEOUT: 30000, // Timeout for reset operations
  SYNC_AFTER_RESET: true, // Whether to sync after reset
  PRESERVE_USER_SETTINGS: true, // Whether to preserve user settings during reset
};

// Log configuration in development only
if (process.env.NODE_ENV === 'development') {
  console.log('Database Configuration:', {
    remoteUrl: currentConfig.remoteUrl,
    remoteName: currentConfig.remoteName,
    syncOptions: {
      ...SYNC_OPTIONS,
      // Don't log sensitive headers
      ajax: {
        ...SYNC_OPTIONS.ajax,
        headers: Object.keys(SYNC_OPTIONS.ajax.headers)
      }
    }
  });
}
