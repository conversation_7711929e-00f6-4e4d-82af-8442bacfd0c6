import PouchDB from 'pouchdb-browser';

/**
 * Index health status information
 */
interface IndexHealthStatus {
  indexName: string;
  isHealthy: boolean;
  expectedDocuments: number;
  actualDocuments: number;
  corruptionDetected: boolean;
  lastChecked: Date;
  errorDetails?: string;
}

/**
 * Overall index health summary
 */
interface IndexHealthSummary {
  hasCorruption: boolean;
  corruptedIndexes: string[];
  healthyIndexes: string[];
  totalIndexes: number;
  overallHealthScore: number;
  lastFullCheck: Date;
}

/**
 * IndexHealthMonitor - Detects and recovers from PouchDB Find index corruption
 * 
 * Addresses the critical issue where PouchDB Find returns only 25 documents instead of 165+
 * Provides automatic index rebuilding and fallback to allDocs queries
 */
export class IndexHealthMonitor {
  private db: PouchDB.Database;
  private indexHealthStatus: Map<string, IndexHealthStatus> = new Map();
  private lastFullHealthCheck: Date = new Date(0);
  private healthCheckInterval: number = 300000; // 5 minutes
  private corruptionThreshold: number = 0.8; // 80% - if actual < 80% of expected, consider corrupted

  // Critical indexes that need monitoring
  private criticalIndexes = [
    'admission-festival-index',
    'admission-location-index', 
    'admission-status-index',
    'festival_documentType_index',
    'item_index',
    'feedback_index',
    'sensory-hub-festival-index'
  ];

  constructor(db: PouchDB.Database) {
    this.db = db;
    console.log('[INDEX HEALTH] IndexHealthMonitor initialized');
  }

  /**
   * Initialize the index health monitor
   */
  public async initialize(): Promise<void> {
    try {
      console.log('[INDEX HEALTH] Initializing index health monitoring...');
      
      // Perform initial health check
      await this.performFullHealthCheck();
      
      // Set up periodic health checks
      this.schedulePeriodicHealthChecks();
      
      console.log('[INDEX HEALTH] Index health monitoring initialized successfully');
    } catch (error) {
      console.error('[INDEX HEALTH] Failed to initialize index health monitoring:', error);
    }
  }

  /**
   * Performs a comprehensive health check of all indexes
   */
  public async checkIndexHealth(): Promise<IndexHealthSummary> {
    const startTime = Date.now();
    console.log('[INDEX HEALTH] Starting comprehensive index health check...');

    try {
      const healthStatuses: IndexHealthStatus[] = [];

      // Check each critical index
      for (const indexName of this.criticalIndexes) {
        const status = await this.checkSingleIndexHealth(indexName);
        healthStatuses.push(status);
        this.indexHealthStatus.set(indexName, status);
      }

      // Calculate overall health summary
      const corruptedIndexes = healthStatuses
        .filter(status => status.corruptionDetected)
        .map(status => status.indexName);

      const healthyIndexes = healthStatuses
        .filter(status => !status.corruptionDetected)
        .map(status => status.indexName);

      const overallHealthScore = healthyIndexes.length / healthStatuses.length;
      const hasCorruption = corruptedIndexes.length > 0;

      this.lastFullHealthCheck = new Date();

      const summary: IndexHealthSummary = {
        hasCorruption,
        corruptedIndexes,
        healthyIndexes,
        totalIndexes: healthStatuses.length,
        overallHealthScore,
        lastFullCheck: this.lastFullHealthCheck
      };

      const duration = Date.now() - startTime;
      console.log(`[INDEX HEALTH] Health check completed in ${duration}ms:`, summary);

      return summary;

    } catch (error) {
      console.error('[INDEX HEALTH] Health check failed:', error);
      return {
        hasCorruption: true,
        corruptedIndexes: this.criticalIndexes,
        healthyIndexes: [],
        totalIndexes: this.criticalIndexes.length,
        overallHealthScore: 0,
        lastFullCheck: new Date()
      };
    }
  }

  /**
   * Checks the health of a single index
   */
  private async checkSingleIndexHealth(indexName: string): Promise<IndexHealthStatus> {
    const startTime = Date.now();
    
    try {
      console.log(`[INDEX HEALTH] Checking index: ${indexName}`);

      // Get expected document count using allDocs (ground truth)
      const expectedCount = await this.getExpectedDocumentCount(indexName);
      
      // Get actual document count using the index
      const actualCount = await this.getActualDocumentCount(indexName);

      // Detect corruption based on significant discrepancy
      const ratio = expectedCount > 0 ? actualCount / expectedCount : 1;
      const corruptionDetected = ratio < this.corruptionThreshold;

      const status: IndexHealthStatus = {
        indexName,
        isHealthy: !corruptionDetected,
        expectedDocuments: expectedCount,
        actualDocuments: actualCount,
        corruptionDetected,
        lastChecked: new Date()
      };

      const duration = Date.now() - startTime;
      
      if (corruptionDetected) {
        console.warn(`[INDEX HEALTH] CORRUPTION DETECTED in ${indexName}: expected ${expectedCount}, got ${actualCount} (${(ratio * 100).toFixed(1)}%) - ${duration}ms`);
      } else {
        console.log(`[INDEX HEALTH] Index ${indexName} is healthy: ${actualCount}/${expectedCount} documents - ${duration}ms`);
      }

      return status;

    } catch (error) {
      console.error(`[INDEX HEALTH] Failed to check index ${indexName}:`, error);
      return {
        indexName,
        isHealthy: false,
        expectedDocuments: 0,
        actualDocuments: 0,
        corruptionDetected: true,
        lastChecked: new Date(),
        errorDetails: String(error)
      };
    }
  }

  /**
   * Gets expected document count using allDocs (ground truth)
   */
  private async getExpectedDocumentCount(indexName: string): Promise<number> {
    try {
      switch (indexName) {
        case 'admission-festival-index':
        case 'admission-location-index':
        case 'admission-status-index':
          return await this.getAdmissionCountAllDocs();
          
        case 'festival_documentType_index':
          return await this.getFestivalCountAllDocs();
          
        case 'item_index':
          return await this.getItemCountAllDocs();
          
        case 'feedback_index':
          return await this.getFeedbackCountAllDocs();
          
        case 'sensory-hub-festival-index':
          return await this.getSensoryHubCountAllDocs();
          
        default:
          console.warn(`[INDEX HEALTH] Unknown index for expected count: ${indexName}`);
          return 0;
      }
    } catch (error) {
      console.error(`[INDEX HEALTH] Failed to get expected count for ${indexName}:`, error);
      return 0;
    }
  }

  /**
   * Gets actual document count using the index
   */
  private async getActualDocumentCount(indexName: string): Promise<number> {
    try {
      // Check if PouchDB Find plugin is available
      if (typeof this.db.find !== 'function') {
        console.warn('[INDEX HEALTH] PouchDB Find plugin not available');
        return 0;
      }

      switch (indexName) {
        case 'admission-festival-index':
          return await this.getAdmissionCountFind();
          
        case 'admission-location-index':
          return await this.getAdmissionCountByLocationFind();
          
        case 'admission-status-index':
          return await this.getAdmissionCountByStatusFind();
          
        case 'festival_documentType_index':
          return await this.getFestivalCountFind();
          
        case 'item_index':
          return await this.getItemCountFind();
          
        case 'feedback_index':
          return await this.getFeedbackCountFind();
          
        case 'sensory-hub-festival-index':
          return await this.getSensoryHubCountFind();
          
        default:
          console.warn(`[INDEX HEALTH] Unknown index for actual count: ${indexName}`);
          return 0;
      }
    } catch (error) {
      console.error(`[INDEX HEALTH] Failed to get actual count for ${indexName}:`, error);
      return 0;
    }
  }

  // Ground truth methods using allDocs
  private async getAdmissionCountAllDocs(): Promise<number> {
    const result = await this.db.allDocs({
      startkey: 'admission_',
      endkey: 'admission_\ufff0',
      include_docs: true
    });
    
    return result.rows.filter(row => {
      const doc = row.doc as any;
      return doc && doc.documentType === 'admission' && !doc.isDeleted;
    }).length;
  }

  private async getFestivalCountAllDocs(): Promise<number> {
    const result = await this.db.allDocs({
      startkey: 'festival_',
      endkey: 'festival_\ufff0',
      include_docs: true
    });
    
    return result.rows.filter(row => {
      const doc = row.doc as any;
      return doc && doc.documentType === 'festival' && !doc.isDeleted;
    }).length;
  }

  private async getItemCountAllDocs(): Promise<number> {
    const result = await this.db.allDocs({
      startkey: 'item_',
      endkey: 'item_\ufff0',
      include_docs: true
    });
    
    return result.rows.filter(row => {
      const doc = row.doc as any;
      return doc && doc.documentType === 'item' && !doc.isDeleted;
    }).length;
  }

  private async getFeedbackCountAllDocs(): Promise<number> {
    const result = await this.db.allDocs({
      startkey: 'feedback_',
      endkey: 'feedback_\ufff0',
      include_docs: true
    });
    
    return result.rows.filter(row => {
      const doc = row.doc as any;
      return doc && doc.type === 'feedback' && !doc.isDeleted;
    }).length;
  }

  private async getSensoryHubCountAllDocs(): Promise<number> {
    const result = await this.db.allDocs({
      startkey: 'sensory_',
      endkey: 'sensory_\ufff0',
      include_docs: true
    });
    
    return result.rows.filter(row => {
      const doc = row.doc as any;
      return doc && doc.documentType === 'sensory_hub_visit' && !doc.isDeleted;
    }).length;
  }

  // Index-based methods using find
  private async getAdmissionCountFind(): Promise<number> {
    const result = await this.db.find({
      selector: {
        documentType: 'admission',
        $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
      },
      fields: ['_id']
    });
    return result.docs.length;
  }

  private async getAdmissionCountByLocationFind(): Promise<number> {
    const result = await this.db.find({
      selector: {
        documentType: 'admission',
        siteLocationId: { $exists: true },
        $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
      },
      fields: ['_id']
    });
    return result.docs.length;
  }

  private async getAdmissionCountByStatusFind(): Promise<number> {
    const result = await this.db.find({
      selector: {
        documentType: 'admission',
        status: { $exists: true },
        $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
      },
      fields: ['_id']
    });
    return result.docs.length;
  }

  private async getFestivalCountFind(): Promise<number> {
    const result = await this.db.find({
      selector: {
        documentType: 'festival',
        $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
      },
      fields: ['_id']
    });
    return result.docs.length;
  }

  private async getItemCountFind(): Promise<number> {
    const result = await this.db.find({
      selector: {
        documentType: 'item',
        $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
      },
      fields: ['_id']
    });
    return result.docs.length;
  }

  private async getFeedbackCountFind(): Promise<number> {
    const result = await this.db.find({
      selector: {
        type: 'feedback',
        $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
      },
      fields: ['_id']
    });
    return result.docs.length;
  }

  private async getSensoryHubCountFind(): Promise<number> {
    const result = await this.db.find({
      selector: {
        documentType: 'sensory_hub_visit',
        $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
      },
      fields: ['_id']
    });
    return result.docs.length;
  }

  /**
   * Recovers corrupted indexes by rebuilding them
   */
  public async recoverCorruptedIndexes(): Promise<void> {
    console.log('[INDEX HEALTH] Starting index recovery process...');

    try {
      const healthSummary = await this.checkIndexHealth();
      
      if (!healthSummary.hasCorruption) {
        console.log('[INDEX HEALTH] No corrupted indexes found, recovery not needed');
        return;
      }

      console.log(`[INDEX HEALTH] Recovering ${healthSummary.corruptedIndexes.length} corrupted indexes...`);

      for (const indexName of healthSummary.corruptedIndexes) {
        await this.rebuildSingleIndex(indexName);
      }

      // Verify recovery
      const postRecoveryHealth = await this.checkIndexHealth();
      if (postRecoveryHealth.hasCorruption) {
        console.warn('[INDEX HEALTH] Some indexes still corrupted after recovery:', postRecoveryHealth.corruptedIndexes);
      } else {
        console.log('[INDEX HEALTH] All indexes recovered successfully');
      }

    } catch (error) {
      console.error('[INDEX HEALTH] Index recovery failed:', error);
      throw error;
    }
  }

  /**
   * Rebuilds a single corrupted index
   */
  private async rebuildSingleIndex(indexName: string): Promise<void> {
    console.log(`[INDEX HEALTH] Rebuilding index: ${indexName}`);

    try {
      // Get the index definition for rebuilding
      const indexDefinition = this.getIndexDefinition(indexName);
      
      if (!indexDefinition) {
        console.warn(`[INDEX HEALTH] No definition found for index: ${indexName}`);
        return;
      }

      // Delete the existing index if possible
      try {
        await this.db.deleteIndex(indexDefinition);
        console.log(`[INDEX HEALTH] Deleted corrupted index: ${indexName}`);
      } catch (deleteError) {
        console.warn(`[INDEX HEALTH] Could not delete index ${indexName}:`, deleteError);
      }

      // Recreate the index
      await this.db.createIndex(indexDefinition);
      console.log(`[INDEX HEALTH] Recreated index: ${indexName}`);

      // Wait a moment for index to be built
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Verify the rebuild
      const status = await this.checkSingleIndexHealth(indexName);
      if (status.corruptionDetected) {
        console.warn(`[INDEX HEALTH] Index ${indexName} still corrupted after rebuild`);
      } else {
        console.log(`[INDEX HEALTH] Index ${indexName} successfully rebuilt`);
      }

    } catch (error) {
      console.error(`[INDEX HEALTH] Failed to rebuild index ${indexName}:`, error);
    }
  }

  /**
   * Gets the index definition for rebuilding
   */
  private getIndexDefinition(indexName: string): any {
    const definitions: { [key: string]: any } = {
      'admission-festival-index': {
        index: {
          fields: ['documentType', 'festivalId', 'createdAt', 'isDeleted'],
          name: 'admission-festival-index'
        }
      },
      'admission-location-index': {
        index: {
          fields: ['documentType', 'siteLocationId', 'createdAt', 'isDeleted'],
          name: 'admission-location-index'
        }
      },
      'admission-status-index': {
        index: {
          fields: ['documentType', 'status', 'InBayNow', 'isDeleted'],
          name: 'admission-status-index'
        }
      },
      'festival_documentType_index': {
        index: {
          fields: ['documentType', 'isDeleted'],
          name: 'festival_documentType_index'
        }
      },
      'item_index': {
        index: {
          fields: ['documentType', 'type', 'festivalId', 'siteLocationId', 'timestamp', 'isDeleted'],
          name: 'item_index'
        }
      },
      'feedback_index': {
        index: {
          fields: ['type', 'documentType', 'timestamp', 'resolved', 'isDeleted'],
          name: 'feedback_index'
        }
      },
      'sensory-hub-festival-index': {
        index: {
          fields: ['documentType', 'festivalId', 'visitTimestamp', 'isDeleted'],
          name: 'sensory-hub-festival-index'
        }
      }
    };

    return definitions[indexName];
  }

  /**
   * Performs a safe document query with fallback to allDocs
   */
  public async performSafeDocumentQuery(documentType?: string): Promise<string[]> {
    try {
      // First try using find if available and healthy
      if (typeof this.db.find === 'function') {
        const indexHealth = this.indexHealthStatus.get(`${documentType || 'admission'}-festival-index`);
        
        if (!indexHealth || !indexHealth.corruptionDetected) {
          const selector: any = {};
          
          if (documentType) {
            selector.documentType = documentType;
          }
          
          selector.$or = [{ isDeleted: { $exists: false } }, { isDeleted: false }];

          const result = await this.db.find({
            selector,
            fields: ['_id']
          });

          console.log(`[INDEX HEALTH] Safe query using find returned ${result.docs.length} documents`);
          return result.docs.map(doc => doc._id);
        }
      }

      // Fallback to allDocs
      console.log('[INDEX HEALTH] Using allDocs fallback for safe query');
      return await this.performAllDocsQuery(documentType);

    } catch (error) {
      console.error('[INDEX HEALTH] Safe query failed, using allDocs fallback:', error);
      return await this.performAllDocsQuery(documentType);
    }
  }

  /**
   * Performs allDocs query as fallback
   */
  private async performAllDocsQuery(documentType?: string): Promise<string[]> {
    try {
      let startkey = '';
      let endkey = '\ufff0';

      if (documentType) {
        startkey = `${documentType}_`;
        endkey = `${documentType}_\ufff0`;
      }

      const result = await this.db.allDocs({
        startkey,
        endkey,
        include_docs: true
      });

      const filteredIds = result.rows
        .filter(row => {
          const doc = row.doc as any;
          if (!doc) return false;
          if (documentType && doc.documentType !== documentType) return false;
          if (doc.isDeleted === true) return false;
          return true;
        })
        .map(row => row.id);

      console.log(`[INDEX HEALTH] AllDocs fallback returned ${filteredIds.length} documents`);
      return filteredIds;

    } catch (error) {
      console.error('[INDEX HEALTH] AllDocs fallback failed:', error);
      return [];
    }
  }

  /**
   * Schedules periodic health checks
   */
  private schedulePeriodicHealthChecks(): void {
    setInterval(async () => {
      try {
        await this.performFullHealthCheck();
      } catch (error) {
        console.error('[INDEX HEALTH] Periodic health check failed:', error);
      }
    }, this.healthCheckInterval);

    console.log(`[INDEX HEALTH] Scheduled periodic health checks every ${this.healthCheckInterval / 1000}s`);
  }

  /**
   * Performs a full health check of all indexes
   */
  private async performFullHealthCheck(): Promise<void> {
    const timeSinceLastCheck = Date.now() - this.lastFullHealthCheck.getTime();
    
    if (timeSinceLastCheck < this.healthCheckInterval) {
      return; // Skip if checked recently
    }

    console.log('[INDEX HEALTH] Performing scheduled full health check...');
    const summary = await this.checkIndexHealth();
    
    if (summary.hasCorruption) {
      console.warn('[INDEX HEALTH] Corruption detected in scheduled check, triggering recovery...');
      await this.recoverCorruptedIndexes();
    }
  }

  /**
   * Gets the overall health score (0-1)
   */
  public getHealthScore(): number {
    const healthyCount = Array.from(this.indexHealthStatus.values())
      .filter(status => !status.corruptionDetected).length;
    
    const totalCount = this.indexHealthStatus.size || this.criticalIndexes.length;
    
    return totalCount > 0 ? healthyCount / totalCount : 1;
  }

  /**
   * Gets health summary for debugging
   */
  public getHealthSummary(): any {
    return {
      indexHealthStatus: Array.from(this.indexHealthStatus.entries()),
      lastFullHealthCheck: this.lastFullHealthCheck,
      overallHealthScore: this.getHealthScore(),
      criticalIndexes: this.criticalIndexes,
      corruptionThreshold: this.corruptionThreshold
    };
  }

  /**
   * Cleanup method
   */
  public cleanup(): void {
    this.indexHealthStatus.clear();
    console.log('[INDEX HEALTH] IndexHealthMonitor cleaned up');
  }
}