/**
 * Sync Configuration Manager
 * 
 * Provides configurable options for sync behavior, cache duration, and force sync scenarios.
 * Allows runtime configuration changes and environment-specific settings.
 */

import { SyncConfig } from './persistent-initialization-manager';

export interface ExtendedSyncConfig extends SyncConfig {
  // Network-related settings
  connectionTimeout: number;
  retryAttempts: number;
  retryBackoffMultiplier: number;
  
  // Performance settings
  batchSize: number;
  maxConcurrentSyncs: number;
  
  // Cache settings
  cacheCompressionEnabled: boolean;
  cacheCleanupInterval: number;
  
  // Debug and monitoring
  enableDetailedLogging: boolean;
  performanceMetricsEnabled: boolean;
  
  // Feature flags
  enableSelectiveSync: boolean;
  enablePersistentInit: boolean;
}

export interface SyncBehaviorProfile {
  name: string;
  description: string;
  config: Partial<ExtendedSyncConfig>;
}

export class SyncConfigManager {
  private static readonly CONFIG_STORAGE_KEY = 'ithink_welfare_sync_config_extended';
  private static readonly PROFILE_STORAGE_KEY = 'ithink_welfare_sync_profile';
  
  private currentConfig: ExtendedSyncConfig;
  private currentProfile: string = 'default';
  
  // Predefined sync behavior profiles
  private static readonly SYNC_PROFILES: Record<string, SyncBehaviorProfile> = {
    default: {
      name: 'Default',
      description: 'Balanced performance and reliability',
      config: {
        initializationTTL: 30 * 60 * 1000, // 30 minutes
        minSyncInterval: 5 * 60 * 1000,    // 5 minutes
        maxPageLoadsBeforeSync: 10,
        maxDataAge: 2 * 60 * 60 * 1000,    // 2 hours
        connectionTimeout: 10000,          // 10 seconds
        retryAttempts: 3,
        retryBackoffMultiplier: 2,
        batchSize: 100,
        maxConcurrentSyncs: 1,
        cacheCompressionEnabled: true,
        cacheCleanupInterval: 60 * 60 * 1000, // 1 hour
        enableDetailedLogging: false,
        performanceMetricsEnabled: true,
        enableSelectiveSync: false,
        enablePersistentInit: true
      }
    },
    
    performance: {
      name: 'Performance',
      description: 'Optimized for speed, less frequent syncs',
      config: {
        initializationTTL: 60 * 60 * 1000, // 1 hour
        minSyncInterval: 15 * 60 * 1000,   // 15 minutes
        maxPageLoadsBeforeSync: 20,
        maxDataAge: 4 * 60 * 60 * 1000,    // 4 hours
        connectionTimeout: 5000,           // 5 seconds
        retryAttempts: 2,
        retryBackoffMultiplier: 1.5,
        batchSize: 200,
        maxConcurrentSyncs: 1,
        cacheCompressionEnabled: true,
        cacheCleanupInterval: 2 * 60 * 60 * 1000, // 2 hours
        enableDetailedLogging: false,
        performanceMetricsEnabled: false,
        enableSelectiveSync: true,
        enablePersistentInit: true
      }
    },
    
    reliability: {
      name: 'Reliability',
      description: 'Frequent syncs, maximum data consistency',
      config: {
        initializationTTL: 10 * 60 * 1000, // 10 minutes
        minSyncInterval: 2 * 60 * 1000,    // 2 minutes
        maxPageLoadsBeforeSync: 5,
        maxDataAge: 30 * 60 * 1000,        // 30 minutes
        connectionTimeout: 15000,          // 15 seconds
        retryAttempts: 5,
        retryBackoffMultiplier: 2,
        batchSize: 50,
        maxConcurrentSyncs: 1,
        cacheCompressionEnabled: false,
        cacheCleanupInterval: 30 * 60 * 1000, // 30 minutes
        enableDetailedLogging: true,
        performanceMetricsEnabled: true,
        enableSelectiveSync: false,
        enablePersistentInit: true
      }
    },
    
    development: {
      name: 'Development',
      description: 'Debug-friendly with detailed logging',
      config: {
        initializationTTL: 5 * 60 * 1000,  // 5 minutes
        minSyncInterval: 30 * 1000,        // 30 seconds
        maxPageLoadsBeforeSync: 3,
        maxDataAge: 15 * 60 * 1000,        // 15 minutes
        connectionTimeout: 20000,          // 20 seconds
        retryAttempts: 1,
        retryBackoffMultiplier: 1,
        batchSize: 25,
        maxConcurrentSyncs: 1,
        cacheCompressionEnabled: false,
        cacheCleanupInterval: 10 * 60 * 1000, // 10 minutes
        enableDetailedLogging: true,
        performanceMetricsEnabled: true,
        enableSelectiveSync: false,
        enablePersistentInit: false // Disable for easier testing
      }
    },
    
    offline: {
      name: 'Offline-First',
      description: 'Minimal syncing, maximum offline capability',
      config: {
        initializationTTL: 24 * 60 * 60 * 1000, // 24 hours
        minSyncInterval: 60 * 60 * 1000,        // 1 hour
        maxPageLoadsBeforeSync: 50,
        maxDataAge: 7 * 24 * 60 * 60 * 1000,    // 7 days
        connectionTimeout: 3000,                // 3 seconds
        retryAttempts: 1,
        retryBackoffMultiplier: 1,
        batchSize: 500,
        maxConcurrentSyncs: 1,
        cacheCompressionEnabled: true,
        cacheCleanupInterval: 4 * 60 * 60 * 1000, // 4 hours
        enableDetailedLogging: false,
        performanceMetricsEnabled: false,
        enableSelectiveSync: true,
        enablePersistentInit: true
      }
    }
  };

  constructor() {
    this.currentProfile = this.loadCurrentProfile();
    this.currentConfig = this.loadConfig();
    
    console.log('[SYNC CONFIG] Initialized with profile:', this.currentProfile);
  }

  /**
   * Get current sync configuration
   */
  public getConfig(): ExtendedSyncConfig {
    return { ...this.currentConfig };
  }

  /**
   * Get basic sync config for PersistentInitializationManager
   */
  public getBasicSyncConfig(): SyncConfig {
    const { 
      initializationTTL, 
      minSyncInterval, 
      maxPageLoadsBeforeSync, 
      maxDataAge 
    } = this.currentConfig;
    
    return {
      initializationTTL,
      minSyncInterval,
      maxPageLoadsBeforeSync,
      maxDataAge
    };
  }

  /**
   * Set sync configuration profile
   */
  public setProfile(profileName: string): boolean {
    const profile = SyncConfigManager.SYNC_PROFILES[profileName];
    if (!profile) {
      console.warn('[SYNC CONFIG] Unknown profile:', profileName);
      return false;
    }

    this.currentProfile = profileName;
    this.currentConfig = this.mergeWithDefaults(profile.config);
    
    this.saveCurrentProfile();
    this.saveConfig();
    
    console.log('[SYNC CONFIG] Profile changed to:', profileName, profile.description);
    return true;
  }

  /**
   * Update specific configuration values
   */
  public updateConfig(updates: Partial<ExtendedSyncConfig>): void {
    this.currentConfig = { ...this.currentConfig, ...updates };
    this.saveConfig();
    
    console.log('[SYNC CONFIG] Configuration updated:', updates);
  }

  /**
   * Get available profiles
   */
  public getAvailableProfiles(): Record<string, SyncBehaviorProfile> {
    return { ...SyncConfigManager.SYNC_PROFILES };
  }

  /**
   * Get current profile name
   */
  public getCurrentProfile(): string {
    return this.currentProfile;
  }

  /**
   * Reset to default configuration
   */
  public resetToDefaults(): void {
    this.setProfile('default');
    console.log('[SYNC CONFIG] Reset to default configuration');
  }

  /**
   * Auto-detect optimal profile based on environment
   */
  public autoDetectProfile(): string {
    // Development environment
    if (process.env.NODE_ENV === 'development') {
      this.setProfile('development');
      return 'development';
    }

    // Check for performance indicators
    const isLowPowerDevice = this.isLowPowerDevice();
    const hasSlowConnection = this.hasSlowConnection();

    if (isLowPowerDevice || hasSlowConnection) {
      this.setProfile('performance');
      return 'performance';
    }

    // Check for offline-first preference
    if (localStorage.getItem('preferOfflineMode') === 'true') {
      this.setProfile('offline');
      return 'offline';
    }

    // Default to balanced profile
    this.setProfile('default');
    return 'default';
  }

  private loadCurrentProfile(): string {
    try {
      const stored = localStorage.getItem(SyncConfigManager.PROFILE_STORAGE_KEY);
      if (stored && SyncConfigManager.SYNC_PROFILES[stored]) {
        return stored;
      }
    } catch (error) {
      console.warn('[SYNC CONFIG] Failed to load profile from localStorage:', error);
    }
    return 'default';
  }

  private saveCurrentProfile(): void {
    try {
      localStorage.setItem(SyncConfigManager.PROFILE_STORAGE_KEY, this.currentProfile);
    } catch (error) {
      console.error('[SYNC CONFIG] Failed to save profile to localStorage:', error);
    }
  }

  private loadConfig(): ExtendedSyncConfig {
    try {
      const stored = localStorage.getItem(SyncConfigManager.CONFIG_STORAGE_KEY);
      if (stored) {
        const config = JSON.parse(stored) as Partial<ExtendedSyncConfig>;
        return this.mergeWithDefaults(config);
      }
    } catch (error) {
      console.warn('[SYNC CONFIG] Failed to load config from localStorage:', error);
    }
    
    // Load from current profile
    const profile = SyncConfigManager.SYNC_PROFILES[this.currentProfile];
    return this.mergeWithDefaults(profile.config);
  }

  private saveConfig(): void {
    try {
      localStorage.setItem(SyncConfigManager.CONFIG_STORAGE_KEY, JSON.stringify(this.currentConfig));
    } catch (error) {
      console.error('[SYNC CONFIG] Failed to save config to localStorage:', error);
    }
  }

  private mergeWithDefaults(config: Partial<ExtendedSyncConfig>): ExtendedSyncConfig {
    const defaultProfile = SyncConfigManager.SYNC_PROFILES.default;
    return { ...defaultProfile.config, ...config } as ExtendedSyncConfig;
  }

  private isLowPowerDevice(): boolean {
    // Check for iPad or mobile device indicators
    const userAgent = navigator.userAgent.toLowerCase();
    const isIpad = /ipad/.test(userAgent);
    const isMobile = /mobile|android|iphone/.test(userAgent);
    
    // Check available memory if supported
    const deviceMemory = (navigator as any).deviceMemory;
    const hasLowMemory = deviceMemory && deviceMemory <= 4; // 4GB or less
    
    return isIpad || isMobile || hasLowMemory;
  }

  private hasSlowConnection(): boolean {
    // Check connection type if supported
    const connection = (navigator as any).connection;
    if (connection) {
      const slowTypes = ['slow-2g', '2g', '3g'];
      return slowTypes.includes(connection.effectiveType);
    }
    
    return false;
  }
}
