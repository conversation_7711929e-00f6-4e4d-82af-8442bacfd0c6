import PouchDB from 'pouchdb-browser';
import { getConfig, SYNC_OPTIONS, SYNC_CONSTANTS } from './config';
import debounce from 'lodash/debounce';

interface SyncError {
  status?: number;
  name?: string;
  message?: string;
  reason?: string;
}

type ReplicationResult = PouchDB.Replication.ReplicationResultComplete<{}>;

interface SyncResult {
  push?: ReplicationResult;
  pull?: ReplicationResult;
  status: 'complete' | 'error';
  errors: SyncError[];
}

interface QueuedChange {
  id: string;
  timestamp: number;
  retryCount: number;
}

type DebouncedFunction = {
  (): void;
  cancel(): void;
};

interface AuthRetryState {
  attempts: number;
  lastAttempt: number;
  backoffDelay: number;
  isAuthError: boolean;
}

export class SyncManager {
  private db: PouchDB.Database;
  private remoteDb?: PouchDB.Database;
  private syncHandler?: PouchDB.Replication.Sync<{}>;
  private syncListeners: Set<() => void> = new Set();
  private syncStatus: {
    status: 'synced' | 'syncing' | 'error' | 'disconnected' | 'paused' | 'auth_error' | 'initial_sync';
    lastSync?: Date;
    pendingChanges: number;
    error?: string;
    authError?: boolean;
  } = {
    status: 'disconnected',
    pendingChanges: 0,
    authError: false
  };
  private retryAttempts: number = 0;
  private lastConnectionTest: number = 0;
  private syncInProgress: boolean = false;
  private isInitialized: boolean = false;
  private changeQueue: Map<string, QueuedChange> = new Map();
  private processingQueue: boolean = false;
  private debouncedSync: DebouncedFunction;
  private authRetryState: AuthRetryState = {
    attempts: 0,
    lastAttempt: 0,
    backoffDelay: 5000, // Start with 5 seconds for auth errors
    isAuthError: false
  };
  private suppressAuthErrors: boolean = false;

  constructor(db: PouchDB.Database) {
    this.db = db;
    if (this.db.setMaxListeners) {
      this.db.setMaxListeners(20);
    }

    const config = getConfig();
    const { remoteUrl, remoteName, username, password } = config;
    const baseUrl = remoteUrl.replace(/\/$/, '');
    const fullRemoteUrl = `${baseUrl}/${remoteName}`;

    if (process.env.NODE_ENV === 'development') {
      console.log('Direct CouchDB connection configured - Cloudflare Access headers removed');
    }

    // Create basic auth header for PouchDB
    const basicAuthHeader = username && password
      ? 'Basic ' + btoa(`${username}:${password}`)
      : undefined;

    const options = {
      ...SYNC_OPTIONS,
      auth: {
        username,
        password
      },
      ajax: {
        ...SYNC_OPTIONS.ajax,
        headers: {
          ...SYNC_OPTIONS.ajax.headers,
          // Only include Authorization header for direct CouchDB connection
          ...(basicAuthHeader ? { 'Authorization': basicAuthHeader } : {})
        }
      }
    } as PouchDB.Configuration.RemoteDatabaseConfiguration;

    // Debug: Log the headers being sent (without sensitive data)
    if (process.env.NODE_ENV === 'development') {
      console.log('=== Direct CouchDB Connection - Headers being sent ===');
      console.log('All headers that will be sent:', Object.keys({
        ...SYNC_OPTIONS.ajax.headers,
        ...(basicAuthHeader ? { 'Authorization': basicAuthHeader } : {})
      }));
      console.log('Authentication configured:', {
        'Basic Auth': basicAuthHeader ? 'CONFIGURED' : 'MISSING',
        'Username': username ? 'SET' : 'MISSING',
        'Password': password ? 'SET' : 'MISSING'
      });
      console.log('=== Headers match CouchDB CORS config: accept, authorization, content-type, origin, referer ===');
    }

    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('Initializing remote database connection to:', fullRemoteUrl);
      }
      this.remoteDb = new PouchDB(fullRemoteUrl, options);
      if (this.remoteDb.setMaxListeners) {
        this.remoteDb.setMaxListeners(20);
      }
      this.initializeSync();
    } catch (error) {
      const errorStr = String(error);
      if (errorStr.includes('<!DOCTYPE') || errorStr.includes('<html')) {
        console.error('Failed to initialize remote database: Received HTML instead of JSON. This likely indicates an authentication issue with Cloudflare Access.');
        this.updateSyncStatus('error', 'Authentication error: Received HTML instead of JSON. Please check Cloudflare Access credentials.');
      } else {
        console.error('Failed to initialize remote database:', error);
        this.updateSyncStatus('error', errorStr);
      }
    }

    // Setup debounced sync
    this.debouncedSync = debounce(this.processChangeQueue.bind(this), SYNC_CONSTANTS.DEBOUNCE_DELAY) as DebouncedFunction;
  }

  private updateSyncStatus(
    status: 'synced' | 'syncing' | 'error' | 'disconnected' | 'paused' | 'auth_error' | 'initial_sync',
    error?: string,
    isAuthError: boolean = false
  ) {
    this.syncStatus = {
      status,
      lastSync: status === 'synced' ? new Date() : this.syncStatus.lastSync,
      pendingChanges: this.changeQueue.size,
      error: error,
      authError: isAuthError
    };
    this.notifyListeners();
  }

  private isAuthenticationError(error: any): boolean {
    const errorStr = String(error);
    return errorStr.includes('<!DOCTYPE') ||
           errorStr.includes('<html') ||
           errorStr.includes('530') ||
           errorStr.includes('Authentication failed') ||
           errorStr.includes('blocked by CORS policy') ||
           errorStr.includes('Unexpected token') ||
           (error?.status === 530);
  }

  private async handleAuthError(error: any): Promise<void> {
    const now = Date.now();
    this.authRetryState.isAuthError = true;
    
    // Don't spam console with auth errors after the first few attempts
    if (this.authRetryState.attempts < 3 && !this.suppressAuthErrors) {
      console.error('Authentication error detected:', error);
      console.warn('Cloudflare Access authentication failed. App will continue to work with cached data.');
      console.log('Authentication retry state:', {
        attempts: this.authRetryState.attempts,
        backoffDelay: this.authRetryState.backoffDelay,
        isAuthError: this.authRetryState.isAuthError
      });
    }
    
    // After 3 attempts, suppress further auth error logging for 5 minutes
    if (this.authRetryState.attempts >= 3) {
      this.suppressAuthErrors = true;
      setTimeout(() => {
        console.log('Authentication error suppression period ended, resetting retry state');
        this.suppressAuthErrors = false;
        this.authRetryState.attempts = 0; // Reset after suppression period
      }, 300000); // 5 minutes
    }
    
    this.authRetryState.attempts++;
    this.authRetryState.lastAttempt = now;
    
    // Exponential backoff for auth errors (5s, 10s, 20s, 40s, max 2 minutes)
    this.authRetryState.backoffDelay = Math.min(
      this.authRetryState.backoffDelay * 2,
      120000 // Max 2 minutes
    );
    
    console.log('Updated authentication retry state:', {
      attempts: this.authRetryState.attempts,
      backoffDelay: this.authRetryState.backoffDelay,
      nextRetryTime: new Date(now + this.authRetryState.backoffDelay).toISOString()
    });
    
    this.updateSyncStatus('auth_error', 'Authentication failed. App running in offline mode.', true);
  }

  private shouldRetryAuth(): boolean {
    const now = Date.now();
    const timeSinceLastAttempt = now - this.authRetryState.lastAttempt;
    return timeSinceLastAttempt >= this.authRetryState.backoffDelay;
  }

  public resetAuthRetryState(): void {
    console.log('Resetting authentication retry state');
    this.authRetryState = {
      attempts: 0,
      lastAttempt: 0,
      backoffDelay: 5000,
      isAuthError: false
    };
    this.suppressAuthErrors = false;
    console.log('Authentication state reset complete');
  }

  private async initializeSync() {
    if (this.isInitialized || !this.remoteDb) {
      return;
    }

    // PERFORMANCE: Mark as initialized immediately and start in synced state
    this.isInitialized = true;
    this.updateSyncStatus('synced');

    // PERFORMANCE: Simplified background initialization - no delays
    try {
      console.log('[DEBUG] SyncManager: Starting fast initialization...');
      
      // Quick connection test with reduced timeout
      await this.testConnection();
      this.resetAuthRetryState();
      
      // Check for local data without blocking
      const hasData = await this.checkForLocalData();
      console.log('[DEBUG] SyncManager: Local data found:', hasData);
      
      // Always mark as synced - let data load in background if needed
      this.updateSyncStatus('synced');
      this.notifyListeners();
      
      // PERFORMANCE: Skip initial sync if we have data - sync in background later
      if (!hasData) {
        console.log('[DEBUG] SyncManager: Starting non-blocking background sync...');
        // Fire and forget - don't wait for this
        this.performInitialSync().catch(error => {
          console.warn('[DEBUG] SyncManager: Background sync failed (non-critical):', error);
        });
      }
      
    } catch (error) {
      if (this.isAuthenticationError(error)) {
        await this.handleAuthError(error);
      } else {
        console.warn('[DEBUG] SyncManager: Fast initialization failed (non-blocking):', error);
        this.updateSyncStatus('synced'); // Still mark as synced to unblock UI
      }
    }
  }

  private async checkForLocalData(): Promise<boolean> {
    try {
      // Check for any documents in the database (excluding design docs)
      const result = await this.db.allDocs({
        limit: 1,
        include_docs: false,
        startkey: '\u0000',
        endkey: '\ufff0'
      });
      return result.rows.length > 0;
    } catch (error) {
      console.error('Error checking for local data:', error);
      return false;
    }
  }

  private async performInitialSync(): Promise<void> {
    try {
      console.log('Starting initial sync...');
      this.updateSyncStatus('syncing');
      
      // Perform the sync
      await this.sync();
      
      // Verify we actually got data
      const hasDataAfterSync = await this.checkForLocalData();
      if (hasDataAfterSync) {
        console.log('Initial sync completed successfully with data');
        this.updateSyncStatus('synced');
      } else {
        console.warn('Initial sync completed but no data found');
        this.updateSyncStatus('synced'); // Still mark as synced even if no data
      }
      
      // Force notification to all listeners
      this.notifyListeners();
      
      // Emit a custom event to notify the application that initial sync is complete
      // This can help trigger broader UI updates
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('initialSyncComplete', {
          detail: { hasData: hasDataAfterSync }
        }));
      }
      
    } catch (error) {
      console.error('Initial sync failed:', error);
      this.updateSyncStatus('error', String(error));
      throw error;
    }
  }

  private shouldTestConnection(): boolean {
    const now = Date.now();
    return now - this.lastConnectionTest >= SYNC_CONSTANTS.CONNECTION_TEST_INTERVAL;
  }

  private async testConnection(): Promise<void> {
    if (!this.remoteDb) {
      throw new Error('No remote database configured');
    }

    // Try a direct fetch to the CouchDB for diagnostic purposes only
    // Don't let this block the actual PouchDB connection test
    const config = getConfig();
    const { remoteUrl, remoteName } = config;
    const testUrl = `${remoteUrl}/${remoteName}`;
    
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 Testing direct connection to:', testUrl);
        console.log('🌐 Origin:', window.location.origin);
      }
      
      // Create basic auth header if username and password are provided
      const basicAuthHeader = config.username && config.password
        ? 'Basic ' + btoa(`${config.username}:${config.password}`)
        : undefined;
        
      const headers: Record<string, string> = {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      };
      
      // Add basic auth header if credentials are available
      if (basicAuthHeader) {
        headers['Authorization'] = basicAuthHeader;
      }
      
      if (process.env.NODE_ENV === 'development') {
        console.log('=== Direct CouchDB Connection Test ===');
        console.log('📤 Headers being sent:', Object.keys(headers));
        console.log('🔑 Auth configured:', basicAuthHeader ? 'YES' : 'NO');
      }
      
      const directResponse = await fetch(testUrl, {
        method: 'GET',
        headers
      });
      
      if (process.env.NODE_ENV === 'development') {
        console.log('📊 Direct fetch response status:', directResponse.status);
        console.log('📋 Response headers received:');
        
        // Check for CORS headers specifically
        const corsHeaders = {
          'access-control-allow-origin': directResponse.headers.get('access-control-allow-origin'),
          'access-control-allow-methods': directResponse.headers.get('access-control-allow-methods'),
          'access-control-allow-headers': directResponse.headers.get('access-control-allow-headers'),
          'access-control-allow-credentials': directResponse.headers.get('access-control-allow-credentials')
        };
        console.log('🔒 CORS headers:', corsHeaders);
        
        // Log all response headers for debugging
        console.log('📋 All response headers:', Object.fromEntries(directResponse.headers.entries()));
      }
      
      if (!directResponse.ok) {
        const responseText = await directResponse.text();
        if (process.env.NODE_ENV === 'development') {
          console.warn('❌ Direct fetch failed (diagnostic only):', responseText.substring(0, 200));
        }
      } else if (process.env.NODE_ENV === 'development') {
        console.log('✅ Direct fetch successful, now testing PouchDB connection...');
      }
    } catch (fetchError) {
      // Don't throw here - this is just diagnostic
      if (process.env.NODE_ENV === 'development') {
        console.warn('❌ Direct fetch test failed (diagnostic only):', fetchError);
        console.log('💡 This error suggests CORS is blocking the request');
        console.log('🔧 Check CouchDB CORS configuration for origin:', window.location.origin);
      }
    }

    // The actual connection test using PouchDB
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 Testing PouchDB connection to database...');
      }
      const response = await this.remoteDb.info();
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ Database connection successful:', response.db_name);
      }
      this.lastConnectionTest = Date.now();
      this.retryAttempts = 0;
      
      // Reset auth retry state on successful connection
      this.resetAuthRetryState();
    } catch (error) {
      console.error('❌ PouchDB connection test failed with error:', error);
      
      // Log more details about the error
      if (error && typeof error === 'object') {
        const errorObj = error as any;
        console.error('📊 PouchDB error details:', {
          status: errorObj.status,
          statusText: errorObj.statusText,
          message: errorObj.message,
          name: errorObj.name
        });
      }
      
      if (this.isAuthenticationError(error)) {
        throw new Error('Authentication error: Direct CouchDB authentication failed.');
      } else {
        throw error;
      }
    }
  }

  private async retryConnection() {
    // Don't retry if we have an auth error and haven't waited long enough
    if (this.authRetryState.isAuthError && !this.shouldRetryAuth()) {
      return;
    }
    
    if (this.retryAttempts >= SYNC_CONSTANTS.MAX_RETRY_ATTEMPTS) {
      if (!this.suppressAuthErrors) {
        console.warn('Max retry attempts reached. App will continue in offline mode.');
      }
      this.retryAttempts = 0;
      return;
    }

    const backoffTime = Math.min(
      SYNC_CONSTANTS.RETRY_DELAY_BASE * Math.pow(1.5, this.retryAttempts),
      300000
    );
    this.retryAttempts++;

    if (process.env.NODE_ENV === 'development' && !this.suppressAuthErrors) {
      console.log(`Retrying connection in ${backoffTime}ms (attempt ${this.retryAttempts}/${SYNC_CONSTANTS.MAX_RETRY_ATTEMPTS})`);
    }
    
    await new Promise(resolve => setTimeout(resolve, backoffTime));
    try {
      await this.initializeSync();
    } catch (error) {
      if (!this.isAuthenticationError(error)) {
        await this.retryConnection();
      }
      // For auth errors, don't retry immediately - let the backoff handle it
    }
  }

  private async processChangeQueue() {
    if (this.processingQueue || this.changeQueue.size === 0) {
      return;
    }

    // Skip processing if we have an auth error and haven't waited long enough
    if (this.authRetryState.isAuthError && !this.shouldRetryAuth()) {
      console.log('Skipping sync due to authentication backoff period');
      return;
    }

    console.log(`[SYNC QUEUE] Starting to process queue with ${this.changeQueue.size} items`);
    this.processingQueue = true;
    this.updateSyncStatus('syncing');

    try {
      if (this.shouldTestConnection()) {
        await this.testConnection();
      }

      // Process all changes in the queue
      const changes = Array.from(this.changeQueue.values());
      console.log(`[SYNC QUEUE] Processing ${changes.length} changes. Clearing queue.`);
      this.changeQueue.clear();

      const syncStartTime = Date.now();
      await this.sync();
      const syncDuration = Date.now() - syncStartTime;
      console.log(`[SYNC QUEUE] Sync completed in ${syncDuration}ms for ${changes.length} changes`);
      
      // Reset auth retry state on successful sync
      if (this.authRetryState.isAuthError) {
        console.log('Successful sync after authentication error, resetting auth state');
        this.resetAuthRetryState();
      }
      
      this.updateSyncStatus('synced');
    } catch (error) {
      if (this.isAuthenticationError(error)) {
        await this.handleAuthError(error);
        // Don't re-queue changes for auth errors - they'll be retried later
      } else {
        if (!this.suppressAuthErrors) {
          console.error('Failed to process change queue:', error);
        }
        
        // Re-queue failed changes with increased retry count (only for non-auth errors)
        const now = Date.now();
        const changes = Array.from(this.changeQueue.values());
        changes.forEach((change: QueuedChange) => {
          if (change.retryCount < SYNC_CONSTANTS.MAX_RETRY_ATTEMPTS) {
            this.changeQueue.set(change.id, {
              ...change,
              timestamp: now,
              retryCount: change.retryCount + 1
            });
          }
        });

        this.updateSyncStatus('error', String(error));
      }
    } finally {
      this.processingQueue = false;
    }
  }

  addSyncListener(callback: () => void): () => void {
    this.syncListeners.add(callback);
    return () => {
      this.syncListeners.delete(callback);
    };
  }

  getSyncStatus() {
    return this.syncStatus;
  }

  hasPendingChanges(): boolean {
    return this.changeQueue.size > 0;
  }

  async sync(): Promise<void> {
    if (this.syncInProgress || !this.remoteDb) {
      return;
    }

    // Skip sync if we have an auth error and haven't waited long enough
    if (this.authRetryState.isAuthError && !this.shouldRetryAuth()) {
      return;
    }

    this.syncInProgress = true;
    this.updateSyncStatus('syncing');

    try {
      // Log sync configuration and admission count before sync
      const admissionCount = await this.getAdmissionCount();
      console.log(`[SYNC BATCH] Starting sync with batch_size: ${SYNC_OPTIONS.batch_size}, batches_limit: ${SYNC_OPTIONS.batches_limit}`);
      console.log(`[SYNC BATCH] Current admission count: ${admissionCount}`);
      
      if (this.syncHandler) {
        this.syncHandler.cancel();
        this.syncHandler = undefined;
      }

      const syncResult = await this.db.sync(this.remoteDb, {
        ...SYNC_OPTIONS,
        live: false // One-time sync
      }) as unknown as SyncResult;
      
      // Log sync results
      const newAdmissionCount = await this.getAdmissionCount();
      console.log(`[SYNC BATCH] Sync completed. Admission count: ${newAdmissionCount} (change: ${newAdmissionCount - admissionCount})`);
      
      // Warn if approaching batch limits
      if (admissionCount > (SYNC_OPTIONS.batch_size * SYNC_OPTIONS.batches_limit * 0.8)) {
        console.warn(`[SYNC BATCH] WARNING: Admission count (${admissionCount}) approaching sync capacity (${SYNC_OPTIONS.batch_size * SYNC_OPTIONS.batches_limit})`);
      }
      
      if (syncResult.status === 'complete' && !syncResult.errors?.length) {
        this.updateSyncStatus('synced');
        this.retryAttempts = 0;
        this.resetAuthRetryState(); // Reset on successful sync
      } else {
        // Log detailed sync warnings/errors with JSON stringify for full visibility
        console.warn('[SYNC BATCH] Sync completed with warnings/errors:');
        console.warn('[SYNC BATCH] Status:', syncResult.status);
        console.warn('[SYNC BATCH] Errors:', syncResult.errors);
        console.warn('[SYNC BATCH] Push details:', JSON.stringify({
          ok: syncResult.push?.ok,
          docs_read: syncResult.push?.docs_read,
          docs_written: syncResult.push?.docs_written,
          doc_write_failures: syncResult.push?.doc_write_failures,
          errors: syncResult.push?.errors
        }, null, 2));
        console.warn('[SYNC BATCH] Pull details:', JSON.stringify({
          ok: syncResult.pull?.ok,
          docs_read: syncResult.pull?.docs_read,
          docs_written: syncResult.pull?.docs_written,
          doc_write_failures: syncResult.pull?.doc_write_failures,
          errors: syncResult.pull?.errors
        }, null, 2));
        
        // Check if push failed (which would prevent new admissions from syncing)
        if (syncResult.push?.errors?.length) {
          console.error('[SYNC BATCH] Push errors detected:', syncResult.push.errors);
        }
        
        // Check for document write failures
        if (syncResult.push?.doc_write_failures) {
          console.error('[SYNC BATCH] Push write failures:', syncResult.push.doc_write_failures);
        }
        
        // Check if pull is overwriting local documents
        if (syncResult.pull?.docs_written && syncResult.pull.docs_written > 0) {
          console.warn('[SYNC BATCH] Pull overwrote local documents:', syncResult.pull.docs_written);
        }
        
        // Still mark as synced but with warnings
        this.updateSyncStatus('synced');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      if (this.isAuthenticationError(error)) {
        await this.handleAuthError(error);
        return; // Don't retry immediately for auth errors
      }
      
      if (process.env.NODE_ENV === 'development' || !errorMessage.includes('502')) {
        if (!this.suppressAuthErrors) {
          console.error('Sync failed:', error);
        }
      }
      
      this.updateSyncStatus('error', errorMessage);
      
      const isNetworkError = errorMessage.includes('Failed to fetch') ||
                           errorMessage.includes('NetworkError') ||
                           errorMessage.includes('network error') ||
                           errorMessage.includes('502');
      
      if (isNetworkError) {
        // Retry connection with reduced frequency to prevent server overload
        console.log('[DEBUG] SyncManager: Network error detected, retrying with backoff');
        await this.retryConnection();
      }
    } finally {
      this.syncInProgress = false;
    }
  }

  private notifyListeners() {
    const listeners = Array.from(this.syncListeners);
    listeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.error('Error in sync listener:', error);
      }
    });
  }

  async syncAfterChange(docId?: string): Promise<void> {
    const now = Date.now();
    if (docId) {
      this.changeQueue.set(docId, {
        id: docId,
        timestamp: now,
        retryCount: 0
      });
      console.log(`[SYNC QUEUE] Added document to sync queue: ${docId}. Queue size: ${this.changeQueue.size}`);
      
      // Warn if queue is getting large
      if (this.changeQueue.size > 20) {
        console.warn(`[SYNC QUEUE] Queue size is large: ${this.changeQueue.size} items. This may indicate sync performance issues.`);
      }
      
      // Log queue contents if it's getting very large
      if (this.changeQueue.size > 50) {
        const queueIds = Array.from(this.changeQueue.keys()).slice(0, 10);
        console.warn(`[SYNC QUEUE] Queue overflow risk! Size: ${this.changeQueue.size}. First 10 items: ${queueIds.join(', ')}`);
      }
    }
    this.updateSyncStatus(this.syncStatus.status);
    this.debouncedSync();
  }

  /**
   * Handles post-reset synchronization
   * Resets sync state and triggers a full sync from remote
   */
  async handlePostResetSync(): Promise<void> {
    console.log('[SYNC MANAGER] Starting post-reset synchronization...');
    
    try {
      // Reset sync state
      this.syncInProgress = false;
      this.changeQueue.clear();
      this.processingQueue = false;
      this.retryAttempts = 0;
      
      // Reset auth retry state for fresh start
      this.resetAuthRetryState();
      
      // Update status to indicate fresh sync
      this.updateSyncStatus('initial_sync');
      
      // Perform a full sync from remote
      await this.sync();
      
      console.log('[SYNC MANAGER] Post-reset synchronization completed successfully');
      
    } catch (error) {
      console.error('[SYNC MANAGER] Post-reset synchronization failed:', error);
      
      // Don't throw - allow app to work offline after reset
      if (this.isAuthenticationError(error)) {
        await this.handleAuthError(error);
      } else {
        this.updateSyncStatus('error', `Post-reset sync failed: ${error}`);
      }
    }
  }

  /**
   * Get current admission count for sync monitoring
   */
  private async getAdmissionCount(): Promise<number> {
    try {
      // Try multiple query approaches to debug the issue
      console.log(`[SYNC BATCH] === DEBUGGING ADMISSION COUNT ===`);
      
      // 1. Check total document count in database
      const allDocsResult = await this.db.allDocs({ include_docs: false });
      console.log(`[SYNC BATCH] Total documents in local DB: ${allDocsResult.total_rows}`);
      
      // 2. Try the main query
      const result = await this.db.find({
        selector: {
          documentType: 'admission',
          $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
        },
        fields: ['_id', 'createdAt']
      });
      console.log(`[SYNC BATCH] Main query found ${result.docs.length} admission documents`);
      
      // 3. Try a simpler query without the $or clause
      const simpleResult = await this.db.find({
        selector: {
          documentType: 'admission'
        },
        fields: ['_id', 'createdAt']
      });
      console.log(`[SYNC BATCH] Simple query found ${simpleResult.docs.length} admission documents`);
      
      // 4. Try using allDocs with startkey/endkey
      const allAdmissionsResult = await this.db.allDocs({
        startkey: 'admission_',
        endkey: 'admission_\ufff0',
        include_docs: false
      });
      console.log(`[SYNC BATCH] AllDocs query found ${allAdmissionsResult.rows.length} admission documents`);
      
      if (result.docs.length > 0) {
        const sortedDocs = result.docs.sort((a: any, b: any) =>
          new Date(a.createdAt || '').getTime() - new Date(b.createdAt || '').getTime()
        );
        console.log(`[SYNC BATCH] Oldest admission: ${sortedDocs[0]._id} (${(sortedDocs[0] as any).createdAt})`);
        console.log(`[SYNC BATCH] Newest admission: ${sortedDocs[sortedDocs.length - 1]._id} (${(sortedDocs[sortedDocs.length - 1] as any).createdAt})`);
      }
      
      console.log(`[SYNC BATCH] === END DEBUGGING ===`);
      return result.docs.length;
    } catch (error) {
      console.warn('[SYNC BATCH] Failed to get admission count:', error);
      return 0;
    }
  }

  // Manual CORS diagnostic test - call this from browser console
  async testCORSManually(): Promise<void> {
    console.log('🔧 Manual CORS Test Started');
    console.log('🌐 Current origin:', window.location.origin);
    
    const config = getConfig();
    const testUrl = `${config.remoteUrl}/${config.remoteName}`;
    console.log('🎯 Testing URL:', testUrl);
    
    try {
      const response = await fetch(testUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Basic ${btoa(`${config.username}:${config.password}`)}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      console.log('📊 Response status:', response.status);
      console.log('📋 All response headers:', Object.fromEntries(response.headers.entries()));
      
      const corsHeaders = {
        'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
        'access-control-allow-methods': response.headers.get('access-control-allow-methods'),
        'access-control-allow-headers': response.headers.get('access-control-allow-headers'),
        'access-control-allow-credentials': response.headers.get('access-control-allow-credentials')
      };
      console.log('🔒 CORS headers specifically:', corsHeaders);
      
      if (corsHeaders['access-control-allow-origin']) {
        console.log('✅ CORS Allow-Origin header is present:', corsHeaders['access-control-allow-origin']);
      } else {
        console.log('❌ CORS Allow-Origin header is MISSING - this is the problem!');
        console.log('💡 Your CouchDB server needs to be configured to allow origin:', window.location.origin);
      }
      
    } catch (error) {
      console.log('❌ Manual CORS test failed:', error);
      console.log('💡 This confirms CORS is blocking the request');
    }
  }

  cleanup() {
    this.debouncedSync.cancel();
    if (this.syncHandler) {
      this.syncHandler.cancel();
    }
    this.syncListeners.clear();
    if (this.remoteDb) {
      this.remoteDb.close();
    }
    this.isInitialized = false;
    this.syncInProgress = false;
    this.changeQueue.clear();
    this.processingQueue = false;
  }
}
