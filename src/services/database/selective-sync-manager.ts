import PouchDB from 'pouchdb-browser';
import { getConfig, SYNC_OPTIONS, SYNC_CONSTANTS } from './config';
import { SyncFilterConfig, DocumentTypeFilter, FestivalScopeFilter, LocationFilter, TimeWindowFilter } from './sync-filters';
import { IndexHealthMonitor } from './index-health-monitor';
import { SyncCacheManager } from './sync-cache-manager';
import { PersistentInitializationManager } from './persistent-initialization-manager';
import debounce from 'lodash/debounce';

// DIAGNOSTIC LOG: Validate imports are resolving correctly
console.log('[SELECTIVE SYNC] [DIAGNOSTIC] Import validation:', {
  SyncFilterConfig: typeof SyncFilterConfig,
  IndexHealthMonitor: typeof IndexHealthMonitor,
  SyncCacheManager: typeof SyncCacheManager,
  timestamp: new Date().toISOString()
});

interface SyncError {
  status?: number;
  name?: string;
  message?: string;
  reason?: string;
}

type ReplicationResult = PouchDB.Replication.ReplicationResultComplete<{}>;

interface SelectiveSyncResult {
  push?: ReplicationResult;
  pull?: ReplicationResult;
  status: 'complete' | 'error';
  errors: SyncError[];
  documentsProcessed: number;
  documentsFiltered: number;
  performanceMetrics: {
    syncDuration: number;
    filteringDuration: number;
    indexHealthCheckDuration: number;
  };
}

interface QueuedChange {
  id: string;
  timestamp: number;
  retryCount: number;
  festivalId?: string;
  documentType?: string;
}

type DebouncedFunction = {
  (): void;
  cancel(): void;
};

interface AuthRetryState {
  attempts: number;
  lastAttempt: number;
  backoffDelay: number;
  isAuthError: boolean;
}

interface SyncMetrics {
  totalDocuments: number;
  filteredDocuments: number;
  syncedDocuments: number;
  volumeReduction: number;
  lastSyncDuration: number;
  indexHealthScore: number;
  cacheHitRate: number;
}

/**
 * SelectiveSyncManager - Replaces the current full-database sync with intelligent filtering
 * 
 * Key Features:
 * - Festival-scoped sync (80-90% volume reduction)
 * - Document type prioritization
 * - Location-based filtering
 * - Index health monitoring and corruption recovery
 * - Intelligent caching strategy
 * - Performance metrics and monitoring
 */
export class SelectiveSyncManager {
  private db: PouchDB.Database;
  private remoteDb?: PouchDB.Database;
  private syncHandler?: PouchDB.Replication.Sync<{}>;
  private syncListeners: Set<() => void> = new Set();
  private syncStatus: {
    status: 'synced' | 'syncing' | 'error' | 'disconnected' | 'paused' | 'auth_error' | 'initial_sync';
    lastSync?: Date;
    pendingChanges: number;
    error?: string;
    authError?: boolean;
    metrics?: SyncMetrics;
  } = {
    status: 'disconnected',
    pendingChanges: 0,
    authError: false
  };

  private filterConfig: SyncFilterConfig;
  private indexHealthMonitor: IndexHealthMonitor;
  private cacheManager: SyncCacheManager;
  
  private retryAttempts: number = 0;
  private lastConnectionTest: number = 0;
  private syncInProgress: boolean = false;
  private isInitialized: boolean = false;
  private changeQueue: Map<string, QueuedChange> = new Map();
  private processingQueue: boolean = false;
  private debouncedSync: DebouncedFunction;
  
  private authRetryState: AuthRetryState = {
    attempts: 0,
    lastAttempt: 0,
    backoffDelay: 5000,
    isAuthError: false
  };
  private suppressAuthErrors: boolean = false;
  private persistentInitManager: PersistentInitializationManager;

  // Performance tracking
  private performanceMetrics = {
    totalSyncs: 0,
    totalVolumeReduction: 0,
    averageSyncDuration: 0,
    indexCorruptionEvents: 0,
    cacheHitRate: 0
  };

  constructor(db: PouchDB.Database, activeFestivalId?: string) {
    this.db = db;
    if (this.db.setMaxListeners) {
      this.db.setMaxListeners(20);
    }

    // Initialize components
    this.filterConfig = new SyncFilterConfig(activeFestivalId);
    this.indexHealthMonitor = new IndexHealthMonitor(this.db);
    this.cacheManager = new SyncCacheManager(this.db, activeFestivalId);
    this.persistentInitManager = new PersistentInitializationManager(activeFestivalId);

    // Initialize remote database connection
    this.initializeRemoteConnection();

    // Setup debounced sync with festival-aware batching
    this.debouncedSync = debounce(
      this.processChangeQueue.bind(this), 
      SYNC_CONSTANTS.DEBOUNCE_DELAY
    ) as DebouncedFunction;

    console.log('[SELECTIVE SYNC] SelectiveSyncManager initialized with festival scope:', activeFestivalId);
  }

  private initializeRemoteConnection(): void {
    const config = getConfig();
    const { remoteUrl, remoteName, username, password } = config;
    const baseUrl = remoteUrl.replace(/\/$/, '');
    const fullRemoteUrl = `${baseUrl}/${remoteName}`;

    // Create basic auth header for PouchDB
    const basicAuthHeader = username && password
      ? 'Basic ' + btoa(`${username}:${password}`)
      : undefined;

    const options = {
      ...SYNC_OPTIONS,
      auth: {
        username,
        password
      },
      ajax: {
        ...SYNC_OPTIONS.ajax,
        headers: {
          ...SYNC_OPTIONS.ajax.headers,
          ...(basicAuthHeader ? { 'Authorization': basicAuthHeader } : {})
        }
      }
    } as PouchDB.Configuration.RemoteDatabaseConfiguration;

    try {
      this.remoteDb = new PouchDB(fullRemoteUrl, options);
      if (this.remoteDb.setMaxListeners) {
        this.remoteDb.setMaxListeners(20);
      }
      this.initializeSync();
    } catch (error) {
      console.error('[SELECTIVE SYNC] Failed to initialize remote database:', error);
      this.updateSyncStatus('error', String(error));
    }
  }

  /**
   * Updates the active festival scope for filtering
   */
  public updateFestivalScope(festivalId: string): void {
    console.log('[SELECTIVE SYNC] Updating festival scope to:', festivalId);
    this.filterConfig.updateFestivalScope(festivalId);
    this.cacheManager.updateFestivalScope(festivalId);
    
    // Trigger a fresh sync with new scope
    this.triggerSelectiveSync();
  }

  /**
   * Performs selective sync with intelligent filtering
   */
  public async performSelectiveSync(): Promise<SelectiveSyncResult> {
    if (this.syncInProgress || !this.remoteDb) {
      throw new Error('Sync already in progress or remote database not available');
    }

    const startTime = Date.now();
    this.syncInProgress = true;
    this.updateSyncStatus('syncing');

    try {
      // Step 1: Index health check and recovery
      const indexHealthStart = Date.now();
      const indexHealth = await this.indexHealthMonitor.checkIndexHealth();
      
      if (indexHealth.hasCorruption) {
        console.warn('[SELECTIVE SYNC] Index corruption detected, attempting recovery...');
        await this.indexHealthMonitor.recoverCorruptedIndexes();
        this.performanceMetrics.indexCorruptionEvents++;
      }
      const indexHealthDuration = Date.now() - indexHealthStart;

      // Step 2: Apply selective filtering
      const filteringStart = Date.now();
      const filteredDocuments = await this.applySelectiveFiltering();
      const filteringDuration = Date.now() - filteringStart;

      // Step 3: Perform filtered sync
      const syncResult = await this.performFilteredSync(filteredDocuments);
      
      const totalDuration = Date.now() - startTime;

      // Step 4: Update cache and metrics
      await this.cacheManager.updateCache(filteredDocuments);
      this.updatePerformanceMetrics(syncResult, totalDuration);

      const result: SelectiveSyncResult = {
        ...syncResult,
        documentsProcessed: filteredDocuments.length,
        documentsFiltered: syncResult.documentsProcessed || 0,
        performanceMetrics: {
          syncDuration: totalDuration,
          filteringDuration,
          indexHealthCheckDuration: indexHealthDuration
        }
      };

      this.updateSyncStatus('synced');
      console.log('[SELECTIVE SYNC] Selective sync completed:', result);
      
      return result;

    } catch (error) {
      console.error('[SELECTIVE SYNC] Selective sync failed:', error);
      this.updateSyncStatus('error', String(error));
      throw error;
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Applies intelligent filtering based on festival scope and document priorities
   */
  private async applySelectiveFiltering(): Promise<string[]> {
    const startTime = Date.now();
    
    try {
      // Check cache first for performance
      const cachedDocuments = await this.cacheManager.getCachedDocuments();
      if (cachedDocuments.length > 0) {
        console.log('[SELECTIVE SYNC] Using cached document list:', cachedDocuments.length);
        return cachedDocuments;
      }

      // Apply festival scope filter (primary optimization - 80-90% reduction)
      const festivalScopedDocs = await this.filterConfig.applyFestivalScopeFilter();
      console.log('[SELECTIVE SYNC] Festival scope filter result:', festivalScopedDocs.length);

      // Apply document type prioritization
      const prioritizedDocs = await this.filterConfig.applyDocumentTypeFilter(festivalScopedDocs);
      console.log('[SELECTIVE SYNC] Document type filter result:', prioritizedDocs.length);

      // Apply location-based filtering if configured
      const locationFilteredDocs = await this.filterConfig.applyLocationFilter(prioritizedDocs);
      console.log('[SELECTIVE SYNC] Location filter result:', locationFilteredDocs.length);

      // Apply time window filtering for performance
      const timeFilteredDocs = await this.filterConfig.applyTimeWindowFilter(locationFilteredDocs);
      console.log('[SELECTIVE SYNC] Time window filter result:', timeFilteredDocs.length);

      const filteringDuration = Date.now() - startTime;
      console.log(`[SELECTIVE SYNC] Filtering completed in ${filteringDuration}ms`);

      return timeFilteredDocs;

    } catch (error) {
      console.error('[SELECTIVE SYNC] Filtering failed:', error);
      
      // Fallback to index health monitor's safe query method
      console.log('[SELECTIVE SYNC] Using fallback filtering method...');
      return await this.indexHealthMonitor.performSafeDocumentQuery();
    }
  }

  /**
   * Performs sync with filtered document set
   */
  private async performFilteredSync(documentIds: string[]): Promise<SelectiveSyncResult> {
    if (!this.remoteDb) {
      throw new Error('Remote database not available');
    }

    // Use adaptive batching based on document count
    const adaptiveBatchSize = this.calculateAdaptiveBatchSize(documentIds.length);
    
    const syncOptions = {
      ...SYNC_OPTIONS,
      batch_size: adaptiveBatchSize,
      live: false,
      // Add document filter for selective sync
      filter: (doc: any) => {
        return documentIds.includes(doc._id);
      }
    };

    console.log(`[SELECTIVE SYNC] Starting filtered sync with ${documentIds.length} documents, batch size: ${adaptiveBatchSize}`);

    try {
      const syncResult = await this.db.sync(this.remoteDb, syncOptions) as unknown as SelectiveSyncResult;
      
      // Calculate volume reduction
      const totalDocuments = await this.getTotalDocumentCount();
      const volumeReduction = ((totalDocuments - documentIds.length) / totalDocuments) * 100;
      
      console.log(`[SELECTIVE SYNC] Volume reduction achieved: ${volumeReduction.toFixed(1)}%`);
      
      return {
        ...syncResult,
        documentsProcessed: documentIds.length,
        documentsFiltered: totalDocuments - documentIds.length,
        performanceMetrics: {
          syncDuration: 0, // Will be set by caller
          filteringDuration: 0, // Will be set by caller
          indexHealthCheckDuration: 0 // Will be set by caller
        }
      };

    } catch (error) {
      console.error('[SELECTIVE SYNC] Filtered sync failed:', error);
      throw error;
    }
  }

  /**
   * Calculates adaptive batch size based on document count and rate limiting
   */
  private calculateAdaptiveBatchSize(documentCount: number): number {
    // Start with base batch size from config
    let batchSize = SYNC_OPTIONS.batch_size;

    // Reduce batch size for large document sets to prevent rate limiting
    if (documentCount > 100) {
      batchSize = Math.max(5, Math.floor(batchSize * 0.5));
    } else if (documentCount > 50) {
      batchSize = Math.max(8, Math.floor(batchSize * 0.7));
    }

    // Consider recent rate limiting events
    if (this.retryAttempts > 0) {
      batchSize = Math.max(3, Math.floor(batchSize * 0.6));
    }

    console.log(`[SELECTIVE SYNC] Adaptive batch size: ${batchSize} (documents: ${documentCount}, retries: ${this.retryAttempts})`);
    return batchSize;
  }

  /**
   * Gets total document count for volume reduction calculation
   */
  private async getTotalDocumentCount(): Promise<number> {
    try {
      const result = await this.db.allDocs({ include_docs: false });
      return result.total_rows;
    } catch (error) {
      console.warn('[SELECTIVE SYNC] Failed to get total document count:', error);
      return 0;
    }
  }

  /**
   * Updates performance metrics
   */
  private updatePerformanceMetrics(syncResult: SelectiveSyncResult, duration: number): void {
    this.performanceMetrics.totalSyncs++;
    this.performanceMetrics.averageSyncDuration = 
      (this.performanceMetrics.averageSyncDuration * (this.performanceMetrics.totalSyncs - 1) + duration) / 
      this.performanceMetrics.totalSyncs;

    if (syncResult.documentsFiltered > 0) {
      const volumeReduction = (syncResult.documentsFiltered / (syncResult.documentsProcessed + syncResult.documentsFiltered)) * 100;
      this.performanceMetrics.totalVolumeReduction = 
        (this.performanceMetrics.totalVolumeReduction * (this.performanceMetrics.totalSyncs - 1) + volumeReduction) / 
        this.performanceMetrics.totalSyncs;
    }

    this.performanceMetrics.cacheHitRate = this.cacheManager.getCacheHitRate();

    // Update sync status with metrics
    this.syncStatus.metrics = {
      totalDocuments: syncResult.documentsProcessed + syncResult.documentsFiltered,
      filteredDocuments: syncResult.documentsFiltered,
      syncedDocuments: syncResult.documentsProcessed,
      volumeReduction: this.performanceMetrics.totalVolumeReduction,
      lastSyncDuration: duration,
      indexHealthScore: this.indexHealthMonitor.getHealthScore(),
      cacheHitRate: this.performanceMetrics.cacheHitRate
    };
  }

  /**
   * Triggers selective sync (public interface)
   */
  public triggerSelectiveSync(): void {
    this.debouncedSync();
  }

  /**
   * Adds a document change to the sync queue with festival context
   */
  public async syncAfterChange(docId?: string, festivalId?: string, documentType?: string): Promise<void> {
    const now = Date.now();
    if (docId) {
      this.changeQueue.set(docId, {
        id: docId,
        timestamp: now,
        retryCount: 0,
        festivalId,
        documentType
      });
      
      console.log(`[SELECTIVE SYNC] Added document to sync queue: ${docId} (festival: ${festivalId}, type: ${documentType}). Queue size: ${this.changeQueue.size}`);
    }
    
    this.updateSyncStatus(this.syncStatus.status);
    this.debouncedSync();
  }

  /**
   * Processes the change queue with selective filtering
   */
  private async processChangeQueue(): Promise<void> {
    if (this.processingQueue || this.changeQueue.size === 0) {
      return;
    }

    // Skip processing if we have an auth error and haven't waited long enough
    if (this.authRetryState.isAuthError && !this.shouldRetryAuth()) {
      console.log('[SELECTIVE SYNC] Skipping sync due to authentication backoff period');
      return;
    }

    console.log(`[SELECTIVE SYNC] Processing queue with ${this.changeQueue.size} items`);
    this.processingQueue = true;
    this.updateSyncStatus('syncing');

    try {
      // Group changes by festival for efficient processing
      const changesByFestival = this.groupChangesByFestival();
      
      // ES5 COMPATIBILITY LOG: Using Array.from() instead of for...of on Map.entries()
      console.log('[SELECTIVE SYNC] [ES5 FIX] Converting Map.entries() to Array for ES5 compatibility');
      const festivalEntries = Array.from(changesByFestival.entries());
      
      for (let i = 0; i < festivalEntries.length; i++) {
        const [festivalId, changes] = festivalEntries[i];
        if (festivalId && festivalId !== this.filterConfig.getActiveFestivalId()) {
          // Update festival scope if needed
          this.updateFestivalScope(festivalId);
        }
        
        console.log(`[SELECTIVE SYNC] Processing ${changes.length} changes for festival: ${festivalId}`);
        await this.performSelectiveSync();
      }

      this.changeQueue.clear();
      this.updateSyncStatus('synced');

    } catch (error) {
      if (this.isAuthenticationError(error)) {
        await this.handleAuthError(error);
      } else {
        console.error('[SELECTIVE SYNC] Failed to process change queue:', error);
        this.updateSyncStatus('error', String(error));
      }
    } finally {
      this.processingQueue = false;
    }
  }

  /**
   * Groups changes by festival for efficient batch processing
   */
  private groupChangesByFestival(): Map<string, QueuedChange[]> {
    const grouped = new Map<string, QueuedChange[]>();
    
    // ES5 COMPATIBILITY LOG: Using Array.from() instead of for...of on Map.values()
    console.log('[SELECTIVE SYNC] [ES5 FIX] Converting Map.values() to Array for ES5 compatibility');
    const changeArray = Array.from(this.changeQueue.values());
    
    for (let i = 0; i < changeArray.length; i++) {
      const change = changeArray[i];
      const festivalId = change.festivalId || 'unknown';
      if (!grouped.has(festivalId)) {
        grouped.set(festivalId, []);
      }
      grouped.get(festivalId)!.push(change);
    }
    
    console.log('[SELECTIVE SYNC] [ES5 FIX] Grouped changes by festival:', grouped.size, 'festivals');
    return grouped;
  }

  /**
   * Gets current sync metrics for monitoring
   */
  public getSyncMetrics(): SyncMetrics | null {
    return this.syncStatus.metrics || null;
  }

  /**
   * Gets performance summary for debugging
   */
  public getPerformanceSummary(): any {
    return {
      ...this.performanceMetrics,
      indexHealth: this.indexHealthMonitor.getHealthSummary(),
      cacheStats: this.cacheManager.getCacheStats(),
      currentFestivalScope: this.filterConfig.getActiveFestivalId()
    };
  }

  // Legacy compatibility methods
  private updateSyncStatus(
    status: 'synced' | 'syncing' | 'error' | 'disconnected' | 'paused' | 'auth_error' | 'initial_sync',
    error?: string,
    isAuthError: boolean = false
  ): void {
    this.syncStatus = {
      status,
      lastSync: status === 'synced' ? new Date() : this.syncStatus.lastSync,
      pendingChanges: this.changeQueue.size,
      error: error,
      authError: isAuthError,
      metrics: this.syncStatus.metrics
    };
    this.notifyListeners();
  }

  private isAuthenticationError(error: any): boolean {
    const errorStr = String(error);
    return errorStr.includes('<!DOCTYPE') ||
           errorStr.includes('<html') ||
           errorStr.includes('530') ||
           errorStr.includes('Authentication failed') ||
           errorStr.includes('blocked by CORS policy') ||
           errorStr.includes('Unexpected token') ||
           (error?.status === 530);
  }

  private async handleAuthError(error: any): Promise<void> {
    const now = Date.now();
    this.authRetryState.isAuthError = true;
    
    if (this.authRetryState.attempts < 3 && !this.suppressAuthErrors) {
      console.error('[SELECTIVE SYNC] Authentication error detected:', error);
    }
    
    this.authRetryState.attempts++;
    this.authRetryState.lastAttempt = now;
    this.authRetryState.backoffDelay = Math.min(this.authRetryState.backoffDelay * 2, 120000);
    
    this.updateSyncStatus('auth_error', 'Authentication failed. App running in offline mode.', true);
  }

  private shouldRetryAuth(): boolean {
    const now = Date.now();
    const timeSinceLastAttempt = now - this.authRetryState.lastAttempt;
    return timeSinceLastAttempt >= this.authRetryState.backoffDelay;
  }

  private async initializeSync(): Promise<void> {
    if (this.isInitialized || !this.remoteDb) {
      return;
    }

    // Check if we should skip sync based on persistent state
    if (this.persistentInitManager.shouldSkipSync()) {
      console.log('[SELECTIVE SYNC] Skipping sync due to persistent initialization state');
      this.isInitialized = true;
      this.updateSyncStatus('synced');
      return;
    }

    // Mark sync as started in persistent state
    this.persistentInitManager.markSyncStarted();

    this.isInitialized = true;
    this.updateSyncStatus('synced');

    try {
      console.log('[SELECTIVE SYNC] Starting initialization with persistent tracking...');

      // Initialize index health monitoring
      await this.indexHealthMonitor.initialize();

      // Initialize cache manager
      await this.cacheManager.initialize();

      // Check for local data
      const hasData = await this.checkForLocalData();
      console.log('[SELECTIVE SYNC] Local data found:', hasData);

      if (!hasData) {
        console.log('[SELECTIVE SYNC] Starting initial selective sync...');
        const syncResult = await this.performSelectiveSync();
        // Mark sync as completed with result
        this.persistentInitManager.markSyncCompleted(syncResult.documentsProcessed > 0);
      } else {
        // Mark sync as completed since we have local data
        this.persistentInitManager.markSyncCompleted(hasData);
      }

    } catch (error) {
      this.persistentInitManager.markSyncFailed();
      if (this.isAuthenticationError(error)) {
        await this.handleAuthError(error);
      } else {
        console.warn('[SELECTIVE SYNC] Initialization failed (non-blocking):', error);
        this.updateSyncStatus('synced');
      }
    }
  }

  private async checkForLocalData(): Promise<boolean> {
    try {
      const result = await this.db.allDocs({
        limit: 1,
        include_docs: false,
        startkey: '\u0000',
        endkey: '\ufff0'
      });
      return result.rows.length > 0;
    } catch (error) {
      console.error('[SELECTIVE SYNC] Error checking for local data:', error);
      return false;
    }
  }

  private notifyListeners(): void {
    const listeners = Array.from(this.syncListeners);
    listeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.error('[SELECTIVE SYNC] Error in sync listener:', error);
      }
    });
  }

  // Public interface methods
  public addSyncListener(callback: () => void): () => void {
    this.syncListeners.add(callback);
    return () => {
      this.syncListeners.delete(callback);
    };
  }

  public getSyncStatus(): any {
    return this.syncStatus;
  }

  public hasPendingChanges(): boolean {
    return this.changeQueue.size > 0;
  }

  public async sync(): Promise<void> {
    await this.performSelectiveSync();
  }

  /**
   * Stops the selective sync process
   */
  public async stopSync(): Promise<void> {
    console.log('[SELECTIVE SYNC] Stopping sync process...');
    
    if (this.syncHandler) {
      this.syncHandler.cancel();
    }
    
    this.debouncedSync.cancel();
    this.syncInProgress = false;
    this.processingQueue = false;
    
    this.updateSyncStatus('paused');
    console.log('[SELECTIVE SYNC] Sync process stopped');
  }

  /**
   * Resets the selective sync state
   */
  public async resetSync(): Promise<void> {
    console.log('[SELECTIVE SYNC] Resetting sync state...');
    
    // Stop any active sync
    await this.stopSync();
    
    // Clear queues and reset state
    this.changeQueue.clear();
    this.retryAttempts = 0;
    this.authRetryState = {
      attempts: 0,
      lastAttempt: 0,
      backoffDelay: 5000,
      isAuthError: false
    };
    
    // Reset performance metrics
    this.performanceMetrics = {
      totalSyncs: 0,
      totalVolumeReduction: 0,
      averageSyncDuration: 0,
      indexCorruptionEvents: 0,
      cacheHitRate: 0
    };
    
    // Clear cache
    await this.cacheManager.clearCache();
    
    this.updateSyncStatus('disconnected');
    console.log('[SELECTIVE SYNC] Sync state reset completed');
  }

  /**
   * Force a sync bypassing persistent initialization checks
   */
  async forceSync(): Promise<SelectiveSyncResult> {
    console.log('[SELECTIVE SYNC] Force sync requested, bypassing persistent checks');
    this.persistentInitManager.invalidateCache();
    return await this.performSelectiveSync();
  }

  /**
   * Get the persistent initialization manager for external access
   */
  getPersistentInitManager(): PersistentInitializationManager {
    return this.persistentInitManager;
  }

  /**
   * Reset persistent initialization state
   */
  resetPersistentState(): void {
    this.persistentInitManager.reset();
    console.log('[SELECTIVE SYNC] Persistent state reset');
  }

  public cleanup(): void {
    this.debouncedSync.cancel();
    if (this.syncHandler) {
      this.syncHandler.cancel();
    }
    this.syncListeners.clear();
    if (this.remoteDb) {
      this.remoteDb.close();
    }
    this.indexHealthMonitor.cleanup();
    this.cacheManager.cleanup();
    this.isInitialized = false;
    this.syncInProgress = false;
    this.changeQueue.clear();
    this.processingQueue = false;
  }
}