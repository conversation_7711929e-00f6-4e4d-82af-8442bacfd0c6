import PouchDB from 'pouchdb-browser';
import { SelectiveSyncManager } from './selective-sync-manager';
import { IndexHealthMonitor } from './index-health-monitor';
import { SyncCacheManager } from './sync-cache-manager';
import { SyncManager } from './sync-manager';

/**
 * Migration status tracking
 */
interface MigrationStatus {
  isComplete: boolean;
  currentStep: string;
  progress: number;
  errors: string[];
  startTime: Date;
  endTime?: Date;
  rollbackAvailable: boolean;
}

/**
 * Migration configuration options
 */
interface MigrationConfig {
  enableSelectiveSync: boolean;
  preserveCache: boolean;
  performIndexHealthCheck: boolean;
  createBackup: boolean;
  activeFestivalId?: string;
}

/**
 * Data validation result
 */
interface ValidationResult {
  isValid: boolean;
  totalDocuments: number;
  validDocuments: number;
  corruptedDocuments: string[];
  missingIndexes: string[];
  recommendations: string[];
}

/**
 * Migration utilities for transitioning to selective sync system
 * 
 * Provides safe migration from legacy SyncManager to SelectiveSyncManager
 * with data validation, backup, and rollback capabilities
 */
export class MigrationUtils {
  private db: PouchDB.Database;
  private migrationStatus: MigrationStatus;
  private backupData: any = null;

  constructor(db: PouchDB.Database) {
    this.db = db;
    this.migrationStatus = {
      isComplete: false,
      currentStep: 'initialized',
      progress: 0,
      errors: [],
      startTime: new Date(),
      rollbackAvailable: false
    };
  }

  /**
   * Performs complete migration to selective sync system
   */
  public async migrateToSelectiveSync(config: MigrationConfig): Promise<MigrationStatus> {
    console.log('[MIGRATION] Starting migration to selective sync system...');
    this.updateMigrationStatus('starting', 0);

    try {
      // Step 1: Create backup if requested
      if (config.createBackup) {
        await this.createDataBackup();
        this.updateMigrationStatus('backup_created', 10);
      }

      // Step 2: Validate existing data
      const validationResult = await this.validateExistingData();
      this.updateMigrationStatus('data_validated', 20);

      if (!validationResult.isValid) {
        console.warn('[MIGRATION] Data validation issues found:', validationResult);
        // Continue with migration but log issues
      }

      // Step 3: Reset cache if not preserving
      if (!config.preserveCache) {
        await this.resetSyncCache();
        this.updateMigrationStatus('cache_reset', 30);
      }

      // Step 4: Initialize selective sync components
      await this.initializeSelectiveSyncComponents(config);
      this.updateMigrationStatus('components_initialized', 50);

      // Step 5: Perform index health check and recovery
      if (config.performIndexHealthCheck) {
        await this.performIndexHealthCheck();
        this.updateMigrationStatus('index_health_checked', 70);
      }

      // Step 6: Test selective sync functionality
      await this.testSelectiveSyncFunctionality(config);
      this.updateMigrationStatus('functionality_tested', 90);

      // Step 7: Finalize migration
      await this.finalizeMigration();
      this.updateMigrationStatus('completed', 100);

      console.log('[MIGRATION] Migration to selective sync completed successfully');
      return this.migrationStatus;

    } catch (error) {
      console.error('[MIGRATION] Migration failed:', error);
      this.migrationStatus.errors.push(String(error));
      this.updateMigrationStatus('failed', this.migrationStatus.progress);
      
      // Attempt rollback if backup is available
      if (this.migrationStatus.rollbackAvailable) {
        await this.performRollback();
      }
      
      throw error;
    }
  }

  /**
   * Creates a backup of current database state
   */
  private async createDataBackup(): Promise<void> {
    console.log('[MIGRATION] Creating data backup...');
    
    try {
      // Get all documents for backup
      const allDocs = await this.db.allDocs({
        include_docs: true,
        attachments: true
      });

      this.backupData = {
        timestamp: new Date().toISOString(),
        totalDocuments: allDocs.total_rows,
        documents: allDocs.rows.map(row => row.doc)
      };

      this.migrationStatus.rollbackAvailable = true;
      console.log(`[MIGRATION] Backup created with ${allDocs.total_rows} documents`);

    } catch (error) {
      console.error('[MIGRATION] Failed to create backup:', error);
      throw new Error(`Backup creation failed: ${error}`);
    }
  }

  /**
   * Validates existing data integrity
   */
  private async validateExistingData(): Promise<ValidationResult> {
    console.log('[MIGRATION] Validating existing data...');
    
    try {
      const allDocs = await this.db.allDocs({
        include_docs: true
      });

      let validDocuments = 0;
      const corruptedDocuments: string[] = [];
      const recommendations: string[] = [];

      // Validate each document
      for (const row of allDocs.rows) {
        if (this.validateDocument(row.doc)) {
          validDocuments++;
        } else {
          corruptedDocuments.push(row.id);
        }
      }

      // Check for missing indexes
      const missingIndexes = await this.checkForMissingIndexes();

      // Generate recommendations
      if (corruptedDocuments.length > 0) {
        recommendations.push(`${corruptedDocuments.length} corrupted documents found - consider cleanup`);
      }
      if (missingIndexes.length > 0) {
        recommendations.push(`${missingIndexes.length} missing indexes - will be recreated`);
      }

      const result: ValidationResult = {
        isValid: corruptedDocuments.length === 0 && missingIndexes.length === 0,
        totalDocuments: allDocs.total_rows,
        validDocuments,
        corruptedDocuments,
        missingIndexes,
        recommendations
      };

      console.log('[MIGRATION] Data validation completed:', result);
      return result;

    } catch (error) {
      console.error('[MIGRATION] Data validation failed:', error);
      return {
        isValid: false,
        totalDocuments: 0,
        validDocuments: 0,
        corruptedDocuments: [],
        missingIndexes: [],
        recommendations: ['Validation failed - proceed with caution']
      };
    }
  }

  /**
   * Validates a single document
   */
  private validateDocument(doc: any): boolean {
    if (!doc || !doc._id) return false;
    
    // Check for required fields based on document type
    if (doc.documentType) {
      switch (doc.documentType) {
        case 'admission':
          return !!(doc.festivalId && doc.createdAt);
        case 'festival':
          return !!(doc.name && doc.startDate);
        case 'item':
          return !!(doc.festivalId && doc.type);
        default:
          return true; // Unknown types are considered valid
      }
    }
    
    return true; // Documents without documentType are considered valid
  }

  /**
   * Checks for missing critical indexes
   */
  private async checkForMissingIndexes(): Promise<string[]> {
    try {
      const indexes = await this.db.getIndexes();
      const existingIndexNames = indexes.indexes.map(idx => idx.name);
      
      const requiredIndexes = [
        'admission-festival-index',
        'admission-location-index',
        'admission-status-index',
        'festival_documentType_index',
        'item_index',
        'feedback_index',
        'sensory-hub-festival-index'
      ];

      return requiredIndexes.filter(name => !existingIndexNames.includes(name));
    } catch (error) {
      console.warn('[MIGRATION] Could not check indexes:', error);
      return [];
    }
  }

  /**
   * Resets sync cache for fresh start
   */
  private async resetSyncCache(): Promise<void> {
    console.log('[MIGRATION] Resetting sync cache...');
    
    try {
      // Clear localStorage cache entries
      const cacheKeys = Object.keys(localStorage).filter(key => 
        key.startsWith('syncCache_') || 
        key.startsWith('festivalScope_') ||
        key.startsWith('indexHealth_')
      );

      for (const key of cacheKeys) {
        localStorage.removeItem(key);
      }

      // Clear any PouchDB cache documents
      const cacheDocsResult = await this.db.allDocs({
        startkey: '_cache_',
        endkey: '_cache_\ufff0',
        include_docs: false
      });

      if (cacheDocsResult.rows.length > 0) {
        const docsToDelete = cacheDocsResult.rows.map(row => ({
          _id: row.id,
          _rev: row.value.rev,
          _deleted: true
        }));

        await this.db.bulkDocs(docsToDelete);
      }

      console.log(`[MIGRATION] Cache reset completed - cleared ${cacheKeys.length} localStorage entries and ${cacheDocsResult.rows.length} cache documents`);

    } catch (error) {
      console.warn('[MIGRATION] Cache reset failed (non-critical):', error);
    }
  }

  /**
   * Initializes selective sync components
   */
  private async initializeSelectiveSyncComponents(config: MigrationConfig): Promise<void> {
    console.log('[MIGRATION] Initializing selective sync components...');
    
    try {
      // Initialize IndexHealthMonitor
      const indexHealthMonitor = new IndexHealthMonitor(this.db);
      await indexHealthMonitor.initialize();
      console.log('[MIGRATION] IndexHealthMonitor initialized');

      // Initialize SyncCacheManager
      const syncCacheManager = new SyncCacheManager(this.db, config.activeFestivalId);
      await syncCacheManager.initialize();
      console.log('[MIGRATION] SyncCacheManager initialized');

      // Initialize SelectiveSyncManager
      const selectiveSyncManager = new SelectiveSyncManager(this.db, config.activeFestivalId);
      console.log('[MIGRATION] SelectiveSyncManager initialized');

      // Store initialization success flag
      localStorage.setItem('selectiveSyncInitialized', 'true');
      localStorage.setItem('selectiveSyncInitializedAt', new Date().toISOString());

    } catch (error) {
      console.error('[MIGRATION] Component initialization failed:', error);
      throw new Error(`Component initialization failed: ${error}`);
    }
  }

  /**
   * Performs comprehensive index health check
   */
  private async performIndexHealthCheck(): Promise<void> {
    console.log('[MIGRATION] Performing index health check...');
    
    try {
      const indexHealthMonitor = new IndexHealthMonitor(this.db);
      await indexHealthMonitor.initialize();
      
      const healthSummary = await indexHealthMonitor.checkIndexHealth();
      
      if (healthSummary.hasCorruption) {
        console.warn('[MIGRATION] Index corruption detected, performing recovery...');
        await indexHealthMonitor.recoverCorruptedIndexes();
        
        // Verify recovery
        const postRecoveryHealth = await indexHealthMonitor.checkIndexHealth();
        if (postRecoveryHealth.hasCorruption) {
          throw new Error('Index recovery failed - some indexes still corrupted');
        }
      }

      console.log('[MIGRATION] Index health check completed successfully');

    } catch (error) {
      console.error('[MIGRATION] Index health check failed:', error);
      throw new Error(`Index health check failed: ${error}`);
    }
  }

  /**
   * Tests selective sync functionality
   */
  private async testSelectiveSyncFunctionality(config: MigrationConfig): Promise<void> {
    console.log('[MIGRATION] Testing selective sync functionality...');
    
    try {
      const selectiveSyncManager = new SelectiveSyncManager(this.db, config.activeFestivalId);
      
      // Test sync metrics retrieval
      const metrics = selectiveSyncManager.getSyncMetrics();
      console.log('[MIGRATION] Sync metrics test:', metrics !== null ? 'PASS' : 'FAIL');

      // Test performance summary
      const performanceSummary = selectiveSyncManager.getPerformanceSummary();
      console.log('[MIGRATION] Performance summary test:', performanceSummary ? 'PASS' : 'FAIL');

      // Test sync status
      const syncStatus = selectiveSyncManager.getSyncStatus();
      console.log('[MIGRATION] Sync status test:', syncStatus ? 'PASS' : 'FAIL');

      // Test festival scope update if festival ID provided
      if (config.activeFestivalId) {
        selectiveSyncManager.updateFestivalScope(config.activeFestivalId);
        console.log('[MIGRATION] Festival scope update test: PASS');
      }

      console.log('[MIGRATION] Selective sync functionality tests completed');

    } catch (error) {
      console.error('[MIGRATION] Functionality test failed:', error);
      throw new Error(`Functionality test failed: ${error}`);
    }
  }

  /**
   * Finalizes the migration process
   */
  private async finalizeMigration(): Promise<void> {
    console.log('[MIGRATION] Finalizing migration...');
    
    try {
      // Set feature flag to enable selective sync
      localStorage.setItem('useSelectiveSync', 'true');
      localStorage.setItem('migrationCompletedAt', new Date().toISOString());
      
      // Clear backup data to free memory
      this.backupData = null;
      this.migrationStatus.rollbackAvailable = false;

      console.log('[MIGRATION] Migration finalized successfully');

    } catch (error) {
      console.error('[MIGRATION] Migration finalization failed:', error);
      throw new Error(`Migration finalization failed: ${error}`);
    }
  }

  /**
   * Performs rollback to previous state
   */
  private async performRollback(): Promise<void> {
    console.log('[MIGRATION] Performing rollback...');
    
    try {
      if (!this.backupData) {
        throw new Error('No backup data available for rollback');
      }

      // Clear current database
      const allDocs = await this.db.allDocs();
      const docsToDelete = allDocs.rows.map(row => ({
        _id: row.id,
        _rev: row.value.rev,
        _deleted: true
      }));

      if (docsToDelete.length > 0) {
        await this.db.bulkDocs(docsToDelete);
      }

      // Restore backup data
      if (this.backupData.documents && this.backupData.documents.length > 0) {
        await this.db.bulkDocs(this.backupData.documents);
      }

      // Clear selective sync flags
      localStorage.removeItem('useSelectiveSync');
      localStorage.removeItem('selectiveSyncInitialized');
      localStorage.removeItem('migrationCompletedAt');

      console.log('[MIGRATION] Rollback completed successfully');

    } catch (error) {
      console.error('[MIGRATION] Rollback failed:', error);
      throw new Error(`Rollback failed: ${error}`);
    }
  }

  /**
   * Updates migration status
   */
  private updateMigrationStatus(step: string, progress: number): void {
    this.migrationStatus.currentStep = step;
    this.migrationStatus.progress = progress;
    
    if (progress >= 100) {
      this.migrationStatus.isComplete = true;
      this.migrationStatus.endTime = new Date();
    }

    console.log(`[MIGRATION] Status: ${step} (${progress}%)`);
  }

  /**
   * Gets current migration status
   */
  public getMigrationStatus(): MigrationStatus {
    return { ...this.migrationStatus };
  }

  /**
   * Checks if migration is needed
   */
  public static isMigrationNeeded(): boolean {
    const useSelectiveSync = localStorage.getItem('useSelectiveSync') === 'true';
    const isInitialized = localStorage.getItem('selectiveSyncInitialized') === 'true';
    
    // Migration needed if selective sync is enabled but not initialized
    return useSelectiveSync && !isInitialized;
  }

  /**
   * Gets migration recommendations
   */
  public static getMigrationRecommendations(): string[] {
    const recommendations: string[] = [];
    
    if (MigrationUtils.isMigrationNeeded()) {
      recommendations.push('Selective sync migration is required');
    }

    const lastMigration = localStorage.getItem('migrationCompletedAt');
    if (lastMigration) {
      const migrationDate = new Date(lastMigration);
      const daysSinceMigration = (Date.now() - migrationDate.getTime()) / (1000 * 60 * 60 * 24);
      
      if (daysSinceMigration > 30) {
        recommendations.push('Consider updating selective sync configuration');
      }
    }

    return recommendations;
  }

  /**
   * Cleanup method
   */
  public cleanup(): void {
    this.backupData = null;
    console.log('[MIGRATION] MigrationUtils cleaned up');
  }
}