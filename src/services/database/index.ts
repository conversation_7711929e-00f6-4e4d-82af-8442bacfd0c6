import PouchDB from 'pouchdb-browser';
import PouchDBFind from 'pouchdb-find';
import { AdmissionManager } from './admission-manager';
import { FestivalManager } from './festival-manager';
import { ItemManager } from './item-manager';
import { LostPropertyManager } from './lost-property-manager';
import { ShiftManager } from './shift-manager';
import { FeedbackManager } from './feedback-manager';
import { BayManager, BayStatus } from './bay-manager';
import { KnowledgeBaseManager } from './knowledgebase-manager';
import { SensoryHubManager } from './sensory-hub-manager';
import { accessManager, AccessRole, PageAccess, UserRole } from './access-manager';
import { SyncManager } from './sync-manager';
import { SelectiveSyncManager } from './selective-sync-manager';
import { IndexHealthMonitor } from './index-health-monitor';
import { SyncCacheManager } from './sync-cache-manager';
import {
  WelfareAdmission,
  NewWelfareAdmission,
  ItemDocument,
  ItemName,
  Festival,
  NewFestival,
  LostPropertyItem,
  ShiftConfig,
  NewShiftConfig,
  TeamLeader,
  NewTeamLeader,
  ShiftAssignment,
  NewShiftAssignment,
  Feedback,
  FeedbackInput,
  KnowledgeBaseItem
} from '../../types/index';
import { SensoryHubVisit, NewSensoryHubVisit } from '../../types/sensory-hub';
import { getConfig, initializeConfig } from './config';
import { AdmissionFormData } from '../../types/forms';

PouchDB.plugin(PouchDBFind);

// Simplified Database Service - Direct PouchDB access without complex proxy patterns
class SimplifiedDatabaseService {
  private db!: PouchDB.Database;
  private syncManager!: SyncManager;
  private admissionManager!: AdmissionManager;
  private festivalManager!: FestivalManager;
  private itemManager!: ItemManager;
  private lostPropertyManager!: LostPropertyManager;
  private shiftManager!: ShiftManager;
  private feedbackManager!: FeedbackManager;
  private bayManager!: BayManager;
  private knowledgeBaseManager!: KnowledgeBaseManager;
  private sensoryHubManager!: SensoryHubManager;
  private isInitialized: boolean = false;
  private initializationPromise: Promise<void> | null = null;
  
  // Selective sync components
  private useSelectiveSync: boolean = false;
  private selectiveSyncManager?: SelectiveSyncManager;
  private indexHealthMonitor?: IndexHealthMonitor;
  private syncCacheManager?: SyncCacheManager;

  constructor() {
    console.log('[DEBUG] SimplifiedDatabaseService: Starting initialization at:', new Date().toISOString());
    
    // Store the initialization promise so other methods can wait for it
    this.initializationPromise = this.initializeSync();
  }

  private async initializeSync() {
    try {
      // Initialize configuration
      await initializeConfig();
      const config = getConfig();
      
      console.log('[DEBUG] SimplifiedDatabaseService: Creating PouchDB instance...');
      this.db = new PouchDB(config.localName);
      
      // Check feature flag for selective sync
      this.useSelectiveSync = this.getSelectiveSyncFeatureFlag();
      console.log('[DEBUG] SimplifiedDatabaseService: Selective sync enabled:', this.useSelectiveSync);
      
      // Initialize sync components based on feature flag
      if (this.useSelectiveSync) {
        console.log('[DEBUG] SimplifiedDatabaseService: Creating SelectiveSyncManager...');
        this.selectiveSyncManager = new SelectiveSyncManager(this.db);
        
        console.log('[DEBUG] SimplifiedDatabaseService: Creating IndexHealthMonitor...');
        this.indexHealthMonitor = new IndexHealthMonitor(this.db);
        
        console.log('[DEBUG] SimplifiedDatabaseService: Creating SyncCacheManager...');
        this.syncCacheManager = new SyncCacheManager(this.db);
        
        // Initialize index health monitoring
        await this.indexHealthMonitor.initialize();
      } else {
        console.log('[DEBUG] SimplifiedDatabaseService: Creating legacy SyncManager...');
        this.syncManager = new SyncManager(this.db);
      }
      
      // Initialize all managers with appropriate sync manager
      if (this.useSelectiveSync && this.selectiveSyncManager) {
        // For selective sync, we need to ensure compatibility with existing managers
        // Cast to SyncManager interface for now - TODO: Update manager interfaces
        const compatibleSyncManager = this.selectiveSyncManager as any;
        this.admissionManager = new AdmissionManager(this.db, compatibleSyncManager);
        this.festivalManager = new FestivalManager(this.db, compatibleSyncManager);
        this.itemManager = new ItemManager(this.db, compatibleSyncManager);
        this.lostPropertyManager = new LostPropertyManager(this.db, compatibleSyncManager);
        this.shiftManager = new ShiftManager(this.db, compatibleSyncManager);
        this.feedbackManager = new FeedbackManager(this.db, compatibleSyncManager);
        this.bayManager = new BayManager(this.db, compatibleSyncManager);
        this.knowledgeBaseManager = new KnowledgeBaseManager(this.db, compatibleSyncManager);
        this.sensoryHubManager = new SensoryHubManager(this.db, compatibleSyncManager);
      } else {
        this.admissionManager = new AdmissionManager(this.db, this.syncManager);
        this.festivalManager = new FestivalManager(this.db, this.syncManager);
        this.itemManager = new ItemManager(this.db, this.syncManager);
        this.lostPropertyManager = new LostPropertyManager(this.db, this.syncManager);
        this.shiftManager = new ShiftManager(this.db, this.syncManager);
        this.feedbackManager = new FeedbackManager(this.db, this.syncManager);
        this.bayManager = new BayManager(this.db, this.syncManager);
        this.knowledgeBaseManager = new KnowledgeBaseManager(this.db, this.syncManager);
        this.sensoryHubManager = new SensoryHubManager(this.db, this.syncManager);
      }
      
      this.isInitialized = true;
      console.log('[DEBUG] SimplifiedDatabaseService: Initialization complete at:', new Date().toISOString());
      
      // Start background sync without blocking
      this.startBackgroundSync();
      
    } catch (error) {
      console.error('[DEBUG] SimplifiedDatabaseService: Initialization failed:', error);
      // Don't throw - allow app to work offline
    }
  }

  private startBackgroundSync() {
    // Start sync in background without blocking data access
    setTimeout(() => {
      try {
        console.log('[DEBUG] SimplifiedDatabaseService: Starting background sync...');
        const activeSyncManager = this.getActiveSyncManager();
        if (activeSyncManager) {
          activeSyncManager.sync().catch((error: any) => {
            console.warn('[DEBUG] SimplifiedDatabaseService: Background sync failed (non-blocking):', error);
          });
        }
      } catch (error) {
        console.warn('[DEBUG] SimplifiedDatabaseService: Background sync error (non-blocking):', error);
      }
    }, 100); // Small delay to ensure UI is responsive
  }

  // Feature flag for selective sync
  private getSelectiveSyncFeatureFlag(): boolean {
    // Check localStorage for user preference
    const userPreference = localStorage.getItem('useSelectiveSync');
    if (userPreference !== null) {
      return userPreference === 'true';
    }
    
    // Check environment variable
    if (process.env.REACT_APP_USE_SELECTIVE_SYNC === 'true') {
      return true;
    }
    
    // Default to false for gradual rollout
    return false;
  }

  // Wait for initialization to complete
  async waitForInitialization(): Promise<void> {
    if (this.initializationPromise) {
      await this.initializationPromise;
    }
  }

  // Ensure database is initialized (alias for waitForInitialization)
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.waitForInitialization();
    }
  }

  // Direct data access methods - no complex caching or blocking
  
  // Admission Methods
  async addAdmission(data: AdmissionFormData): Promise<WelfareAdmission> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.admissionManager.addAdmission(data);
    // Trigger background sync after data changes
    this.triggerBackgroundSync();
    return result;
  }

  async getAdmissionsByFestival(festivalId: string): Promise<WelfareAdmission[]> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    return this.admissionManager.getAdmissionsByFestival(festivalId, false);
  }

  async getAdmissionsByLocation(location: string): Promise<WelfareAdmission[]> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    return this.admissionManager.getAdmissionsByLocation(location, false);
  }

  async getAdmissionById(id: string): Promise<WelfareAdmission | null> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    return this.admissionManager.getAdmissionById(id);
  }

  async updateAdmission(data: AdmissionFormData): Promise<WelfareAdmission> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.admissionManager.updateAdmission(data);
    this.triggerBackgroundSync();
    return result;
  }

  async deleteAdmission(id: string): Promise<void> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    await this.admissionManager.deleteAdmission(id);
    this.triggerBackgroundSync();
  }

  async bulkDeleteAdmissions(ids: string[]): Promise<void> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    await this.admissionManager.bulkDeleteAdmissions(ids);
    this.triggerBackgroundSync();
  }

  async fixInconsistentAdmissions(): Promise<void> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    return this.admissionManager.fixInconsistentAdmissions();
  }

  // Festival Methods
  async addFestival(festival: NewFestival): Promise<Festival> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.festivalManager.addFestival(festival);
    this.triggerBackgroundSync();
    return result;
  }

  async getFestivals(): Promise<Festival[]> {
    if (!this.isInitialized) {
      await this.waitForInitialization();
    }
    return this.festivalManager.getFestivals(false);
  }

  async updateFestival(festival: Festival): Promise<Festival> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.festivalManager.updateFestival(festival);
    this.triggerBackgroundSync();
    return result;
  }

  async deleteFestival(id: string): Promise<void> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    await this.festivalManager.deleteFestival(id);
    this.triggerBackgroundSync();
  }

  async getFestivalNotes(festivalId: string): Promise<string> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    return this.festivalManager.getFestivalNotes(festivalId);
  }

  async updateFestivalNotes(festivalId: string, notes: string): Promise<void> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    await this.festivalManager.updateFestivalNotes(festivalId, notes);
    this.triggerBackgroundSync();
  }

  // Item Methods
  async addOrUpdateItemCount(itemName: ItemName, festivalId: string, location?: string, quantity?: number): Promise<ItemDocument> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.itemManager.addOrUpdateItemCount(itemName, festivalId, location, quantity);
    this.triggerBackgroundSync();
    return result;
  }

  async getItemCountsByFestival(festivalId: string): Promise<ItemDocument[]> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    return this.itemManager.getItemCountsByFestival(festivalId, false);
  }

  async updateItemCount(item: ItemDocument): Promise<ItemDocument> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.itemManager.updateItemCount(item);
    this.triggerBackgroundSync();
    return result;
  }

  async deleteItemCount(id: string): Promise<void> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    await this.itemManager.deleteItemCount(id);
    this.triggerBackgroundSync();
  }

  async bulkDeleteItems(ids: string[]): Promise<void> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    await this.itemManager.bulkDeleteItems(ids);
    this.triggerBackgroundSync();
  }

  // Lost Property Methods
  async addLostPropertyItem(item: Omit<LostPropertyItem, '_id' | '_rev'>): Promise<LostPropertyItem> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.lostPropertyManager.addLostPropertyItem(item);
    this.triggerBackgroundSync();
    return result;
  }

  async getLostPropertyItems(festivalId?: string): Promise<LostPropertyItem[]> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    return this.lostPropertyManager.getLostPropertyItems(festivalId, false);
  }

  async updateLostPropertyItem(item: LostPropertyItem): Promise<LostPropertyItem> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.lostPropertyManager.updateLostPropertyItem(item);
    this.triggerBackgroundSync();
    return result;
  }

  async deleteLostPropertyItem(id: string): Promise<void> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    await this.lostPropertyManager.deleteLostPropertyItem(id);
    this.triggerBackgroundSync();
  }

  async bulkDeleteLostProperty(ids: string[]): Promise<void> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    await this.lostPropertyManager.bulkDeleteLostProperty(ids);
    this.triggerBackgroundSync();
  }

  // Shift Management Methods
  async getShiftConfig(festivalId: string): Promise<ShiftConfig | null> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    return this.shiftManager.getShiftConfig(festivalId);
  }

  async saveShiftConfig(config: NewShiftConfig): Promise<ShiftConfig> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.shiftManager.saveShiftConfig(config);
    this.triggerBackgroundSync();
    return result;
  }

  async getTeamLeaders(festivalId: string): Promise<TeamLeader[]> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    return this.shiftManager.getTeamLeaders(festivalId);
  }

  async addTeamLeader(leader: NewTeamLeader): Promise<TeamLeader> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.shiftManager.addTeamLeader(leader);
    this.triggerBackgroundSync();
    return result;
  }

  async updateTeamLeader(leader: TeamLeader): Promise<TeamLeader> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.shiftManager.updateTeamLeader(leader);
    this.triggerBackgroundSync();
    return result;
  }

  async deleteTeamLeader(id: string): Promise<void> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    await this.shiftManager.deleteTeamLeader(id);
    this.triggerBackgroundSync();
  }

  async getShiftAssignments(festivalId: string): Promise<ShiftAssignment[]> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    return this.shiftManager.getShiftAssignments(festivalId);
  }

  async addShiftAssignment(assignment: NewShiftAssignment): Promise<ShiftAssignment> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.shiftManager.addShiftAssignment(assignment);
    this.triggerBackgroundSync();
    return result;
  }

  async updateShiftAssignment(assignment: ShiftAssignment): Promise<ShiftAssignment> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.shiftManager.updateShiftAssignment(assignment);
    this.triggerBackgroundSync();
    return result;
  }

  async deleteShiftAssignment(id: string): Promise<void> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    await this.shiftManager.deleteShiftAssignment(id);
    this.triggerBackgroundSync();
  }

  async generateShiftSchedule(festivalId: string, startDate: string, endDate: string, config: ShiftConfig): Promise<ShiftAssignment[]> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.shiftManager.generateShiftSchedule(festivalId, startDate, endDate, config);
    this.triggerBackgroundSync();
    return result;
  }

  // Feedback Methods
  async addFeedback(feedback: FeedbackInput): Promise<Feedback> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.feedbackManager.addFeedback(feedback);
    this.triggerBackgroundSync();
    return result;
  }

  async getAllFeedback(): Promise<Feedback[]> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    return this.feedbackManager.getAllFeedback(false);
  }

  async getUnresolvedFeedback(): Promise<Feedback[]> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    return this.feedbackManager.getUnresolvedFeedback(false);
  }

  async updateFeedbackStatus(id: string, resolved: boolean): Promise<Feedback> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.feedbackManager.updateFeedbackStatus(id, resolved);
    this.triggerBackgroundSync();
    return result;
  }

  async deleteFeedback(id: string): Promise<void> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    await this.feedbackManager.deleteFeedback(id);
    this.triggerBackgroundSync();
  }

  // Knowledge Base Methods
  async getKnowledgeBaseItems(festivalId: string): Promise<KnowledgeBaseItem[]> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    return this.knowledgeBaseManager.getKnowledgeBaseItems(festivalId);
  }

  async getKnowledgeBaseCategories(festivalId: string): Promise<string[]> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    return this.knowledgeBaseManager.getKnowledgeBaseCategories(festivalId);
  }

  async addKnowledgeBaseItem(item: Omit<KnowledgeBaseItem, '_id' | '_rev' | 'createdAt'>): Promise<KnowledgeBaseItem> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.knowledgeBaseManager.addKnowledgeBaseItem(item);
    this.triggerBackgroundSync();
    return result;
  }

  async updateKnowledgeBaseItem(item: KnowledgeBaseItem): Promise<KnowledgeBaseItem> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.knowledgeBaseManager.updateKnowledgeBaseItem(item);
    this.triggerBackgroundSync();
    return result;
  }

  async deleteKnowledgeBaseItem(id: string): Promise<void> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    await this.knowledgeBaseManager.deleteKnowledgeBaseItem(id);
    this.triggerBackgroundSync();
  }

  // Bay Management Methods
  async listAdmissionsForBay(bayNumber: number): Promise<WelfareAdmission[]> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    return this.bayManager.listAdmissionsForBay(bayNumber);
  }

  async checkBayAvailability(bayNumber: number): Promise<BayStatus> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    return this.bayManager.checkBayAvailability(bayNumber);
  }

  async validateBayAssignment(bayNumber: number, admissionId: string): Promise<boolean> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    return this.bayManager.validateBayAssignment(bayNumber, admissionId);
  }

  async assignToBay(admissionId: string, bayNumber: number): Promise<void> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    await this.bayManager.assignToBay(admissionId, bayNumber);
    this.triggerBackgroundSync();
  }

  async removeFromBay(admissionId: string): Promise<void> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    await this.bayManager.removeFromBay(admissionId);
    this.triggerBackgroundSync();
  }

  // Sensory Hub Methods
  async addSensoryHubVisit(visitData: NewSensoryHubVisit): Promise<SensoryHubVisit> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.sensoryHubManager.addVisit(visitData);
    this.triggerBackgroundSync();
    return result;
  }

  async getSensoryHubVisitsByFestival(festivalId: string): Promise<SensoryHubVisit[]> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    return this.sensoryHubManager.getVisitsByFestival(festivalId, false);
  }

  async getSensoryHubVisitsByLocation(siteLocationId: string): Promise<SensoryHubVisit[]> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    return this.sensoryHubManager.getVisitsByLocation(siteLocationId, false);
  }

  async updateSensoryHubVisit(visitData: SensoryHubVisit): Promise<SensoryHubVisit> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    const result = await this.sensoryHubManager.updateVisit(visitData);
    this.triggerBackgroundSync();
    return result;
  }

  async deleteSensoryHubVisit(id: string): Promise<void> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    await this.sensoryHubManager.deleteVisit(id);
    this.triggerBackgroundSync();
  }

  async bulkDeleteVisits(ids: string[]): Promise<void> {
    if (!this.isInitialized) throw new Error('Database not initialized');
    await this.sensoryHubManager.bulkDeleteVisits(ids);
    this.triggerBackgroundSync();
  }

  // Access Management Methods
  async getAllPageAccess(): Promise<PageAccess[]> {
    return accessManager.getAllPageAccess();
  }

  async getPageAccess(pageId: string): Promise<PageAccess | null> {
    return accessManager.getPageAccess(pageId);
  }

  async setPageAccess(pageId: string, userId: string, role: AccessRole): Promise<boolean> {
    const result = await accessManager.setPageAccess(pageId, userId, role);
    this.triggerBackgroundSync();
    return result;
  }

  async getAllUserRoles(): Promise<UserRole[]> {
    return accessManager.getAllUserRoles();
  }

  async getUserRole(userId: string): Promise<UserRole | null> {
    return accessManager.getUserRole(userId);
  }

  async setUserRole(userId: string, role: AccessRole): Promise<boolean> {
    const result = await accessManager.setUserRole(userId, role);
    this.triggerBackgroundSync();
    return result;
  }

  async deleteUserRole(userId: string): Promise<boolean> {
    const result = await accessManager.deleteUserRole(userId);
    this.triggerBackgroundSync();
    return result;
  }

  // Sync Methods - Non-blocking background operations
  sync(): void {
    const activeSyncManager = this.getActiveSyncManager();
    if (activeSyncManager) {
      this.triggerBackgroundSync();
    }
  }

  addSyncListener(callback: () => void): () => void {
    const activeSyncManager = this.getActiveSyncManager();
    if (!activeSyncManager) return () => {};
    return activeSyncManager.addSyncListener(callback);
  }

  async manualSync(): Promise<void> {
    const activeSyncManager = this.getActiveSyncManager();
    if (!activeSyncManager) return;
    try {
      await activeSyncManager.sync();
    } catch (error) {
      console.warn('Manual sync failed (non-blocking):', error);
    }
  }

  hasPendingChanges(): boolean {
    const activeSyncManager = this.getActiveSyncManager();
    if (!activeSyncManager) return false;
    return activeSyncManager.hasPendingChanges();
  }

  getSyncStatus(): any {
    const activeSyncManager = this.getActiveSyncManager();
    if (!activeSyncManager) return { status: 'disconnected' };
    return activeSyncManager.getSyncStatus();
  }

  getSyncManager(): any {
    return this.getActiveSyncManager();
  }

  // Get the currently active sync manager based on feature flag
  private getActiveSyncManager(): any {
    return this.useSelectiveSync ? this.selectiveSyncManager : this.syncManager;
  }

  // Background sync trigger - non-blocking
  private triggerBackgroundSync() {
    const activeSyncManager = this.getActiveSyncManager();
    if (activeSyncManager) {
      setTimeout(() => {
        if (this.useSelectiveSync && this.selectiveSyncManager) {
          // Use selective sync's syncAfterChange method
          this.selectiveSyncManager.syncAfterChange().catch((error: any) => {
            console.warn('Background sync after change failed (non-blocking):', error);
          });
        } else if (this.syncManager) {
          // Use legacy sync manager
          this.syncManager.syncAfterChange().catch((error: any) => {
            console.warn('Background sync after change failed (non-blocking):', error);
          });
        }
      }, 100);
    }
  }

  // Comprehensive database cleanup method
  async performDatabaseCleanup(): Promise<{
    totalCleaned: number;
    cleanupSummary: {
      admissions: number;
      feedback: number;
      items: number;
      lostProperty: number;
      shifts: number;
    };
    errors: string[];
  }> {
    if (!this.isInitialized) {
      throw new Error('Database not initialized');
    }
    
    console.log('[DATABASE CLEANUP] Starting comprehensive database cleanup...');
    
    // Calculate 3-month cutoff date
    const cutoff = new Date();
    cutoff.setMonth(cutoff.getMonth() - 3);
    const cutoffTimestamp = cutoff.toISOString();
    
    console.log(`[DATABASE CLEANUP] Cutoff date: ${cutoffTimestamp}`);

    const cleanupSummary = {
      admissions: 0,
      feedback: 0,
      items: 0,
      lostProperty: 0,
      shifts: 0,
      sensoryHubVisits: 0
    };
    
    const errors: string[] = [];
    let totalCleaned = 0;

    // Clean up admissions (old and discharged)
    try {
      console.log('[DATABASE CLEANUP] Cleaning up admissions...');
      cleanupSummary.admissions = await this.admissionManager.cleanupOldAdmissions(cutoffTimestamp);
      totalCleaned += cleanupSummary.admissions;
    } catch (error) {
      const errorMsg = `Failed to cleanup admissions: ${error}`;
      console.error(`[DATABASE CLEANUP] ${errorMsg}`);
      errors.push(errorMsg);
    }

    // Clean up feedback (old and resolved)
    try {
      console.log('[DATABASE CLEANUP] Cleaning up feedback...');
      cleanupSummary.feedback = await this.feedbackManager.cleanupOldFeedback(cutoffTimestamp);
      totalCleaned += cleanupSummary.feedback;
    } catch (error) {
      const errorMsg = `Failed to cleanup feedback: ${error}`;
      console.error(`[DATABASE CLEANUP] ${errorMsg}`);
      errors.push(errorMsg);
    }

    // Clean up items (old daily aggregations)
    try {
      console.log('[DATABASE CLEANUP] Cleaning up item aggregations...');
      cleanupSummary.items = await this.itemManager.cleanupOldItems(cutoffTimestamp);
      totalCleaned += cleanupSummary.items;
    } catch (error) {
      const errorMsg = `Failed to cleanup items: ${error}`;
      console.error(`[DATABASE CLEANUP] ${errorMsg}`);
      errors.push(errorMsg);
    }

    // Clean up lost property (old and claimed)
    try {
      console.log('[DATABASE CLEANUP] Cleaning up lost property...');
      cleanupSummary.lostProperty = await this.lostPropertyManager.cleanupOldLostProperty(cutoffTimestamp);
      totalCleaned += cleanupSummary.lostProperty;
    } catch (error) {
      const errorMsg = `Failed to cleanup lost property: ${error}`;
      console.error(`[DATABASE CLEANUP] ${errorMsg}`);
      errors.push(errorMsg);
    }

    // Clean up shifts (old completed assignments)
    try {
      console.log('[DATABASE CLEANUP] Cleaning up shift assignments...');
      cleanupSummary.shifts = await this.shiftManager.cleanupOldShifts(cutoffTimestamp);
      totalCleaned += cleanupSummary.shifts;
    } catch (error) {
      const errorMsg = `Failed to cleanup shifts: ${error}`;
      console.error(`[DATABASE CLEANUP] ${errorMsg}`);
      errors.push(errorMsg);
    }

    // Clean up sensory hub visits (old visits)
    try {
      console.log('[DATABASE CLEANUP] Cleaning up sensory hub visits...');
      cleanupSummary.sensoryHubVisits = await this.sensoryHubManager.cleanupOldVisits(cutoffTimestamp);
      totalCleaned += cleanupSummary.sensoryHubVisits;
    } catch (error) {
      const errorMsg = `Failed to cleanup sensory hub visits: ${error}`;
      console.error(`[DATABASE CLEANUP] ${errorMsg}`);
      errors.push(errorMsg);
    }

    // Final sync to ensure all changes are persisted
    try {
      if (totalCleaned > 0) {
        console.log('[DATABASE CLEANUP] Performing final sync...');
        await this.syncManager.sync();
      }
    } catch (error) {
      const errorMsg = `Failed final sync after cleanup: ${error}`;
      console.error(`[DATABASE CLEANUP] ${errorMsg}`);
      errors.push(errorMsg);
    }

    const result = {
      totalCleaned,
      cleanupSummary,
      errors
    };

    console.log('[DATABASE CLEANUP] Cleanup completed:', result);
    return result;
  }

  // Legacy method for backward compatibility
  async cleanupOldRecords(): Promise<void> {
    try {
      await this.performDatabaseCleanup();
    } catch (error) {
      console.warn('Cleanup failed (non-blocking):', error);
    }
  }

  // Database Export Service
  async exportCompleteDatabase(format: 'json' | 'csv' = 'json'): Promise<Blob> {
    try {
      console.log(`[EXPORT] Starting complete database export in ${format} format...`);
      
      await this.ensureInitialized();
      
      const exportData: any = {
        metadata: {
          exportTimestamp: new Date().toISOString(),
          exportFormat: format,
          databaseVersion: '1.8.1',
          exportedBy: 'iThinc Welfare Management System'
        },
        data: {},
        recordCounts: {}
      };

      const errors: string[] = [];

      // Export admissions
      try {
        const admissions = await this.admissionManager.exportAllAdmissions();
        exportData.data.admissions = admissions;
        exportData.recordCounts.admissions = admissions.length;
        console.log(`[EXPORT] Exported ${admissions.length} admissions`);
      } catch (error) {
        console.error('[EXPORT] Failed to export admissions:', error);
        errors.push('admissions');
        exportData.data.admissions = [];
        exportData.recordCounts.admissions = 0;
      }

      // Export festivals
      try {
        const festivals = await this.festivalManager.exportAllFestivals();
        exportData.data.festivals = festivals;
        exportData.recordCounts.festivals = festivals.length;
        console.log(`[EXPORT] Exported ${festivals.length} festivals`);
      } catch (error) {
        console.error('[EXPORT] Failed to export festivals:', error);
        errors.push('festivals');
        exportData.data.festivals = [];
        exportData.recordCounts.festivals = 0;
      }

      // Export items
      try {
        const items = await this.itemManager.exportAllItems();
        exportData.data.items = items;
        exportData.recordCounts.items = items.length;
        console.log(`[EXPORT] Exported ${items.length} items`);
      } catch (error) {
        console.error('[EXPORT] Failed to export items:', error);
        errors.push('items');
        exportData.data.items = [];
        exportData.recordCounts.items = 0;
      }

      // Export lost property
      try {
        const lostProperty = await this.lostPropertyManager.exportAllLostProperty();
        exportData.data.lostProperty = lostProperty;
        exportData.recordCounts.lostProperty = lostProperty.length;
        console.log(`[EXPORT] Exported ${lostProperty.length} lost property items`);
      } catch (error) {
        console.error('[EXPORT] Failed to export lost property:', error);
        errors.push('lostProperty');
        exportData.data.lostProperty = [];
        exportData.recordCounts.lostProperty = 0;
      }

      // Export feedback
      try {
        const feedback = await this.feedbackManager.exportAllFeedback();
        exportData.data.feedback = feedback;
        exportData.recordCounts.feedback = feedback.length;
        console.log(`[EXPORT] Exported ${feedback.length} feedback items`);
      } catch (error) {
        console.error('[EXPORT] Failed to export feedback:', error);
        errors.push('feedback');
        exportData.data.feedback = [];
        exportData.recordCounts.feedback = 0;
      }

      // Export shifts
      try {
        const shifts = await this.shiftManager.exportAllShifts();
        exportData.data.shifts = shifts;
        exportData.recordCounts.shifts = {
          configs: shifts.configs.length,
          teamLeaders: shifts.teamLeaders.length,
          assignments: shifts.assignments.length
        };
        console.log(`[EXPORT] Exported ${shifts.configs.length} shift configs, ${shifts.teamLeaders.length} team leaders, ${shifts.assignments.length} assignments`);
      } catch (error) {
        console.error('[EXPORT] Failed to export shifts:', error);
        errors.push('shifts');
        exportData.data.shifts = { configs: [], teamLeaders: [], assignments: [] };
        exportData.recordCounts.shifts = { configs: 0, teamLeaders: 0, assignments: 0 };
      }

      // Export knowledge base
      try {
        const knowledgeBase = await this.knowledgeBaseManager.exportAllKnowledgeBase();
        exportData.data.knowledgeBase = knowledgeBase;
        exportData.recordCounts.knowledgeBase = knowledgeBase.length;
        console.log(`[EXPORT] Exported ${knowledgeBase.length} knowledge base items`);
      } catch (error) {
        console.error('[EXPORT] Failed to export knowledge base:', error);
        errors.push('knowledgeBase');
        exportData.data.knowledgeBase = [];
        exportData.recordCounts.knowledgeBase = 0;
      }

      // Export sensory hub visits
      try {
        const sensoryHubVisits = await this.sensoryHubManager.exportAllVisits();
        exportData.data.sensoryHubVisits = sensoryHubVisits;
        exportData.recordCounts.sensoryHubVisits = sensoryHubVisits.length;
        console.log(`[EXPORT] Exported ${sensoryHubVisits.length} sensory hub visits`);
      } catch (error) {
        console.error('[EXPORT] Failed to export sensory hub visits:', error);
        errors.push('sensoryHubVisits');
        exportData.data.sensoryHubVisits = [];
        exportData.recordCounts.sensoryHubVisits = 0;
      }

      // Export access control
      try {
        const accessControl = await accessManager.exportAllAccessControl();
        exportData.data.accessControl = accessControl;
        exportData.recordCounts.accessControl = {
          pageAccess: accessControl.pageAccess.length,
          userRoles: accessControl.userRoles.length
        };
        console.log(`[EXPORT] Exported ${accessControl.pageAccess.length} page access configs, ${accessControl.userRoles.length} user roles`);
      } catch (error) {
        console.error('[EXPORT] Failed to export access control:', error);
        errors.push('accessControl');
        exportData.data.accessControl = { pageAccess: [], userRoles: [] };
        exportData.recordCounts.accessControl = { pageAccess: 0, userRoles: 0 };
      }

      // Add error information to metadata
      if (errors.length > 0) {
        exportData.metadata.partialExport = true;
        exportData.metadata.failedDataTypes = errors;
        console.warn(`[EXPORT] Partial export completed. Failed data types: ${errors.join(', ')}`);
      } else {
        exportData.metadata.partialExport = false;
        console.log('[EXPORT] Complete export successful');
      }

      // Generate export based on format
      if (format === 'json') {
        const jsonString = JSON.stringify(exportData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        console.log(`[EXPORT] JSON export completed. Size: ${(blob.size / 1024).toFixed(2)} KB`);
        return blob;
      } else if (format === 'csv') {
        // CSV export would require JSZip for multiple files
        // For now, return JSON with a note
        exportData.metadata.note = 'CSV export requires JSZip library. Returning JSON format instead.';
        const jsonString = JSON.stringify(exportData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        console.warn('[EXPORT] CSV export not implemented (requires JSZip). Returning JSON format.');
        return blob;
      } else {
        throw new Error(`Unsupported export format: ${format}`);
      }

    } catch (error) {
      console.error('[EXPORT] Database export failed:', error);
      throw error;
    }
  }

  // Database Cleanup Methods for Cache Persistence Issues
  
  /**
   * Completely destroys the local PouchDB database
   * This removes all local data and forces a fresh start
   */
  async clearLocalDatabase(): Promise<void> {
    console.log('[DATABASE RESET] Starting local database cleanup...');
    
    try {
      // Stop any active sync operations first
      if (this.syncManager) {
        console.log('[DATABASE RESET] Stopping sync operations...');
        this.syncManager.cleanup();
      }

      // Remove all event listeners to prevent memory leaks
      if (this.db) {
        console.log('[DATABASE RESET] Removing database event listeners...');
        this.db.removeAllListeners();
      }

      // Destroy the local database completely
      if (this.db) {
        console.log('[DATABASE RESET] Destroying local PouchDB instance...');
        await this.db.destroy();
        console.log('[DATABASE RESET] Local database destroyed successfully');
      }

      // Reset initialization state
      this.isInitialized = false;
      this.initializationPromise = null;

      console.log('[DATABASE RESET] Local database cleanup completed');
    } catch (error) {
      console.error('[DATABASE RESET] Error during local database cleanup:', error);
      throw new Error(`Failed to clear local database: ${error}`);
    }
  }

  /**
   * Reinitializes the database after clearing
   * Creates a new PouchDB instance and all managers
   */
  async reinitializeDatabase(): Promise<void> {
    console.log('[DATABASE RESET] Starting database reinitialization...');
    
    try {
      // Ensure we're starting fresh
      if (this.isInitialized) {
        throw new Error('Database is already initialized. Call clearLocalDatabase() first.');
      }

      // Initialize configuration
      await initializeConfig();
      const config = getConfig();
      
      console.log('[DATABASE RESET] Creating new PouchDB instance...');
      this.db = new PouchDB(config.localName);
      
      // Create new sync manager
      console.log('[DATABASE RESET] Creating new SyncManager...');
      this.syncManager = new SyncManager(this.db);
      
      // Initialize all managers with new database instance
      console.log('[DATABASE RESET] Initializing all data managers...');
      this.admissionManager = new AdmissionManager(this.db, this.syncManager);
      this.festivalManager = new FestivalManager(this.db, this.syncManager);
      this.itemManager = new ItemManager(this.db, this.syncManager);
      this.lostPropertyManager = new LostPropertyManager(this.db, this.syncManager);
      this.shiftManager = new ShiftManager(this.db, this.syncManager);
      this.feedbackManager = new FeedbackManager(this.db, this.syncManager);
      this.bayManager = new BayManager(this.db, this.syncManager);
      this.knowledgeBaseManager = new KnowledgeBaseManager(this.db, this.syncManager);
      
      this.isInitialized = true;
      console.log('[DATABASE RESET] Database reinitialization completed successfully');
      
    } catch (error) {
      console.error('[DATABASE RESET] Error during database reinitialization:', error);
      // Reset state on failure
      this.isInitialized = false;
      this.initializationPromise = null;
      throw new Error(`Failed to reinitialize database: ${error}`);
    }
  }

  /**
   * Performs a complete database reset with fresh sync from remote
   * This is the main method to resolve cache persistence issues
   */
  async resetDatabaseWithFreshSync(): Promise<void> {
    console.log('[DATABASE RESET] Starting complete database reset with fresh sync...');
    
    try {
      // Step 1: Clear the local database
      console.log('[DATABASE RESET] Step 1: Clearing local database...');
      await this.clearLocalDatabase();
      
      // Step 2: Reinitialize the database
      console.log('[DATABASE RESET] Step 2: Reinitializing database...');
      await this.reinitializeDatabase();
      
      // Step 3: Trigger a fresh sync from remote
      console.log('[DATABASE RESET] Step 3: Starting fresh sync from remote...');
      if (this.syncManager) {
        try {
          await this.syncManager.handlePostResetSync();
          console.log('[DATABASE RESET] Fresh sync completed successfully');
        } catch (syncError) {
          console.warn('[DATABASE RESET] Fresh sync failed (non-blocking):', syncError);
          // Don't throw here - the database is still functional for offline use
        }
      }
      
      console.log('[DATABASE RESET] Complete database reset finished successfully');
      
    } catch (error) {
      console.error('[DATABASE RESET] Error during complete database reset:', error);
      throw new Error(`Failed to reset database with fresh sync: ${error}`);
    }
  }
}

// Export the simplified database service directly
export const databaseService = new SimplifiedDatabaseService();

// Export sync manager for compatibility
export let syncManager: SyncManager | null = null;

// Set sync manager reference when available
setTimeout(() => {
  syncManager = databaseService.getSyncManager();
}, 1000);
