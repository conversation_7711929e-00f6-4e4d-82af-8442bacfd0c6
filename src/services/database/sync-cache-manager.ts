import PouchDB from 'pouchdb-browser';

/**
 * Cache partition for festival-specific data
 */
interface CachePartition {
  festivalId: string;
  documentIds: string[];
  lastUpdated: Date;
  documentCount: number;
  sizeEstimate: number; // Estimated size in bytes
}

/**
 * Cache statistics for monitoring
 */
interface CacheStats {
  totalPartitions: number;
  totalDocuments: number;
  totalSizeEstimate: number;
  hitRate: number;
  missRate: number;
  lastCleanup: Date;
  storageQuotaUsage: number;
}

/**
 * Cache invalidation rule
 */
interface CacheInvalidationRule {
  documentType: string;
  maxAge: number; // Maximum age in milliseconds
  triggerEvents: string[]; // Events that trigger invalidation
}

/**
 * SyncCacheManager - Implements intelligent caching strategy for selective sync
 * 
 * Features:
 * - Festival-specific cache partitions
 * - Automatic cache invalidation based on data changes
 * - Storage quota monitoring and optimization
 * - Cache optimization algorithms
 */
export class SyncCacheManager {
  private db: PouchDB.Database;
  private activeFestivalId?: string;
  private cachePartitions: Map<string, CachePartition> = new Map();
  private cacheStats: CacheStats;
  private invalidationRules: Map<string, CacheInvalidationRule> = new Map();
  
  // Performance tracking
  private cacheHits: number = 0;
  private cacheMisses: number = 0;
  private lastCleanup: Date = new Date();
  
  // Storage quota management
  private maxCacheSize: number = 50 * 1024 * 1024; // 50MB default
  private storageQuotaThreshold: number = 0.8; // 80% of quota
  private cleanupInterval: number = 600000; // 10 minutes

  constructor(db: PouchDB.Database, activeFestivalId?: string) {
    this.db = db;
    this.activeFestivalId = activeFestivalId;
    
    this.cacheStats = {
      totalPartitions: 0,
      totalDocuments: 0,
      totalSizeEstimate: 0,
      hitRate: 0,
      missRate: 0,
      lastCleanup: new Date(),
      storageQuotaUsage: 0
    };

    this.initializeInvalidationRules();
    console.log('[SYNC CACHE] SyncCacheManager initialized for festival:', activeFestivalId);
  }

  /**
   * Initialize cache invalidation rules
   */
  private initializeInvalidationRules(): void {
    // Admission data - invalidate quickly due to frequent updates
    this.invalidationRules.set('admission', {
      documentType: 'admission',
      maxAge: 300000, // 5 minutes
      triggerEvents: ['admission_created', 'admission_updated', 'admission_deleted']
    });

    // Festival data - longer cache time, stable data
    this.invalidationRules.set('festival', {
      documentType: 'festival',
      maxAge: 3600000, // 1 hour
      triggerEvents: ['festival_updated', 'festival_deleted']
    });

    // Item counts - medium cache time
    this.invalidationRules.set('item', {
      documentType: 'item',
      maxAge: 900000, // 15 minutes
      triggerEvents: ['item_updated', 'item_deleted']
    });

    // Feedback - longer cache time, less frequently accessed
    this.invalidationRules.set('feedback', {
      documentType: 'feedback',
      maxAge: 1800000, // 30 minutes
      triggerEvents: ['feedback_created', 'feedback_updated']
    });

    // Knowledge base - very long cache time, rarely changes
    this.invalidationRules.set('knowledgeBase', {
      documentType: 'knowledgeBase',
      maxAge: 7200000, // 2 hours
      triggerEvents: ['knowledge_updated', 'knowledge_deleted']
    });

    console.log('[SYNC CACHE] Initialized cache invalidation rules for', this.invalidationRules.size, 'document types');
  }

  /**
   * Initialize the cache manager
   */
  public async initialize(): Promise<void> {
    try {
      console.log('[SYNC CACHE] Initializing cache manager...');
      
      // Check storage quota
      await this.checkStorageQuota();
      
      // Load existing cache partitions from local storage if available
      await this.loadCachePartitions();
      
      // Schedule periodic cleanup
      this.schedulePeriodicCleanup();
      
      console.log('[SYNC CACHE] Cache manager initialized successfully');
    } catch (error) {
      console.error('[SYNC CACHE] Failed to initialize cache manager:', error);
    }
  }

  /**
   * Updates the active festival scope
   */
  public updateFestivalScope(festivalId: string): void {
    console.log('[SYNC CACHE] Updating festival scope to:', festivalId);
    this.activeFestivalId = festivalId;
    
    // Prioritize the new festival's cache partition
    this.prioritizeFestivalPartition(festivalId);
  }

  /**
   * Gets cached documents for the current festival scope
   */
  public async getCachedDocuments(): Promise<string[]> {
    if (!this.activeFestivalId) {
      console.log('[SYNC CACHE] No active festival ID, cache miss');
      this.cacheMisses++;
      return [];
    }

    const partition = this.cachePartitions.get(this.activeFestivalId);
    
    if (!partition) {
      console.log('[SYNC CACHE] No cache partition for festival:', this.activeFestivalId);
      this.cacheMisses++;
      return [];
    }

    // Check if cache is still valid
    const isValid = await this.isCachePartitionValid(partition);
    
    if (!isValid) {
      console.log('[SYNC CACHE] Cache partition expired for festival:', this.activeFestivalId);
      this.cachePartitions.delete(this.activeFestivalId);
      this.cacheMisses++;
      return [];
    }

    console.log(`[SYNC CACHE] Cache hit for festival ${this.activeFestivalId}: ${partition.documentIds.length} documents`);
    this.cacheHits++;
    this.updateCacheStats();
    
    return partition.documentIds;
  }

  /**
   * Updates cache with new document list
   */
  public async updateCache(documentIds: string[]): Promise<void> {
    if (!this.activeFestivalId) {
      console.warn('[SYNC CACHE] Cannot update cache without active festival ID');
      return;
    }

    try {
      const sizeEstimate = await this.estimateDocumentSize(documentIds);
      
      // Check if we need to free up space
      if (this.cacheStats.totalSizeEstimate + sizeEstimate > this.maxCacheSize) {
        await this.performCacheOptimization();
      }

      const partition: CachePartition = {
        festivalId: this.activeFestivalId,
        documentIds: [...documentIds], // Create a copy
        lastUpdated: new Date(),
        documentCount: documentIds.length,
        sizeEstimate
      };

      this.cachePartitions.set(this.activeFestivalId, partition);
      this.updateCacheStats();
      
      // Persist cache partitions
      await this.persistCachePartitions();
      
      console.log(`[SYNC CACHE] Updated cache for festival ${this.activeFestivalId}: ${documentIds.length} documents (${(sizeEstimate / 1024).toFixed(1)}KB)`);

    } catch (error) {
      console.error('[SYNC CACHE] Failed to update cache:', error);
    }
  }

  /**
   * Invalidates cache based on document changes
   */
  public async invalidateCache(documentId: string, documentType: string, eventType: string): Promise<void> {
    console.log(`[SYNC CACHE] Invalidating cache for ${documentType} document: ${documentId} (event: ${eventType})`);

    const rule = this.invalidationRules.get(documentType);
    
    if (!rule) {
      console.log(`[SYNC CACHE] No invalidation rule for document type: ${documentType}`);
      return;
    }

    // Check if this event triggers invalidation
    if (!rule.triggerEvents.includes(eventType)) {
      console.log(`[SYNC CACHE] Event ${eventType} does not trigger invalidation for ${documentType}`);
      return;
    }

    // Find and invalidate affected partitions
    const affectedPartitions: string[] = [];
    
    this.cachePartitions.forEach((partition, festivalId) => {
      if (partition.documentIds.includes(documentId)) {
        affectedPartitions.push(festivalId);
      }
    });

    // Remove affected partitions
    for (const festivalId of affectedPartitions) {
      this.cachePartitions.delete(festivalId);
      console.log(`[SYNC CACHE] Invalidated cache partition for festival: ${festivalId}`);
    }

    this.updateCacheStats();
    await this.persistCachePartitions();
  }

  /**
   * Checks if a cache partition is still valid
   */
  private async isCachePartitionValid(partition: CachePartition): Promise<boolean> {
    const now = Date.now();
    const partitionAge = now - partition.lastUpdated.getTime();
    
    // Check age-based invalidation for different document types
    let isExpired = false;
    this.invalidationRules.forEach((rule, documentType) => {
      if (partitionAge > rule.maxAge) {
        console.log(`[SYNC CACHE] Partition expired due to ${documentType} rule: ${partitionAge}ms > ${rule.maxAge}ms`);
        isExpired = true;
      }
    });
    
    if (isExpired) {
      return false;
    }

    // Check if documents still exist and haven't been modified
    try {
      const sampleSize = Math.min(5, partition.documentIds.length);
      const sampleIds = partition.documentIds.slice(0, sampleSize);
      
      for (const docId of sampleIds) {
        try {
          const doc = await this.db.get(docId);
          const docUpdated = new Date((doc as any).updatedAt || (doc as any).timestamp || (doc as any).createdAt || 0);
          
          if (docUpdated > partition.lastUpdated) {
            console.log(`[SYNC CACHE] Partition invalid due to document update: ${docId}`);
            return false;
          }
        } catch (error: any) {
          if (error.status === 404) {
            console.log(`[SYNC CACHE] Partition invalid due to deleted document: ${docId}`);
            return false;
          }
        }
      }
      
      return true;
      
    } catch (error) {
      console.error('[SYNC CACHE] Error validating cache partition:', error);
      return false;
    }
  }

  /**
   * Estimates the size of documents for storage quota management
   */
  private async estimateDocumentSize(documentIds: string[]): Promise<number> {
    try {
      // Sample a few documents to estimate average size
      const sampleSize = Math.min(10, documentIds.length);
      const sampleIds = documentIds.slice(0, sampleSize);
      
      let totalSampleSize = 0;
      let validSamples = 0;
      
      for (const docId of sampleIds) {
        try {
          const doc = await this.db.get(docId);
          const docSize = JSON.stringify(doc).length * 2; // Rough estimate (UTF-16)
          totalSampleSize += docSize;
          validSamples++;
        } catch (error) {
          // Skip documents that can't be retrieved
        }
      }
      
      if (validSamples === 0) {
        return 0;
      }
      
      const averageSize = totalSampleSize / validSamples;
      const estimatedTotalSize = averageSize * documentIds.length;
      
      console.log(`[SYNC CACHE] Estimated size for ${documentIds.length} documents: ${(estimatedTotalSize / 1024).toFixed(1)}KB`);
      return estimatedTotalSize;
      
    } catch (error) {
      console.error('[SYNC CACHE] Failed to estimate document size:', error);
      return documentIds.length * 1024; // Fallback: 1KB per document
    }
  }

  /**
   * Performs cache optimization when storage quota is exceeded
   */
  private async performCacheOptimization(): Promise<void> {
    console.log('[SYNC CACHE] Performing cache optimization...');

    // Sort partitions by priority (most recent first, current festival prioritized)
    const partitionEntries: Array<[string, CachePartition]> = [];
    this.cachePartitions.forEach((partition, festivalId) => {
      partitionEntries.push([festivalId, partition]);
    });
    
    const sortedPartitions = partitionEntries
      .sort(([festivalIdA, partitionA], [festivalIdB, partitionB]) => {
        // Prioritize active festival
        if (festivalIdA === this.activeFestivalId) return -1;
        if (festivalIdB === this.activeFestivalId) return 1;
        
        // Then by last updated time
        return partitionB.lastUpdated.getTime() - partitionA.lastUpdated.getTime();
      });

    // Remove oldest partitions until we're under the size limit
    const targetSize = this.maxCacheSize * 0.7; // Target 70% of max size
    let currentSize = this.cacheStats.totalSizeEstimate;
    let removedCount = 0;

    for (let i = sortedPartitions.length - 1; i >= 0 && currentSize > targetSize; i--) {
      const [festivalId, partition] = sortedPartitions[i];
      
      // Don't remove the active festival's partition
      if (festivalId === this.activeFestivalId) {
        continue;
      }
      
      this.cachePartitions.delete(festivalId);
      currentSize -= partition.sizeEstimate;
      removedCount++;
      
      console.log(`[SYNC CACHE] Removed cache partition for festival: ${festivalId} (${(partition.sizeEstimate / 1024).toFixed(1)}KB)`);
    }

    this.updateCacheStats();
    await this.persistCachePartitions();
    
    console.log(`[SYNC CACHE] Cache optimization completed: removed ${removedCount} partitions, size reduced to ${(currentSize / 1024).toFixed(1)}KB`);
  }

  /**
   * Prioritizes a festival's cache partition
   */
  private prioritizeFestivalPartition(festivalId: string): void {
    const partition = this.cachePartitions.get(festivalId);
    
    if (partition) {
      // Update the last accessed time to keep it in cache longer
      partition.lastUpdated = new Date();
      console.log(`[SYNC CACHE] Prioritized cache partition for festival: ${festivalId}`);
    }
  }

  /**
   * Checks storage quota and adjusts cache size limits
   */
  private async checkStorageQuota(): Promise<void> {
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate();
        const usage = estimate.usage || 0;
        const quota = estimate.quota || 0;
        
        if (quota > 0) {
          const usageRatio = usage / quota;
          this.cacheStats.storageQuotaUsage = usageRatio;
          
          // Adjust cache size based on available storage
          if (usageRatio > this.storageQuotaThreshold) {
            this.maxCacheSize = Math.max(10 * 1024 * 1024, this.maxCacheSize * 0.5); // Reduce to 50% or min 10MB
            console.warn(`[SYNC CACHE] Storage quota pressure detected (${(usageRatio * 100).toFixed(1)}%), reducing cache size to ${(this.maxCacheSize / 1024 / 1024).toFixed(1)}MB`);
          }
          
          console.log(`[SYNC CACHE] Storage quota: ${(usage / 1024 / 1024).toFixed(1)}MB / ${(quota / 1024 / 1024).toFixed(1)}MB (${(usageRatio * 100).toFixed(1)}%)`);
        }
      }
    } catch (error) {
      console.warn('[SYNC CACHE] Could not check storage quota:', error);
    }
  }

  /**
   * Updates cache statistics
   */
  private updateCacheStats(): void {
    this.cacheStats.totalPartitions = this.cachePartitions.size;
    this.cacheStats.totalDocuments = Array.from(this.cachePartitions.values())
      .reduce((sum, partition) => sum + partition.documentCount, 0);
    this.cacheStats.totalSizeEstimate = Array.from(this.cachePartitions.values())
      .reduce((sum, partition) => sum + partition.sizeEstimate, 0);
    
    const totalRequests = this.cacheHits + this.cacheMisses;
    this.cacheStats.hitRate = totalRequests > 0 ? this.cacheHits / totalRequests : 0;
    this.cacheStats.missRate = totalRequests > 0 ? this.cacheMisses / totalRequests : 0;
  }

  /**
   * Loads cache partitions from local storage
   */
  private async loadCachePartitions(): Promise<void> {
    try {
      const cacheKey = 'ithink_welfare_sync_cache';
      const cached = localStorage.getItem(cacheKey);
      
      if (cached) {
        const data = JSON.parse(cached);
        
        for (const partitionData of data.partitions || []) {
          const partition: CachePartition = {
            ...partitionData,
            lastUpdated: new Date(partitionData.lastUpdated)
          };
          
          // Validate partition before loading
          if (await this.isCachePartitionValid(partition)) {
            this.cachePartitions.set(partition.festivalId, partition);
          }
        }
        
        this.updateCacheStats();
        console.log(`[SYNC CACHE] Loaded ${this.cachePartitions.size} cache partitions from storage`);
      }
    } catch (error) {
      console.error('[SYNC CACHE] Failed to load cache partitions:', error);
    }
  }

  /**
   * Persists cache partitions to local storage
   */
  private async persistCachePartitions(): Promise<void> {
    try {
      const cacheKey = 'ithink_welfare_sync_cache';
      const data = {
        partitions: Array.from(this.cachePartitions.values()),
        lastUpdated: new Date().toISOString()
      };
      
      localStorage.setItem(cacheKey, JSON.stringify(data));
    } catch (error) {
      console.error('[SYNC CACHE] Failed to persist cache partitions:', error);
    }
  }

  /**
   * Schedules periodic cache cleanup
   */
  private schedulePeriodicCleanup(): void {
    setInterval(async () => {
      try {
        await this.performPeriodicCleanup();
      } catch (error) {
        console.error('[SYNC CACHE] Periodic cleanup failed:', error);
      }
    }, this.cleanupInterval);

    console.log(`[SYNC CACHE] Scheduled periodic cleanup every ${this.cleanupInterval / 1000}s`);
  }

  /**
   * Performs periodic cleanup of expired cache partitions
   */
  private async performPeriodicCleanup(): Promise<void> {
    console.log('[SYNC CACHE] Performing periodic cleanup...');
    
    const expiredPartitions: string[] = [];
    
    // Check each partition for expiration
    const partitionChecks: Promise<{ festivalId: string; isValid: boolean }>[] = [];
    this.cachePartitions.forEach((partition, festivalId) => {
      partitionChecks.push(
        this.isCachePartitionValid(partition).then(isValid => ({ festivalId, isValid }))
      );
    });
    
    const results = await Promise.all(partitionChecks);
    for (const result of results) {
      if (!result.isValid) {
        expiredPartitions.push(result.festivalId);
      }
    }
    
    for (const festivalId of expiredPartitions) {
      this.cachePartitions.delete(festivalId);
    }
    
    if (expiredPartitions.length > 0) {
      this.updateCacheStats();
      await this.persistCachePartitions();
      console.log(`[SYNC CACHE] Cleaned up ${expiredPartitions.length} expired cache partitions`);
    }
    
    // Check storage quota
    await this.checkStorageQuota();
    
    this.lastCleanup = new Date();
    this.cacheStats.lastCleanup = this.lastCleanup;
  }

  /**
   * Gets cache hit rate for performance monitoring
   */
  public getCacheHitRate(): number {
    return this.cacheStats.hitRate;
  }

  /**
   * Gets cache statistics for monitoring
   */
  public getCacheStats(): CacheStats {
    this.updateCacheStats();
    return { ...this.cacheStats };
  }

  /**
   * Clears all cache partitions
   */
  public async clearCache(): Promise<void> {
    console.log('[SYNC CACHE] Clearing all cache partitions...');
    
    this.cachePartitions.clear();
    this.cacheHits = 0;
    this.cacheMisses = 0;
    this.updateCacheStats();
    
    try {
      const cacheKey = 'ithink_welfare_sync_cache';
      localStorage.removeItem(cacheKey);
    } catch (error) {
      console.error('[SYNC CACHE] Failed to clear cache from storage:', error);
    }
    
    console.log('[SYNC CACHE] Cache cleared successfully');
  }

  /**
   * Cleanup method
   */
  public cleanup(): void {
    this.cachePartitions.clear();
    this.invalidationRules.clear();
    console.log('[SYNC CACHE] SyncCacheManager cleaned up');
  }
}