/**
 * Persistent Initialization Manager
 * 
 * Manages persistent initialization state using localStorage to avoid redundant syncs
 * on every page load or refresh. Tracks sync status, data versions, and timing.
 */

export interface InitializationState {
  isInitialized: boolean;
  lastSyncTime: number;
  lastSuccessfulSync: number;
  dataVersion: string;
  syncCount: number;
  hasLocalData: boolean;
  festivalId?: string;
  userAgent: string;
  sessionId: string;
}

export interface SyncConfig {
  // How long to consider initialization valid (in milliseconds)
  initializationTTL: number;
  // Minimum time between automatic syncs (in milliseconds)
  minSyncInterval: number;
  // Force sync after this many page loads
  maxPageLoadsBeforeSync: number;
  // Force sync if data is older than this (in milliseconds)
  maxDataAge: number;
}

export class PersistentInitializationManager {
  private static readonly STORAGE_KEY = 'ithink_welfare_initialization_state';
  private static readonly CONFIG_KEY = 'ithink_welfare_sync_config';
  private static readonly SESSION_KEY = 'ithink_welfare_session_id';
  
  private currentState: InitializationState;
  private config: SyncConfig;
  private sessionId: string;
  
  // Default configuration
  private static readonly DEFAULT_CONFIG: SyncConfig = {
    initializationTTL: 30 * 60 * 1000, // 30 minutes
    minSyncInterval: 5 * 60 * 1000,    // 5 minutes
    maxPageLoadsBeforeSync: 10,        // Force sync after 10 page loads
    maxDataAge: 2 * 60 * 60 * 1000     // 2 hours
  };

  constructor(festivalId?: string) {
    this.sessionId = this.getOrCreateSessionId();
    this.config = this.loadConfig();
    this.currentState = this.loadState(festivalId);
    
    console.log('[PERSISTENT INIT] Initialized with state:', {
      isInitialized: this.currentState.isInitialized,
      lastSyncTime: new Date(this.currentState.lastSyncTime).toISOString(),
      sessionId: this.sessionId,
      festivalId
    });
  }

  /**
   * Check if initialization is still valid and sync is not needed
   */
  public shouldSkipSync(): boolean {
    const now = Date.now();
    const timeSinceLastSync = now - this.currentState.lastSyncTime;
    const timeSinceSuccessfulSync = now - this.currentState.lastSuccessfulSync;
    
    // Always sync if never initialized
    if (!this.currentState.isInitialized) {
      console.log('[PERSISTENT INIT] First time initialization required');
      return false;
    }

    // Always sync if no local data
    if (!this.currentState.hasLocalData) {
      console.log('[PERSISTENT INIT] No local data, sync required');
      return false;
    }

    // Force sync if initialization TTL expired
    if (timeSinceLastSync > this.config.initializationTTL) {
      console.log('[PERSISTENT INIT] Initialization TTL expired, sync required');
      return false;
    }

    // Force sync if minimum interval hasn't been respected
    if (timeSinceLastSync < this.config.minSyncInterval) {
      console.log('[PERSISTENT INIT] Within minimum sync interval, skipping sync');
      return true;
    }

    // Force sync if data is too old
    if (timeSinceSuccessfulSync > this.config.maxDataAge) {
      console.log('[PERSISTENT INIT] Data too old, sync required');
      return false;
    }

    // Force sync after too many page loads
    if (this.currentState.syncCount >= this.config.maxPageLoadsBeforeSync) {
      console.log('[PERSISTENT INIT] Max page loads reached, sync required');
      return false;
    }

    console.log('[PERSISTENT INIT] Sync not needed, using cached initialization');
    return true;
  }

  /**
   * Mark initialization as started
   */
  public markSyncStarted(): void {
    this.currentState.lastSyncTime = Date.now();
    this.currentState.syncCount += 1;
    this.saveState();
    
    console.log('[PERSISTENT INIT] Sync started, count:', this.currentState.syncCount);
  }

  /**
   * Mark initialization as completed successfully
   */
  public markSyncCompleted(hasData: boolean, dataVersion?: string): void {
    const now = Date.now();
    this.currentState.isInitialized = true;
    this.currentState.lastSuccessfulSync = now;
    this.currentState.hasLocalData = hasData;
    this.currentState.syncCount = 0; // Reset counter after successful sync
    
    if (dataVersion) {
      this.currentState.dataVersion = dataVersion;
    }
    
    this.saveState();
    
    console.log('[PERSISTENT INIT] Sync completed successfully:', {
      hasData,
      dataVersion,
      timestamp: new Date(now).toISOString()
    });
  }

  /**
   * Mark initialization as failed
   */
  public markSyncFailed(): void {
    // Don't mark as initialized, but update sync time to prevent immediate retry
    this.currentState.lastSyncTime = Date.now();
    this.saveState();
    
    console.log('[PERSISTENT INIT] Sync failed');
  }

  /**
   * Force a sync on next initialization check
   */
  public invalidateCache(): void {
    this.currentState.isInitialized = false;
    this.currentState.lastSyncTime = 0;
    this.currentState.lastSuccessfulSync = 0;
    this.currentState.syncCount = 0;
    this.saveState();
    
    console.log('[PERSISTENT INIT] Cache invalidated, next sync will be forced');
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<SyncConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.saveConfig();
    
    console.log('[PERSISTENT INIT] Configuration updated:', newConfig);
  }

  /**
   * Get current initialization state (read-only)
   */
  public getState(): Readonly<InitializationState> {
    return { ...this.currentState };
  }

  /**
   * Get current configuration (read-only)
   */
  public getConfig(): Readonly<SyncConfig> {
    return { ...this.config };
  }

  /**
   * Reset all persistent state (useful for debugging or major updates)
   */
  public reset(): void {
    localStorage.removeItem(PersistentInitializationManager.STORAGE_KEY);
    localStorage.removeItem(PersistentInitializationManager.CONFIG_KEY);
    localStorage.removeItem(PersistentInitializationManager.SESSION_KEY);
    
    this.sessionId = this.getOrCreateSessionId();
    this.config = PersistentInitializationManager.DEFAULT_CONFIG;
    this.currentState = this.createDefaultState();
    
    console.log('[PERSISTENT INIT] All state reset');
  }

  private getOrCreateSessionId(): string {
    let sessionId = sessionStorage.getItem(PersistentInitializationManager.SESSION_KEY);
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem(PersistentInitializationManager.SESSION_KEY, sessionId);
    }
    return sessionId;
  }

  private loadState(festivalId?: string): InitializationState {
    try {
      const stored = localStorage.getItem(PersistentInitializationManager.STORAGE_KEY);
      if (stored) {
        const state = JSON.parse(stored) as InitializationState;
        
        // Validate state structure and update if needed
        if (this.isValidState(state)) {
          // Update session and user agent info
          state.sessionId = this.sessionId;
          state.userAgent = navigator.userAgent;
          
          // If festival ID changed, invalidate cache
          if (festivalId && state.festivalId !== festivalId) {
            console.log('[PERSISTENT INIT] Festival ID changed, invalidating cache');
            state.isInitialized = false;
            state.festivalId = festivalId;
          }
          
          return state;
        }
      }
    } catch (error) {
      console.warn('[PERSISTENT INIT] Failed to load state from localStorage:', error);
    }
    
    return this.createDefaultState(festivalId);
  }

  private createDefaultState(festivalId?: string): InitializationState {
    return {
      isInitialized: false,
      lastSyncTime: 0,
      lastSuccessfulSync: 0,
      dataVersion: '',
      syncCount: 0,
      hasLocalData: false,
      festivalId,
      userAgent: navigator.userAgent,
      sessionId: this.sessionId
    };
  }

  private isValidState(state: any): state is InitializationState {
    return (
      typeof state === 'object' &&
      typeof state.isInitialized === 'boolean' &&
      typeof state.lastSyncTime === 'number' &&
      typeof state.lastSuccessfulSync === 'number' &&
      typeof state.dataVersion === 'string' &&
      typeof state.syncCount === 'number' &&
      typeof state.hasLocalData === 'boolean'
    );
  }

  private saveState(): void {
    try {
      localStorage.setItem(
        PersistentInitializationManager.STORAGE_KEY,
        JSON.stringify(this.currentState)
      );
    } catch (error) {
      console.error('[PERSISTENT INIT] Failed to save state to localStorage:', error);
    }
  }

  private loadConfig(): SyncConfig {
    try {
      const stored = localStorage.getItem(PersistentInitializationManager.CONFIG_KEY);
      if (stored) {
        const config = JSON.parse(stored) as SyncConfig;
        return { ...PersistentInitializationManager.DEFAULT_CONFIG, ...config };
      }
    } catch (error) {
      console.warn('[PERSISTENT INIT] Failed to load config from localStorage:', error);
    }
    
    return PersistentInitializationManager.DEFAULT_CONFIG;
  }

  private saveConfig(): void {
    try {
      localStorage.setItem(
        PersistentInitializationManager.CONFIG_KEY,
        JSON.stringify(this.config)
      );
    } catch (error) {
      console.error('[PERSISTENT INIT] Failed to save config to localStorage:', error);
    }
  }
}
