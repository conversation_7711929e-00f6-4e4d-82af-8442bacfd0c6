import PouchDB from 'pouchdb-browser';

/**
 * Document type priority levels for selective sync
 */
export enum DocumentTypePriority {
  CRITICAL = 1,    // Festivals, active admissions
  HIGH = 2,        // Recent admissions, shift data
  MEDIUM = 3,      // Items, lost property
  LOW = 4,         // Old feedback, archived data
  MINIMAL = 5      // Historical data, deleted items
}

/**
 * Document type filter configuration
 */
export interface DocumentTypeFilter {
  documentType: string;
  priority: DocumentTypePriority;
  maxAge?: number; // Maximum age in days
  includeDeleted?: boolean;
  customSelector?: any;
}

/**
 * Festival scope filter for primary volume reduction
 */
export interface FestivalScopeFilter {
  activeFestivalId: string;
  includeGlobalData: boolean; // Knowledge base items marked for all festivals
  timeWindow?: {
    startDate: string;
    endDate: string;
  };
}

/**
 * Location-based filter for site-specific data
 */
export interface LocationFilter {
  siteLocationIds: string[];
  includeUnassigned: boolean; // Include documents without location assignment
}

/**
 * Time window filter for performance optimization
 */
export interface TimeWindowFilter {
  startDate: string;
  endDate: string;
  documentTypes?: string[]; // Apply only to specific document types
}

/**
 * Main configuration class for sync filtering
 */
export class SyncFilterConfig {
  private db!: PouchDB.Database; // Using definite assignment assertion
  private activeFestivalId?: string;
  private documentTypeFilters: Map<string, DocumentTypeFilter> = new Map();
  private festivalScopeFilter?: FestivalScopeFilter;
  private locationFilter?: LocationFilter;
  private timeWindowFilter?: TimeWindowFilter;

  constructor(activeFestivalId?: string) {
    this.activeFestivalId = activeFestivalId;
    this.initializeDefaultFilters();
  }

  /**
   * Initialize default document type filters with priorities
   */
  private initializeDefaultFilters(): void {
    // Critical documents - always sync
    this.documentTypeFilters.set('festival', {
      documentType: 'festival',
      priority: DocumentTypePriority.CRITICAL,
      includeDeleted: false
    });

    // High priority - active operational data
    this.documentTypeFilters.set('admission', {
      documentType: 'admission',
      priority: DocumentTypePriority.HIGH,
      maxAge: 30, // Last 30 days
      includeDeleted: false
    });

    this.documentTypeFilters.set('shift_assignment', {
      documentType: 'shift_assignment',
      priority: DocumentTypePriority.HIGH,
      maxAge: 14, // Last 2 weeks
      includeDeleted: false
    });

    this.documentTypeFilters.set('shift_config', {
      documentType: 'shift_config',
      priority: DocumentTypePriority.HIGH,
      includeDeleted: false
    });

    // Medium priority - operational support data
    this.documentTypeFilters.set('item', {
      documentType: 'item',
      priority: DocumentTypePriority.MEDIUM,
      maxAge: 7, // Last week
      includeDeleted: false
    });

    this.documentTypeFilters.set('lost_property', {
      documentType: 'lost_property',
      priority: DocumentTypePriority.MEDIUM,
      maxAge: 30, // Last 30 days
      includeDeleted: false
    });

    this.documentTypeFilters.set('sensory_hub_visit', {
      documentType: 'sensory_hub_visit',
      priority: DocumentTypePriority.MEDIUM,
      maxAge: 14, // Last 2 weeks
      includeDeleted: false
    });

    this.documentTypeFilters.set('knowledgeBase', {
      documentType: 'knowledgeBase',
      priority: DocumentTypePriority.MEDIUM,
      includeDeleted: false
    });

    // Low priority - feedback and notes
    this.documentTypeFilters.set('feedback', {
      documentType: 'feedback',
      priority: DocumentTypePriority.LOW,
      maxAge: 7, // Last week only
      includeDeleted: false
    });

    this.documentTypeFilters.set('festival_notes', {
      documentType: 'festival_notes',
      priority: DocumentTypePriority.LOW,
      includeDeleted: false
    });

    console.log('[SYNC FILTERS] Initialized default document type filters');
  }

  /**
   * Updates the active festival scope
   */
  public updateFestivalScope(festivalId: string): void {
    this.activeFestivalId = festivalId;
    this.festivalScopeFilter = {
      activeFestivalId: festivalId,
      includeGlobalData: true
    };
    console.log('[SYNC FILTERS] Updated festival scope to:', festivalId);
  }

  /**
   * Gets the current active festival ID
   */
  public getActiveFestivalId(): string | undefined {
    return this.activeFestivalId;
  }

  /**
   * Sets the database instance for queries
   */
  public setDatabase(db: PouchDB.Database): void {
    this.db = db;
  }

  /**
   * Applies festival scope filter - PRIMARY OPTIMIZATION (80-90% volume reduction)
   */
  public async applyFestivalScopeFilter(): Promise<string[]> {
    if (!this.db) {
      throw new Error('Database not set for sync filters');
    }

    if (!this.activeFestivalId) {
      console.warn('[SYNC FILTERS] No active festival ID set, returning all documents');
      return await this.getAllDocumentIds();
    }

    try {
      const documentIds: string[] = [];

      // 1. Get festival-specific documents
      const festivalDocs = await this.db.find({
        selector: {
          festivalId: this.activeFestivalId,
          $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
        },
        fields: ['_id']
      });

      documentIds.push(...festivalDocs.docs.map(doc => doc._id));

      // 2. Get the festival document itself
      const festivals = await this.db.find({
        selector: {
          documentType: 'festival',
          _id: { $regex: `^festival_.*` },
          $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
        },
        fields: ['_id']
      });

      documentIds.push(...festivals.docs.map(doc => doc._id));

      // 3. Get global knowledge base items (marked for all festivals)
      const globalKnowledge = await this.db.find({
        selector: {
          documentType: 'knowledgeBase',
          showForAllFestivals: true,
          $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
        },
        fields: ['_id']
      });

      documentIds.push(...globalKnowledge.docs.map(doc => doc._id));

      // 4. Get user roles and access control (global data)
      const accessControl = await this.db.find({
        selector: {
          $or: [
            { documentType: 'user_role' },
            { documentType: 'page_access' }
          ]
        },
        fields: ['_id']
      });

      documentIds.push(...accessControl.docs.map(doc => doc._id));

      // Remove duplicates - ES5 compatible approach
      const uniqueIds = Array.from(new Set(documentIds));
      
      console.log(`[SYNC FILTERS] Festival scope filter: ${uniqueIds.length} documents for festival ${this.activeFestivalId}`);
      return uniqueIds;

    } catch (error) {
      console.error('[SYNC FILTERS] Festival scope filter failed:', error);
      // Fallback to all documents
      return await this.getAllDocumentIds();
    }
  }

  /**
   * Applies document type prioritization filter
   */
  public async applyDocumentTypeFilter(documentIds: string[]): Promise<string[]> {
    if (!this.db || documentIds.length === 0) {
      return documentIds;
    }

    try {
      const prioritizedIds: string[] = [];
      const now = new Date();

      // Process documents by priority level
      for (const priority of [
        DocumentTypePriority.CRITICAL,
        DocumentTypePriority.HIGH,
        DocumentTypePriority.MEDIUM,
        DocumentTypePriority.LOW
      ]) {
        const priorityFilters = Array.from(this.documentTypeFilters.values())
          .filter(filter => filter.priority === priority);

        for (const filter of priorityFilters) {
          const filteredIds = await this.applyDocumentTypeFilterForType(documentIds, filter, now);
          prioritizedIds.push(...filteredIds);
        }
      }

      // Remove duplicates while preserving priority order - ES5 compatible approach
      const uniqueIds = Array.from(new Set(prioritizedIds));
      
      console.log(`[SYNC FILTERS] Document type filter: ${uniqueIds.length} documents (from ${documentIds.length})`);
      return uniqueIds;

    } catch (error) {
      console.error('[SYNC FILTERS] Document type filter failed:', error);
      return documentIds;
    }
  }

  /**
   * Applies document type filter for a specific type
   */
  private async applyDocumentTypeFilterForType(
    documentIds: string[], 
    filter: DocumentTypeFilter, 
    now: Date
  ): Promise<string[]> {
    try {
      const selector: any = {
        _id: { $in: documentIds },
        documentType: filter.documentType
      };

      // Apply age filter if specified
      if (filter.maxAge) {
        const cutoffDate = new Date(now.getTime() - (filter.maxAge * 24 * 60 * 60 * 1000));
        selector.$and = [
          { documentType: filter.documentType },
          {
            $or: [
              { createdAt: { $gte: cutoffDate.toISOString() } },
              { timestamp: { $gte: cutoffDate.toISOString() } },
              { lastUpdated: { $gte: cutoffDate.toISOString() } }
            ]
          }
        ];
        delete selector.documentType; // Remove since it's in $and
      }

      // Apply deletion filter
      if (!filter.includeDeleted) {
        if (selector.$and) {
          selector.$and.push({ $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }] });
        } else {
          selector.$or = [{ isDeleted: { $exists: false } }, { isDeleted: false }];
        }
      }

      // Apply custom selector if provided
      if (filter.customSelector) {
        Object.assign(selector, filter.customSelector);
      }

      const result = await this.db.find({
        selector,
        fields: ['_id']
      });

      return result.docs.map(doc => doc._id);

    } catch (error) {
      console.error(`[SYNC FILTERS] Failed to apply filter for ${filter.documentType}:`, error);
      return [];
    }
  }

  /**
   * Applies location-based filtering
   */
  public async applyLocationFilter(documentIds: string[]): Promise<string[]> {
    if (!this.db || !this.locationFilter || documentIds.length === 0) {
      return documentIds;
    }

    try {
      const selector: any = {
        _id: { $in: documentIds },
        $or: []
      };

      // Include documents with specified location IDs
      if (this.locationFilter.siteLocationIds.length > 0) {
        selector.$or.push({
          siteLocationId: { $in: this.locationFilter.siteLocationIds }
        });
      }

      // Include unassigned documents if configured
      if (this.locationFilter.includeUnassigned) {
        selector.$or.push(
          { siteLocationId: { $exists: false } },
          { siteLocationId: null },
          { siteLocationId: '' }
        );
      }

      // If no location criteria, return all documents
      if (selector.$or.length === 0) {
        return documentIds;
      }

      const result = await this.db.find({
        selector,
        fields: ['_id']
      });

      const filteredIds = result.docs.map(doc => doc._id);
      console.log(`[SYNC FILTERS] Location filter: ${filteredIds.length} documents (from ${documentIds.length})`);
      
      return filteredIds;

    } catch (error) {
      console.error('[SYNC FILTERS] Location filter failed:', error);
      return documentIds;
    }
  }

  /**
   * Applies time window filtering for performance
   */
  public async applyTimeWindowFilter(documentIds: string[]): Promise<string[]> {
    if (!this.db || !this.timeWindowFilter || documentIds.length === 0) {
      return documentIds;
    }

    try {
      const selector: any = {
        _id: { $in: documentIds },
        $or: [
          {
            createdAt: {
              $gte: this.timeWindowFilter.startDate,
              $lte: this.timeWindowFilter.endDate
            }
          },
          {
            timestamp: {
              $gte: this.timeWindowFilter.startDate,
              $lte: this.timeWindowFilter.endDate
            }
          },
          {
            lastUpdated: {
              $gte: this.timeWindowFilter.startDate,
              $lte: this.timeWindowFilter.endDate
            }
          }
        ]
      };

      // Apply to specific document types if specified
      if (this.timeWindowFilter.documentTypes && this.timeWindowFilter.documentTypes.length > 0) {
        selector.documentType = { $in: this.timeWindowFilter.documentTypes };
      }

      const result = await this.db.find({
        selector,
        fields: ['_id']
      });

      const filteredIds = result.docs.map(doc => doc._id);
      console.log(`[SYNC FILTERS] Time window filter: ${filteredIds.length} documents (from ${documentIds.length})`);
      
      return filteredIds;

    } catch (error) {
      console.error('[SYNC FILTERS] Time window filter failed:', error);
      return documentIds;
    }
  }

  /**
   * Sets location filter configuration
   */
  public setLocationFilter(filter: LocationFilter): void {
    this.locationFilter = filter;
    console.log('[SYNC FILTERS] Location filter set:', filter);
  }

  /**
   * Sets time window filter configuration
   */
  public setTimeWindowFilter(filter: TimeWindowFilter): void {
    this.timeWindowFilter = filter;
    console.log('[SYNC FILTERS] Time window filter set:', filter);
  }

  /**
   * Gets all document IDs (fallback method)
   */
  private async getAllDocumentIds(): Promise<string[]> {
    try {
      const result = await this.db.allDocs({ include_docs: false });
      return result.rows.map(row => row.id);
    } catch (error) {
      console.error('[SYNC FILTERS] Failed to get all document IDs:', error);
      return [];
    }
  }

  /**
   * Gets filter configuration summary for debugging
   */
  public getFilterSummary(): any {
    return {
      activeFestivalId: this.activeFestivalId,
      documentTypeFilters: Array.from(this.documentTypeFilters.entries()),
      festivalScopeFilter: this.festivalScopeFilter,
      locationFilter: this.locationFilter,
      timeWindowFilter: this.timeWindowFilter
    };
  }

  /**
   * Estimates volume reduction potential
   */
  public async estimateVolumeReduction(): Promise<{
    totalDocuments: number;
    estimatedFiltered: number;
    estimatedReduction: number;
  }> {
    if (!this.db) {
      return { totalDocuments: 0, estimatedFiltered: 0, estimatedReduction: 0 };
    }

    try {
      const totalResult = await this.db.allDocs({ include_docs: false });
      const totalDocuments = totalResult.total_rows;

      const filteredIds = await this.applyFestivalScopeFilter();
      const estimatedFiltered = filteredIds.length;
      const estimatedReduction = ((totalDocuments - estimatedFiltered) / totalDocuments) * 100;

      console.log(`[SYNC FILTERS] Volume reduction estimate: ${estimatedReduction.toFixed(1)}% (${totalDocuments} -> ${estimatedFiltered})`);

      return {
        totalDocuments,
        estimatedFiltered,
        estimatedReduction
      };

    } catch (error) {
      console.error('[SYNC FILTERS] Failed to estimate volume reduction:', error);
      return { totalDocuments: 0, estimatedFiltered: 0, estimatedReduction: 0 };
    }
  }
}