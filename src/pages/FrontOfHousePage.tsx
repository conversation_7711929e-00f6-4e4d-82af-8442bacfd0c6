import React, { useState, useEffect, useCallback } from 'react';
import { ItemDocument, ItemName } from '../types/item';
import { databaseService } from '../services/database/index';
import { useFestival } from '../contexts/FestivalContext';
import { useSiteLocation } from '../contexts/SiteLocationContext';
import { format, parseISO } from 'date-fns';
import { NumberInputDialog } from '../components/shared/NumberInputDialog';
import { ItemButton } from '../components/shared/ItemButton';
import { SyncSettingsDialog } from '../components/SyncSettingsDialog';
import WbSunnyIcon from '@mui/icons-material/WbSunny';
import CloudIcon from '@mui/icons-material/Cloud';
import LocalDrinkIcon from '@mui/icons-material/LocalDrink';
import ShieldIcon from '@mui/icons-material/Shield';
import VolumeOffIcon from '@mui/icons-material/VolumeOff';
import FavoriteIcon from '@mui/icons-material/Favorite';
import GroupIcon from '@mui/icons-material/Group';
import LocalActivityIcon from '@mui/icons-material/LocalActivity';
import BoltIcon from '@mui/icons-material/Bolt';
import CleanHandsIcon from '@mui/icons-material/CleanHands';
import AutorenewIcon from '@mui/icons-material/Autorenew';
import HelpIcon from '@mui/icons-material/Help';
import WaterDropIcon from '@mui/icons-material/WaterDrop';
import BedIcon from '@mui/icons-material/Bed';
import Grid from '@mui/material/Grid';
import SyncIcon from '@mui/icons-material/Sync';
import ErrorIcon from '@mui/icons-material/Error';
import SettingsIcon from '@mui/icons-material/Settings';
import { DataGrid, GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { 
  Paper, 
  Typography, 
  Box, 
  Button, 
  Container, 
  Stack,
  Alert,
  Chip,
  CircularProgress,
  Tooltip
} from '@mui/material';
import debounce from 'lodash/debounce';

interface ItemRow {
  id: string;
  item: string;
  count: number;
  lastUpdated: string | null;
}

export const FrontOfHousePage: React.FC = () => {
  const { activeFestival } = useFestival();
  const { activeSiteLocation } = useSiteLocation();
  const [isLoading, setIsLoading] = useState(true);
  const [allCounts, setAllCounts] = useState<ItemDocument[]>([]);
  const [updatingItem, setUpdatingItem] = useState<ItemName | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isSyncing, setIsSyncing] = useState(false);
  const [hasPendingChanges, setHasPendingChanges] = useState(false);
  const [syncStatus, setSyncStatus] = useState<string>('');
  // State for handling the quantity input dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<{ name: ItemName, label: string } | null>(null);
  const [syncSettingsOpen, setSyncSettingsOpen] = useState(false);

  // Setup sync status listener
  useEffect(() => {
    const checkPendingChanges = async () => {
      try {
        const pending = await databaseService.hasPendingChanges();
        setHasPendingChanges(pending);
      } catch (error) {
        console.error('Error checking pending changes:', error);
      }
    };

    const unsubscribe = databaseService.addSyncListener(() => {
      checkPendingChanges();
    });

    // Initial check
    checkPendingChanges();

    return () => {
      try {
        unsubscribe();
      } catch (error) {
        console.error('Error unsubscribing from sync listener:', error);
      }
    };
  }, []);

  const loadCounts = useCallback(async () => {
    if (!activeFestival) {
      setAllCounts([]);
      setIsLoading(false);
      return;
    }

    try {
      console.log('Loading counts for festival:', activeFestival._id);
      setIsLoading(true);
      setError(null);
      const counts = await databaseService.getItemCountsByFestival(activeFestival._id);
      const filteredCounts = activeSiteLocation
        ? counts.filter(count => count.siteLocationId === activeSiteLocation.id)
        : counts;
      console.log('Got counts:', filteredCounts);
      setAllCounts(filteredCounts);
    } catch (error) {
      console.error('Error loading counts:', error);
      setError('Failed to load counts');
    } finally {
      setIsLoading(false);
    }
  }, [activeFestival, activeSiteLocation]);

  const handleSync = useCallback(async () => {
    if (isSyncing) return;

    try {
      setIsSyncing(true);
      setError(null);
      setSyncStatus('syncing');

      // Use forceSync to bypass persistent initialization checks for manual sync
      await databaseService.forceSync();
      await loadCounts();
      setSyncStatus('synced');

      console.log('[FRONT OF HOUSE] Manual sync completed successfully');
    } catch (error) {
      console.error('Sync failed:', error);
      setError('Failed to sync with server');
      setSyncStatus('error');
    } finally {
      setIsSyncing(false);
    }
  }, [isSyncing, loadCounts]);

  // Debounced version of handleCountChange
  const debouncedCountChange = useCallback(
    debounce(async (itemName: ItemName, quantity: number = 1) => {
      if (!activeFestival) return;

      try {
        console.log('Updating count for:', itemName, 'Festival ID:', activeFestival._id, 'Quantity:', quantity);
        const result = await databaseService.addOrUpdateItemCount(
          itemName, 
          activeFestival._id,
          activeSiteLocation?.id,
          quantity
        );
        console.log('Count updated successfully:', result);
        
        await loadCounts();
      } catch (error) {
        console.error('Error saving count:', error);
        setError('Failed to update count');
      } finally {
        setUpdatingItem(null);
      }
    }, 300),
    [activeFestival, activeSiteLocation, loadCounts]
  );

  // Handle the simple click on an item button (increment by 1)
  const handleCountChange = useCallback((itemName: ItemName) => {
    if (!activeFestival || updatingItem === itemName) {
      return;
    }

    setUpdatingItem(itemName);
    setError(null);
    debouncedCountChange(itemName, 1);
  }, [activeFestival, updatingItem, debouncedCountChange]);

  // Handle the long press - open dialog for quantity input
  const handleLongPress = useCallback((itemName: ItemName, label: string) => {
    setSelectedItem({ name: itemName, label });
    setDialogOpen(true);
  }, []);

  // Handle the confirmation from the quantity dialog
  const handleDialogConfirm = useCallback((quantity: number) => {
    if (selectedItem && quantity > 0) {
      setUpdatingItem(selectedItem.name);
      setError(null);
      debouncedCountChange(selectedItem.name, quantity);
    }
  }, [selectedItem, debouncedCountChange]);

  useEffect(() => {
    loadCounts();
  }, [loadCounts]);

  const getItemCount = useCallback((itemName: ItemName): number => {
    const todayDoc = allCounts.find(doc => {
      if (!doc.timestamp) return false;
      const docDate = new Date(doc.timestamp);
      const today = new Date();
      return docDate.toDateString() === today.toDateString();
    });
    return todayDoc ? (todayDoc[itemName] as number || 0) : 0;
  }, [allCounts]);

  const renderItem = useCallback((itemName: ItemName, IconComponent: React.ComponentType<any>, label: string) => (
    <ItemButton
      itemName={itemName}
      label={label}
      count={getItemCount(itemName)}
      isUpdating={updatingItem === itemName}
      onItemClick={handleCountChange}
      onLongPress={handleLongPress}
      IconComponent={IconComponent}
    />
  ), [updatingItem, handleCountChange, getItemCount, handleLongPress]);

  const columns: GridColDef<ItemRow>[] = [
    {
      field: 'item',
      headerName: 'Item',
      flex: 1,
    },
    {
      field: 'count',
      headerName: 'Count',
      width: 120,
      renderCell: (params: GridRenderCellParams<ItemRow>) => (
        <Chip
          label={params.row.count}
          sx={{
            bgcolor: 'purple.100',
            color: 'purple.600',
            fontWeight: 'bold',
            fontSize: '0.875rem'
          }}
        />
      ),
    },
    {
      field: 'lastUpdated',
      headerName: 'Last Updated',
      flex: 1,
      renderCell: (params: GridRenderCellParams<ItemRow>) => {
        return params.row.lastUpdated 
          ? format(parseISO(params.row.lastUpdated), 'yyyy-MM-dd HH:mm:ss')
          : 'N/A';
      },
    },
  ];

  const rows: ItemRow[] = allCounts.flatMap((doc) => 
    Object.entries(doc)
      .filter(([key, value]) => typeof value === 'number' && value > 0)
      .map(([name, count]) => ({
        id: `${doc._id}_${name}`,
        item: name,
        count: count as number,
        lastUpdated: doc.timestamp || null,
      }))
  );

  const locationTitle = activeSiteLocation 
    ? `Front of House - ${activeSiteLocation.type === 'arena' ? 'Arena' : 'Campsite'}`
    : 'Front of House';

  const getSyncButtonColor = () => {
    if (error) return 'error';
    if (hasPendingChanges) return 'warning';
    return 'primary';
  };

  const getSyncButtonIcon = () => {
    if (isSyncing) return <CircularProgress size={20} />;
    if (error) return <ErrorIcon />;
    return <SyncIcon />;
  };

  const getSyncButtonText = () => {
    if (isSyncing) return 'Syncing...';
    if (error) return 'Sync Failed';
    if (hasPendingChanges) return 'Changes Pending';
    return 'Sync Now';
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'text.primary' }}>
          {locationTitle}
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title={error || ''}>
            <Button
              variant="contained"
              color={getSyncButtonColor()}
              startIcon={getSyncButtonIcon()}
              onClick={handleSync}
              disabled={isSyncing}
            >
              {getSyncButtonText()}
            </Button>
          </Tooltip>

          <Tooltip title="Sync Settings">
            <Button
              variant="outlined"
              startIcon={<SettingsIcon />}
              onClick={() => setSyncSettingsOpen(true)}
              disabled={isSyncing}
            >
              Settings
            </Button>
          </Tooltip>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          {renderItem('Suncream', WbSunnyIcon, 'Suncream')}
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          {renderItem('Poncho', CloudIcon, 'Poncho')}
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          {renderItem('Water', LocalDrinkIcon, 'Water')}
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          {renderItem('SanitaryProducts', ShieldIcon, 'Sanitary\nProducts')}
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          {renderItem('Earplugs', VolumeOffIcon, 'Earplugs')}
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          {renderItem('Condoms', FavoriteIcon, 'Condoms')}
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          {renderItem('ChildrensWristbands', GroupIcon, 'Children\'s\nWristbands')}
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          {renderItem('GeneralWristbands', LocalActivityIcon, 'General\nWristbands')}
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          {renderItem('Charging', BoltIcon, 'Charging')}
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          {renderItem('Sanitizer', CleanHandsIcon, 'Sanitizer')}
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          {renderItem('ToiletRoll', AutorenewIcon, 'Toilet Roll')}
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          {renderItem('GeneralEnqs', HelpIcon, 'General\nEnquiries')}
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          {renderItem('HotWater', WaterDropIcon, 'Hot Water')}
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          {renderItem('RestAndRecuperation', BedIcon, 'Rest and\nRecuperation')}
        </Grid>
      </Grid>

      <Paper 
        elevation={3}
        sx={{ 
          bgcolor: 'background.paper',
          backdropFilter: 'blur(8px)',
          borderRadius: 2,
          p: 3
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" color="primary">
            Items Given Out
          </Typography>
        </Box>
        <Box sx={{ height: 400, width: '100%' }}>
          {isLoading ? (
            <Box sx={{ p: 3 }}>
              <Typography>Loading items...</Typography>
            </Box>
          ) : (
            <DataGrid<ItemRow>
              rows={rows}
              columns={columns}
              density="compact"
              disableRowSelectionOnClick
              sx={{
                '& .MuiDataGrid-cell': {
                  fontSize: '0.875rem',
                },
                border: 'none',
                bgcolor: 'background.paper',
                backdropFilter: 'blur(8px)',
              }}
            />
          )}
        </Box>
      </Paper>

      {/* Quantity input dialog */}
      <NumberInputDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        onConfirm={handleDialogConfirm}
        itemName={selectedItem?.name || ''}
        label={selectedItem?.label || ''}
      />

      {/* Sync settings dialog */}
      <SyncSettingsDialog
        open={syncSettingsOpen}
        onClose={() => setSyncSettingsOpen(false)}
      />
    </Container>
  );
};
