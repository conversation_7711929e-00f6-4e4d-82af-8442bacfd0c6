/**
 * Custom hook for managing admissions filter state
 * Provides centralized state management for cross-filtering functionality
 */

import { useState, useCallback, useMemo } from 'react';
import { 
  AdmissionsFilterState, 
  FilterActions, 
  FilterType,
  FilterSummary 
} from '../types/filters';

/**
 * Initial state for the admissions filter system
 */
const initialFilterState: AdmissionsFilterState = {
  timeFilters: [],
  reasonFilters: [],
  ageFilters: [],
  filterMode: 'AND'
};

/**
 * Custom hook for managing admissions filter state and actions
 * 
 * @returns Object containing filter state, actions, and computed properties
 */
export const useAdmissionsFilters = () => {
  const [filterState, setFilterState] = useState<AdmissionsFilterState>(initialFilterState);

  /**
   * Add a filter of the specified type
   */
  const addFilter = useCallback((type: FilterType, value: string) => {
    setFilterState(prev => {
      const key = getFilterKey(type);
      const currentFilters = prev[key];
      
      // Avoid duplicates
      if (currentFilters.includes(value)) {
        return prev;
      }
      
      return {
        ...prev,
        [key]: [...currentFilters, value]
      };
    });
  }, []);

  /**
   * Remove a specific filter
   */
  const removeFilter = useCallback((type: FilterType, value: string) => {
    setFilterState(prev => {
      const key = getFilterKey(type);
      return {
        ...prev,
        [key]: prev[key].filter(filter => filter !== value)
      };
    });
  }, []);

  /**
   * Clear all active filters
   */
  const clearFilters = useCallback(() => {
    setFilterState(initialFilterState);
  }, []);

  /**
   * Clear all filters of a specific type
   */
  const clearFilterType = useCallback((type: FilterType) => {
    setFilterState(prev => ({
      ...prev,
      [getFilterKey(type)]: []
    }));
  }, []);

  /**
   * Toggle between AND/OR filter modes
   */
  const toggleFilterMode = useCallback(() => {
    setFilterState(prev => ({
      ...prev,
      filterMode: prev.filterMode === 'AND' ? 'OR' : 'AND'
    }));
  }, []);

  /**
   * Check if a specific filter is active
   */
  const isFilterActive = useCallback((type: FilterType, value: string) => {
    const key = getFilterKey(type);
    return filterState[key].includes(value);
  }, [filterState]);

  /**
   * Get all active filters as a flat array
   */
  const activeFilters = useMemo(() => {
    return [
      ...filterState.timeFilters,
      ...filterState.reasonFilters,
      ...filterState.ageFilters
    ];
  }, [filterState]);

  /**
   * Check if any filters are active
   */
  const hasActiveFilters = useMemo(() => {
    return activeFilters.length > 0;
  }, [activeFilters]);

  /**
   * Get count of active filters by type
   */
  const filterCounts = useMemo(() => ({
    time: filterState.timeFilters.length,
    reason: filterState.reasonFilters.length,
    age: filterState.ageFilters.length,
    total: activeFilters.length
  }), [filterState, activeFilters]);

  /**
   * Get filters for a specific type
   */
  const getFiltersForType = useCallback((type: FilterType): string[] => {
    const key = getFilterKey(type);
    return filterState[key];
  }, [filterState]);

  /**
   * Create filter summary for display purposes
   */
  const createFilterSummary = useCallback((
    totalRecords: number,
    filteredRecords: number
  ): FilterSummary => ({
    totalRecords,
    filteredRecords,
    activeFilterCount: filterCounts.total,
    filterMode: filterState.filterMode
  }), [filterCounts.total, filterState.filterMode]);

  /**
   * Reset filters to initial state
   */
  const resetFilters = useCallback(() => {
    setFilterState(initialFilterState);
  }, []);

  /**
   * Set filter mode directly
   */
  const setFilterMode = useCallback((mode: 'AND' | 'OR') => {
    setFilterState(prev => ({
      ...prev,
      filterMode: mode
    }));
  }, []);

  /**
   * Bulk add filters (useful for programmatic filter setting)
   */
  const addFilters = useCallback((type: FilterType, values: string[]) => {
    setFilterState(prev => {
      const key = getFilterKey(type);
      const currentFilters = prev[key];
      const newFilters = values.filter(value => !currentFilters.includes(value));
      
      return {
        ...prev,
        [key]: [...currentFilters, ...newFilters]
      };
    });
  }, []);

  /**
   * Get filter state as a serializable object (useful for URL params or storage)
   */
  const getSerializableState = useCallback(() => {
    return {
      time: filterState.timeFilters.join(','),
      reason: filterState.reasonFilters.join(','),
      age: filterState.ageFilters.join(','),
      mode: filterState.filterMode
    };
  }, [filterState]);

  /**
   * Restore filter state from serializable object
   */
  const restoreFromSerializable = useCallback((serialized: {
    time?: string;
    reason?: string;
    age?: string;
    mode?: 'AND' | 'OR';
  }) => {
    setFilterState({
      timeFilters: serialized.time ? serialized.time.split(',').filter(Boolean) : [],
      reasonFilters: serialized.reason ? serialized.reason.split(',').filter(Boolean) : [],
      ageFilters: serialized.age ? serialized.age.split(',').filter(Boolean) : [],
      filterMode: serialized.mode || 'AND'
    });
  }, []);

  // Combine all actions into a single object
  const filterActions: FilterActions = {
    addFilter,
    removeFilter,
    clearFilters,
    clearFilterType,
    toggleFilterMode,
    isFilterActive
  };

  return {
    // State
    filterState,
    
    // Actions
    filterActions,
    
    // Extended actions
    addFilters,
    resetFilters,
    setFilterMode,
    
    // Computed properties
    activeFilters,
    hasActiveFilters,
    filterCounts,
    
    // Utility functions
    getFiltersForType,
    createFilterSummary,
    getSerializableState,
    restoreFromSerializable
  };
};

/**
 * Helper function to get the correct filter key for a given filter type
 */
function getFilterKey(type: FilterType): 'timeFilters' | 'reasonFilters' | 'ageFilters' {
  switch (type) {
    case FilterType.TIME:
      return 'timeFilters';
    case FilterType.REASON:
      return 'reasonFilters';
    case FilterType.AGE:
      return 'ageFilters';
    default:
      throw new Error(`Unknown filter type: ${type}`);
  }
}

export default useAdmissionsFilters;