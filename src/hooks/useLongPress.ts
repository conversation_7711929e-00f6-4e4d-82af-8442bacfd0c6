import { useCallback, useRef, useState, useEffect } from 'react';

interface UseLongPressOptions {
  onLongPress: (event: React.MouseEvent | React.TouchEvent) => void;
  onClick?: (event: React.MouseEvent | React.TouchEvent) => void;
  threshold?: number; // default 500ms
  captureEvent?: boolean; // prevent default behavior
}

interface UseLongPressReturn {
  onMouseDown: (event: React.MouseEvent) => void;
  onMouseUp: (event: React.MouseEvent) => void;
  onMouseLeave: (event: React.MouseEvent) => void;
  onTouchStart: (event: React.TouchEvent) => void;
  onTouchEnd: (event: React.TouchEvent) => void;
  onTouchMove: (event: React.TouchEvent) => void;
  isLongPressing: boolean;
}

/**
 * Enhanced long press hook with visual feedback and robust event handling
 *
 * Features:
 * - 500ms threshold for long press detection
 * - Support for both mouse and touch events
 * - Proper event handling and cleanup
 * - Visual feedback during press duration
 * - Cancellation on mouse/touch move
 * - Cross-platform compatibility
 * - Performance optimized with minimal re-renders
 *
 * @param options Configuration options for long press behavior
 * @returns Event handlers and state for long press detection
 */
export const useLongPress = ({
  onLongPress,
  onClick,
  threshold = 500,
  captureEvent = true
}: UseLongPressOptions): UseLongPressReturn => {
  const timerRef = useRef<number | null>(null);
  const isLongPressRef = useRef(false);
  const startPositionRef = useRef<{ x: number; y: number } | null>(null);
  const [isLongPressing, setIsLongPressing] = useState(false);

  // Movement threshold to cancel long press (in pixels)
  const MOVEMENT_THRESHOLD = 10;

  /**
   * Clear the timer and reset state
   */
  const clearTimer = useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
    setIsLongPressing(false);
    isLongPressRef.current = false;
    startPositionRef.current = null;
  }, []);

  /**
   * Start the long press timer
   */
  const startLongPress = useCallback((event: React.MouseEvent | React.TouchEvent) => {
    // Clear any existing timer
    clearTimer();
    
    // Capture starting position for movement detection
    const clientX = 'touches' in event ? event.touches[0]?.clientX : event.clientX;
    const clientY = 'touches' in event ? event.touches[0]?.clientY : event.clientY;
    
    startPositionRef.current = { x: clientX, y: clientY };
    
    // Prevent default behavior if requested
    if (captureEvent) {
      event.preventDefault();
    }
    
    // Set visual feedback state
    setIsLongPressing(true);
    
    // Start the timer
    timerRef.current = window.setTimeout(() => {
      isLongPressRef.current = true;
      setIsLongPressing(false); // Remove visual feedback when long press triggers
      onLongPress(event);
    }, threshold);
  }, [onLongPress, threshold, captureEvent, clearTimer]);

  /**
   * End the long press and potentially trigger click
   */
  const endLongPress = useCallback((event: React.MouseEvent | React.TouchEvent, shouldTriggerClick = true) => {
    const wasLongPress = isLongPressRef.current;
    
    clearTimer();
    
    // Trigger click only if it wasn't a long press and click handler exists
    if (shouldTriggerClick && !wasLongPress && onClick) {
      onClick(event);
    }
  }, [onClick, clearTimer]);

  /**
   * Handle mouse/touch movement to cancel long press if moved too far
   */
  const handleMove = useCallback((event: React.MouseEvent | React.TouchEvent) => {
    if (!startPositionRef.current || !timerRef.current) {
      return;
    }
    
    const clientX = 'touches' in event ? event.touches[0]?.clientX : event.clientX;
    const clientY = 'touches' in event ? event.touches[0]?.clientY : event.clientY;
    
    const deltaX = Math.abs(clientX - startPositionRef.current.x);
    const deltaY = Math.abs(clientY - startPositionRef.current.y);
    
    // Cancel long press if moved beyond threshold
    if (deltaX > MOVEMENT_THRESHOLD || deltaY > MOVEMENT_THRESHOLD) {
      clearTimer();
    }
  }, [clearTimer, MOVEMENT_THRESHOLD]);

  /**
   * Mouse event handlers
   */
  const onMouseDown = useCallback((event: React.MouseEvent) => {
    startLongPress(event);
  }, [startLongPress]);

  const onMouseUp = useCallback((event: React.MouseEvent) => {
    endLongPress(event, true);
  }, [endLongPress]);

  const onMouseLeave = useCallback((event: React.MouseEvent) => {
    endLongPress(event, false);
  }, [endLongPress]);

  const onMouseMove = useCallback((event: React.MouseEvent) => {
    handleMove(event);
  }, [handleMove]);

  /**
   * Touch event handlers
   */
  const onTouchStart = useCallback((event: React.TouchEvent) => {
    startLongPress(event);
  }, [startLongPress]);

  const onTouchEnd = useCallback((event: React.TouchEvent) => {
    endLongPress(event, true);
  }, [endLongPress]);

  const onTouchMove = useCallback((event: React.TouchEvent) => {
    handleMove(event);
  }, [handleMove]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      clearTimer();
    };
  }, [clearTimer]);

  return {
    onMouseDown,
    onMouseUp,
    onMouseLeave,
    onTouchStart,
    onTouchEnd,
    onTouchMove,
    isLongPressing
  };
};

export default useLongPress;