import { useState, useEffect, useCallback } from 'react';
import { databaseService } from '../services/database/index';
import { useFestival } from '../contexts/FestivalContext';

interface UseSimpleDataOptions {
  enabled?: boolean;
  onError?: (error: Error) => void;
}

interface SimpleDataState<T> {
  data: T | null;
  isLoading: boolean;
  error: string | null;
  isFromCache: boolean;
}

// Simplified data hook - direct database queries without complex caching
export function useSimpleData<T>(
  fetchFunction: () => Promise<T>,
  options: UseSimpleDataOptions = {}
): SimpleDataState<T> & { refetch: () => Promise<void> } {
  const { activeFestival } = useFestival();
  const { enabled = true, onError } = options;
  
  const [state, setState] = useState<SimpleDataState<T>>({
    data: null,
    isLoading: false,
    error: null,
    isFromCache: false
  });

  const loadData = useCallback(async () => {
    if (!enabled || !activeFestival) {
      console.log('[DEBUG] useSimpleData: Skipping load - enabled:', enabled, 'activeFestival:', !!activeFestival);
      setState(prev => ({ ...prev, isLoading: false }));
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      console.log('[DEBUG] useSimpleData: Loading data...');
      const startTime = Date.now();
      
      const data = await fetchFunction();
      
      const loadTime = Date.now() - startTime;
      console.log('[DEBUG] useSimpleData: Data loaded in', loadTime, 'ms');

      setState({
        data,
        isLoading: false,
        error: null,
        isFromCache: false
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('[DEBUG] useSimpleData: Load failed:', errorMessage);

      setState(prev => ({
        ...prev, // Preserve existing data to prevent data loss appearance
        isLoading: false,
        error: errorMessage,
        isFromCache: false
      }));
      
      if (onError && error instanceof Error) {
        onError(error);
      }
    }
  }, [activeFestival?._id, enabled]); // Remove fetchFunction from dependencies to prevent loops

  const refetch = useCallback(async () => {
    await loadData();
  }, [loadData]);

  // Load data when festival changes or component mounts
  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    ...state,
    refetch
  };
}

// Specialized hooks for common data types - simplified versions
export function useSimpleAdmissions(options?: UseSimpleDataOptions) {
  const { activeFestival } = useFestival();
  
  const fetchAdmissions = useCallback(() => {
    if (!activeFestival) return Promise.resolve([]);
    return databaseService.getAdmissionsByFestival(activeFestival._id);
  }, [activeFestival?._id]);
  
  return useSimpleData(
    fetchAdmissions,
    { ...options, enabled: !!activeFestival && (options?.enabled ?? true) }
  );
}

export function useSimpleItems(options?: UseSimpleDataOptions) {
  const { activeFestival } = useFestival();
  
  const fetchItems = useCallback(() => {
    if (!activeFestival) return Promise.resolve([]);
    return databaseService.getItemCountsByFestival(activeFestival._id);
  }, [activeFestival?._id]);
  
  return useSimpleData(
    fetchItems,
    { ...options, enabled: !!activeFestival && (options?.enabled ?? true) }
  );
}

export function useSimpleLostProperty(options?: UseSimpleDataOptions) {
  const { activeFestival } = useFestival();
  
  const fetchLostProperty = useCallback(async () => {
    if (!activeFestival) return [];
    return databaseService.getLostPropertyItems(activeFestival._id);
  }, [activeFestival?._id]);
  
  return useSimpleData(
    fetchLostProperty,
    { ...options, enabled: !!activeFestival && (options?.enabled ?? true) }
  );
}

export function useSimpleKnowledgeBase(options?: UseSimpleDataOptions) {
  const { activeFestival } = useFestival();
  
  const fetchKnowledgeBase = useCallback(() => {
    if (!activeFestival) return Promise.resolve([]);
    return databaseService.getKnowledgeBaseItems(activeFestival._id);
  }, [activeFestival?._id]);
  
  return useSimpleData(
    fetchKnowledgeBase,
    { ...options, enabled: !!activeFestival && (options?.enabled ?? true) }
  );
}

export function useSimpleKnowledgeCategories(options?: UseSimpleDataOptions) {
  const { activeFestival } = useFestival();
  
  const fetchKnowledgeCategories = useCallback(() => {
    if (!activeFestival) return Promise.resolve([]);
    return databaseService.getKnowledgeBaseCategories(activeFestival._id);
  }, [activeFestival?._id]);
  
  return useSimpleData(
    fetchKnowledgeCategories,
    { ...options, enabled: !!activeFestival && (options?.enabled ?? true) }
  );
}

// Legacy compatibility exports - map old hooks to new simplified versions
export const useSmartData = useSimpleData;
export const useSmartAdmissions = useSimpleAdmissions;
export const useSmartItems = useSimpleItems;
export const useSmartLostProperty = useSimpleLostProperty;
export const useSmartKnowledgeBase = useSimpleKnowledgeBase;
export const useSmartKnowledgeCategories = useSimpleKnowledgeCategories;

// Simplified cache control hook - no complex cache management
export function useCacheControl() {
  const clearCache = useCallback(() => {
    console.log('Cache clear requested - triggering manual sync');
    databaseService.manualSync();
  }, []);
  
  return {
    stats: { size: 0, entries: [] },
    refreshStats: () => {},
    clearCache,
    clearFestivalCache: () => {},
    isInitialSyncComplete: true // Always true in simplified mode
  };
}