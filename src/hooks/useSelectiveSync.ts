import { useState, useEffect, useCallback, useRef } from 'react';
import { databaseService } from '../services/database';
import type { SelectiveSyncManager } from '../services/database/selective-sync-manager';
import type { SyncCacheManager } from '../services/database/sync-cache-manager';
import type { IndexHealthMonitor } from '../services/database/index-health-monitor';

// Types for hook return values
export interface SyncStatus {
  isActive: boolean;
  progress: number;
  currentFestival: string | null;
  lastSyncTime: Date | null;
  error: string | null;
  documentsProcessed: number;
  totalDocuments: number;
}

export interface CacheStatus {
  size: number;
  hitRate: number;
  lastCleanup: Date | null;
  storageUsed: number;
  storageQuota: number;
}

export interface IndexHealth {
  isHealthy: boolean;
  corruptedIndexes: string[];
  lastCheck: Date | null;
  recoveryInProgress: boolean;
}

// Main selective sync hook
export const useSelectiveSync = () => {
  const [isEnabled, setIsEnabled] = useState(false);
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isActive: false,
    progress: 0,
    currentFestival: null,
    lastSyncTime: null,
    error: null,
    documentsProcessed: 0,
    totalDocuments: 0
  });

  const syncManagerRef = useRef<SelectiveSyncManager | null>(null);

  useEffect(() => {
    const checkSelectiveSyncAvailability = async () => {
      try {
        await databaseService.waitForInitialization();
        const isSelectiveSyncEnabled = (databaseService as any).useSelectiveSync;
        setIsEnabled(isSelectiveSyncEnabled);
        
        if (isSelectiveSyncEnabled && (databaseService as any).selectiveSyncManager) {
          syncManagerRef.current = (databaseService as any).selectiveSyncManager;
        }
      } catch (error) {
        console.error('Error checking selective sync availability:', error);
      }
    };

    checkSelectiveSyncAvailability();
  }, []);

  const startSync = useCallback(async (festivalId?: string) => {
    if (!syncManagerRef.current) {
      throw new Error('Selective sync not available');
    }

    try {
      setSyncStatus(prev => ({ ...prev, isActive: true, error: null }));
      await syncManagerRef.current.performSelectiveSync();
      setSyncStatus(prev => ({ 
        ...prev, 
        isActive: false, 
        lastSyncTime: new Date(),
        currentFestival: festivalId || null
      }));
    } catch (error) {
      setSyncStatus(prev => ({ 
        ...prev, 
        isActive: false, 
        error: error instanceof Error ? error.message : 'Sync failed'
      }));
      throw error;
    }
  }, []);

  const stopSync = useCallback(async () => {
    if (!syncManagerRef.current) {
      return;
    }

    try {
      await syncManagerRef.current.stopSync();
      setSyncStatus(prev => ({ ...prev, isActive: false }));
    } catch (error) {
      console.error('Error stopping sync:', error);
    }
  }, []);

  const resetSync = useCallback(async () => {
    if (!syncManagerRef.current) {
      throw new Error('Selective sync not available');
    }

    try {
      await syncManagerRef.current.resetSync();
      setSyncStatus({
        isActive: false,
        progress: 0,
        currentFestival: null,
        lastSyncTime: null,
        error: null,
        documentsProcessed: 0,
        totalDocuments: 0
      });
    } catch (error) {
      console.error('Error resetting sync:', error);
      throw error;
    }
  }, []);

  return {
    isEnabled,
    syncStatus,
    startSync,
    stopSync,
    resetSync
  };
};

// Festival-scoped sync management hook
export const useFestivalSync = (festivalId: string) => {
  const { isEnabled, startSync, stopSync, syncStatus } = useSelectiveSync();
  const [isSyncing, setIsSyncing] = useState(false);

  const syncFestival = useCallback(async () => {
    if (!isEnabled) {
      throw new Error('Selective sync not enabled');
    }

    try {
      setIsSyncing(true);
      await startSync(festivalId);
    } finally {
      setIsSyncing(false);
    }
  }, [isEnabled, startSync, festivalId]);

  const stopFestivalSync = useCallback(async () => {
    if (syncStatus.currentFestival === festivalId) {
      await stopSync();
    }
    setIsSyncing(false);
  }, [stopSync, syncStatus.currentFestival, festivalId]);

  const isFestivalSyncing = syncStatus.isActive && syncStatus.currentFestival === festivalId;

  return {
    isEnabled,
    isSyncing: isSyncing || isFestivalSyncing,
    syncFestival,
    stopFestivalSync,
    syncStatus: isFestivalSyncing ? syncStatus : null
  };
};

// Sync status monitoring hook
export const useSyncStatus = () => {
  const [status, setStatus] = useState<SyncStatus>({
    isActive: false,
    progress: 0,
    currentFestival: null,
    lastSyncTime: null,
    error: null,
    documentsProcessed: 0,
    totalDocuments: 0
  });

  const [isPolling, setIsPolling] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const startPolling = useCallback((intervalMs: number = 1000) => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    setIsPolling(true);
    intervalRef.current = setInterval(async () => {
      try {
        const dbService = databaseService as any;
        if (dbService.useSelectiveSync && dbService.selectiveSyncManager) {
          const syncManager = dbService.selectiveSyncManager;
          // Get sync status from manager (would need to be implemented)
          // This is a placeholder for the actual status retrieval
          const currentStatus = await syncManager.getSyncStatus?.() || status;
          setStatus(currentStatus);
        }
      } catch (error) {
        console.error('Error polling sync status:', error);
      }
    }, intervalMs);
  }, [status]);

  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    setIsPolling(false);
  }, []);

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    status,
    isPolling,
    startPolling,
    stopPolling
  };
};

// Cache management hook
export const useSyncCache = () => {
  const [cacheStatus, setCacheStatus] = useState<CacheStatus>({
    size: 0,
    hitRate: 0,
    lastCleanup: null,
    storageUsed: 0,
    storageQuota: 0
  });

  const cacheManagerRef = useRef<SyncCacheManager | null>(null);

  useEffect(() => {
    const initializeCacheManager = async () => {
      try {
        const dbService = databaseService as any;
        if (dbService.useSelectiveSync && dbService.syncCacheManager) {
          cacheManagerRef.current = dbService.syncCacheManager;
          await updateCacheStatus();
        }
      } catch (error) {
        console.error('Error initializing cache manager:', error);
      }
    };

    initializeCacheManager();
  }, []);

  const updateCacheStatus = useCallback(async () => {
    if (!cacheManagerRef.current) {
      return;
    }

    try {
      const manager = cacheManagerRef.current;
      const cacheStats = manager.getCacheStats();
      const status: CacheStatus = {
        size: cacheStats.totalDocuments,
        hitRate: cacheStats.hitRate,
        lastCleanup: cacheStats.lastCleanup,
        storageUsed: cacheStats.totalSizeEstimate,
        storageQuota: 50 * 1024 * 1024 // 50MB default from SyncCacheManager
      };
      setCacheStatus(status);
    } catch (error) {
      console.error('Error updating cache status:', error);
    }
  }, []);

  const clearCache = useCallback(async (festivalId?: string) => {
    if (!cacheManagerRef.current) {
      throw new Error('Cache manager not available');
    }

    try {
      // SyncCacheManager only has clearCache method, not clearFestivalCache
      await cacheManagerRef.current.clearCache();
      await updateCacheStatus();
    } catch (error) {
      console.error('Error clearing cache:', error);
      throw error;
    }
  }, [updateCacheStatus]);

  const optimizeCache = useCallback(async () => {
    if (!cacheManagerRef.current) {
      throw new Error('Cache manager not available');
    }

    try {
      // SyncCacheManager doesn't have optimizeCache, use internal optimization
      // This would trigger during normal cache operations
      await updateCacheStatus();
    } catch (error) {
      console.error('Error optimizing cache:', error);
      throw error;
    }
  }, [updateCacheStatus]);

  return {
    cacheStatus,
    updateCacheStatus,
    clearCache,
    optimizeCache
  };
};

// Index health monitoring hook
export const useIndexHealth = () => {
  const [indexHealth, setIndexHealth] = useState<IndexHealth>({
    isHealthy: true,
    corruptedIndexes: [],
    lastCheck: null,
    recoveryInProgress: false
  });

  const healthMonitorRef = useRef<IndexHealthMonitor | null>(null);

  useEffect(() => {
    const initializeHealthMonitor = async () => {
      try {
        await databaseService.waitForInitialization();
        if ((databaseService as any).useSelectiveSync && (databaseService as any).indexHealthMonitor) {
          healthMonitorRef.current = (databaseService as any).indexHealthMonitor;
          await checkIndexHealth();
        }
      } catch (error) {
        console.error('Error initializing health monitor:', error);
      }
    };

    initializeHealthMonitor();
  }, []);

  const checkIndexHealth = useCallback(async () => {
    if (!healthMonitorRef.current) {
      return;
    }

    try {
      const monitor = healthMonitorRef.current;
      const healthSummary = await monitor.checkIndexHealth();
      
      setIndexHealth({
        isHealthy: !healthSummary.hasCorruption,
        corruptedIndexes: healthSummary.corruptedIndexes,
        lastCheck: new Date(),
        recoveryInProgress: false
      });
    } catch (error) {
      console.error('Error checking index health:', error);
      setIndexHealth(prev => ({
        ...prev,
        isHealthy: false,
        lastCheck: new Date()
      }));
    }
  }, []);

  const recoverIndexes = useCallback(async () => {
    if (!healthMonitorRef.current) {
      throw new Error('Health monitor not available');
    }

    try {
      setIndexHealth(prev => ({ ...prev, recoveryInProgress: true }));
      await healthMonitorRef.current.recoverCorruptedIndexes();
      await checkIndexHealth();
    } catch (error) {
      console.error('Error recovering indexes:', error);
      throw error;
    } finally {
      setIndexHealth(prev => ({ ...prev, recoveryInProgress: false }));
    }
  }, [checkIndexHealth]);

  return {
    indexHealth,
    checkIndexHealth,
    recoverIndexes
  };
};

// Combined hook for comprehensive selective sync management
export const useSelectiveSyncManager = () => {
  const selectiveSync = useSelectiveSync();
  const syncStatus = useSyncStatus();
  const syncCache = useSyncCache();
  const indexHealth = useIndexHealth();

  return {
    ...selectiveSync,
    syncStatus: syncStatus.status,
    startStatusPolling: syncStatus.startPolling,
    stopStatusPolling: syncStatus.stopPolling,
    isStatusPolling: syncStatus.isPolling,
    cacheStatus: syncCache.cacheStatus,
    updateCacheStatus: syncCache.updateCacheStatus,
    clearCache: syncCache.clearCache,
    optimizeCache: syncCache.optimizeCache,
    indexHealth: indexHealth.indexHealth,
    checkIndexHealth: indexHealth.checkIndexHealth,
    recoverIndexes: indexHealth.recoverIndexes
  };
};