/**
 * Utility functions for applying filters to admissions data
 * Handles the core filtering logic for the cross-filtering system
 */

import { WelfareAdmission } from '../types/admission';
import { 
  AdmissionsFilterState, 
  FilteredData, 
  TimeOfDayData, 
  ChartData,
  FilterSummary 
} from '../types/filters';

/**
 * Apply filters to admissions data based on the current filter state
 * 
 * @param admissions - Array of admission records to filter
 * @param filterState - Current filter state containing active filters
 * @returns Filtered array of admissions
 */
export function applyFiltersToAdmissions(
  admissions: WelfareAdmission[],
  filterState: AdmissionsFilterState
): WelfareAdmission[] {
  const { timeFilters, reasonFilters, ageFilters, filterMode } = filterState;
  
  // If no filters are active, return all admissions
  if (timeFilters.length === 0 && reasonFilters.length === 0 && ageFilters.length === 0) {
    return admissions;
  }

  return admissions.filter(admission => {
    const timeMatch = timeFilters.length === 0 || matchesTimeFilter(admission, timeFilters);
    const reasonMatch = reasonFilters.length === 0 || matchesReasonFilter(admission, reasonFilters);
    const ageMatch = ageFilters.length === 0 || matchesAgeFilter(admission, ageFilters);

    // Apply filter mode logic
    if (filterMode === 'AND') {
      return timeMatch && reasonMatch && ageMatch;
    } else {
      // OR mode: at least one filter type must match if it has active filters
      const hasTimeFilters = timeFilters.length > 0;
      const hasReasonFilters = reasonFilters.length > 0;
      const hasAgeFilters = ageFilters.length > 0;

      return (hasTimeFilters ? timeMatch : false) ||
             (hasReasonFilters ? reasonMatch : false) ||
             (hasAgeFilters ? ageMatch : false);
    }
  });
}

/**
 * Check if an admission matches any of the active time filters
 */
function matchesTimeFilter(admission: WelfareAdmission, timeFilters: string[]): boolean {
  // Use Attended field as the primary timestamp, fallback to createdAt
  const timeString = admission.Attended || admission.createdAt;
  if (!timeString) return false;
  
  const admissionTime = new Date(timeString);
  const hour = admissionTime.getHours();
  
  return timeFilters.some(timeRange => {
    const hourRange = parseTimeRange(timeRange);
    return hour >= hourRange.start && hour < hourRange.end;
  });
}

/**
 * Check if an admission matches any of the active reason filters
 */
function matchesReasonFilter(admission: WelfareAdmission, reasonFilters: string[]): boolean {
  return reasonFilters.includes(admission.ReasonCategory);
}

/**
 * Check if an admission matches any of the active age filters
 */
function matchesAgeFilter(admission: WelfareAdmission, ageFilters: string[]): boolean {
  if (!admission.Age) return false;
  
  return ageFilters.some(ageRange => {
    const range = parseAgeRange(ageRange);
    return admission.Age! >= range.min && admission.Age! <= range.max;
  });
}

/**
 * Parse a time range string (e.g., "10:00-11:00") into start and end hours
 */
function parseTimeRange(timeRange: string): { start: number; end: number } {
  // Handle both "HH:00-HH:00" and "HH" formats
  if (timeRange.includes('-')) {
    const [startStr, endStr] = timeRange.split('-');
    return {
      start: parseInt(startStr.split(':')[0]),
      end: parseInt(endStr.split(':')[0])
    };
  } else {
    // Single hour format
    const hour = parseInt(timeRange);
    return { start: hour, end: hour + 1 };
  }
}

/**
 * Parse an age range string (e.g., "18-25") into min and max values
 */
function parseAgeRange(ageRange: string): { min: number; max: number } {
  if (ageRange.includes('-')) {
    const [minStr, maxStr] = ageRange.split('-');
    return {
      min: parseInt(minStr),
      max: parseInt(maxStr)
    };
  } else if (ageRange.includes('+')) {
    // Handle "65+" format
    const min = parseInt(ageRange.replace('+', ''));
    return { min, max: 999 };
  } else {
    // Single age
    const age = parseInt(ageRange);
    return { min: age, max: age };
  }
}

/**
 * Get filtered chart data for all chart types
 * 
 * @param admissions - Original admissions data
 * @param filterState - Current filter state
 * @returns Object containing filtered data for all chart types
 */
export function getFilteredChartData(
  admissions: WelfareAdmission[],
  filterState: AdmissionsFilterState
): FilteredData {
  const filteredAdmissions = applyFiltersToAdmissions(admissions, filterState);
  
  // Generate chart data from filtered admissions
  const timeData = generateTimeOfDayData(filteredAdmissions);
  const reasonData = generateReasonData(filteredAdmissions);
  const ageData = generateAgeData(filteredAdmissions);
  
  // Create filter summary
  const filterSummary: FilterSummary = {
    totalRecords: admissions.length,
    filteredRecords: filteredAdmissions.length,
    activeFilterCount: filterState.timeFilters.length + 
                      filterState.reasonFilters.length + 
                      filterState.ageFilters.length,
    filterMode: filterState.filterMode
  };

  return {
    filteredAdmissions,
    timeData,
    reasonData,
    ageData,
    filterSummary
  };
}

/**
 * Generate time of day chart data from admissions
 */
function generateTimeOfDayData(admissions: WelfareAdmission[]): TimeOfDayData[] {
  const hourCounts: { [hour: number]: number } = {};
  
  // Initialize all hours to 0
  for (let i = 0; i < 24; i++) {
    hourCounts[i] = 0;
  }
  
  // Count admissions by hour
  admissions.forEach(admission => {
    // Use Attended field as the primary timestamp, fallback to createdAt
    const timeString = admission.Attended || admission.createdAt;
    if (!timeString) return;
    
    const hour = new Date(timeString).getHours();
    hourCounts[hour]++;
  });
  
  // Convert to chart data format
  return Object.entries(hourCounts).map(([hour, count]) => ({
    hour: parseInt(hour),
    count,
    label: `${hour.padStart(2, '0')}:00`
  }));
}

/**
 * Generate reason chart data from admissions
 */
function generateReasonData(admissions: WelfareAdmission[]): ChartData[] {
  const reasonCounts: { [reason: string]: number } = {};
  
  admissions.forEach(admission => {
    const reason = admission.ReasonCategory;
    reasonCounts[reason] = (reasonCounts[reason] || 0) + 1;
  });
  
  return Object.entries(reasonCounts).map(([reason, count]) => ({
    reason,
    count
  }));
}

/**
 * Generate age distribution chart data from admissions
 */
function generateAgeData(admissions: WelfareAdmission[]): ChartData[] {
  const ageRanges = [
    { label: 'Under 18', min: 0, max: 17 },
    { label: '18-25', min: 18, max: 25 },
    { label: '26-35', min: 26, max: 35 },
    { label: '36-45', min: 36, max: 45 },
    { label: '46-55', min: 46, max: 55 },
    { label: '56-65', min: 56, max: 65 },
    { label: '65+', min: 66, max: 999 }
  ];
  
  const ageCounts: { [range: string]: number } = {};
  
  // Initialize all ranges to 0
  ageRanges.forEach(range => {
    ageCounts[range.label] = 0;
  });
  
  // Count admissions by age range
  admissions.forEach(admission => {
    if (admission.Age) {
      const ageRange = ageRanges.find(range => 
        admission.Age! >= range.min && admission.Age! <= range.max
      );
      if (ageRange) {
        ageCounts[ageRange.label]++;
      }
    }
  });
  
  return Object.entries(ageCounts).map(([ageRange, count]) => ({
    ageRange,
    count
  }));
}

/**
 * Get available filter values for each filter type based on the data
 */
export function getAvailableFilterValues(admissions: WelfareAdmission[]) {
  const timeValues = Array.from({ length: 24 }, (_, i) => `${i.toString().padStart(2, '0')}:00`);
  
  const reasonValues = Array.from(
    new Set(admissions.map(admission => admission.ReasonCategory))
  ).sort();
  
  const ageValues = [
    'Under 18',
    '18-25',
    '26-35',
    '36-45',
    '46-55',
    '56-65',
    '65+'
  ];
  
  return {
    time: timeValues,
    reason: reasonValues,
    age: ageValues
  };
}

/**
 * Create a filter value from chart interaction data
 */
export function createFilterValueFromChartData(
  filterType: 'TIME' | 'REASON' | 'AGE',
  chartData: any
): string {
  switch (filterType) {
    case 'TIME':
      return chartData.label || `${chartData.hour}:00`;
    case 'REASON':
      return chartData.reason;
    case 'AGE':
      return chartData.ageRange;
    default:
      throw new Error(`Unknown filter type: ${filterType}`);
  }
}

/**
 * Check if a filter value is valid for the given filter type
 */
export function isValidFilterValue(
  filterType: 'TIME' | 'REASON' | 'AGE',
  value: string,
  availableValues: string[]
): boolean {
  return availableValues.includes(value);
}

/**
 * Format filter display text for UI components
 */
export function formatFilterDisplayText(
  filterType: 'TIME' | 'REASON' | 'AGE',
  value: string
): string {
  switch (filterType) {
    case 'TIME':
      return value.includes(':') ? value : `${value}:00`;
    case 'REASON':
    case 'AGE':
      return value;
    default:
      return value;
  }
}