import { format, getHours, parseISO } from 'date-fns';
import { WelfareAdmission } from '../types/admission';
import { ItemDocument, LostPropertyItem } from '../types/item';
import { SensoryHubVisit } from '../types/sensory-hub';
import { AdmissionsFilterState } from '../types/filters';
import { applyFiltersToAdmissions } from './admissionsFilterUtils';

export const COLORS = ['#662D91', '#E0338C', '#8E44AD', '#E74C3C'];

export const ITEM_TYPES = [
  'Suncream',
  'Poncho',
  'Water',
  'SanitaryProducts',
  'Earplugs',
  'Condoms',
  'ChildrensWristbands',
  'GeneralWristbands',
  'Charging',
  'Sanitizer',
  'ToiletRoll',
  'GeneralEnqs',
] as const;

interface TimeOfDayData {
  hour: number;
  count: number;
  label: string;
}

interface ChartData {
  count: number;
  [key: string]: string | number;
}

// Admissions data processing
export const getAdmissionsByTimeOfDay = (admissions: WelfareAdmission[]): TimeOfDayData[] => {
  const hourlyData = Array.from({ length: 24 }, (_, i) => ({
    hour: i,
    count: 0,
    label: format(new Date().setHours(i, 0, 0, 0), 'ha')
  }));

  admissions.forEach(admission => {
    const hour = getHours(parseISO(admission.Attended));
    const index = hourlyData.findIndex(d => d.hour === hour);
    if (index !== -1) {
      hourlyData[index].count++;
    }
  });

  return hourlyData;
};

export const getAdmissionsByReason = (admissions: WelfareAdmission[]): ChartData[] => {
  const grouped = admissions.reduce((acc: Record<string, number>, admission) => {
    acc[admission.ReasonCategory] = (acc[admission.ReasonCategory] || 0) + 1;
    return acc;
  }, {});

  return Object.entries(grouped).map(([reason, count]) => ({
    reason,
    count,
  }));
};

export const getAgeDistribution = (admissions: WelfareAdmission[]): ChartData[] => {
  const grouped = admissions.reduce((acc: Record<string, number>, admission) => {
    const ageGroup = Math.floor((admission.Age || 0) / 10) * 10;
    const label = `${ageGroup}-${ageGroup + 9}`;
    acc[label] = (acc[label] || 0) + 1;
    return acc;
  }, {});

  return Object.entries(grouped).map(([ageGroup, count]) => ({
    ageGroup,
    count,
  }));
};

// Filter-aware admissions data processing
export const getFilteredAdmissionsByTimeOfDay = (
  admissions: WelfareAdmission[], 
  filterState: AdmissionsFilterState
): TimeOfDayData[] => {
  const filteredAdmissions = applyFiltersToAdmissions(admissions, filterState);
  return getAdmissionsByTimeOfDay(filteredAdmissions);
};

export const getFilteredAdmissionsByReason = (
  admissions: WelfareAdmission[], 
  filterState: AdmissionsFilterState
): ChartData[] => {
  const filteredAdmissions = applyFiltersToAdmissions(admissions, filterState);
  return getAdmissionsByReason(filteredAdmissions);
};

export const getFilteredAgeDistribution = (
  admissions: WelfareAdmission[], 
  filterState: AdmissionsFilterState
): ChartData[] => {
  const filteredAdmissions = applyFiltersToAdmissions(admissions, filterState);
  return getAgeDistribution(filteredAdmissions);
};

// Front of House data processing
export const getItemCountsByTimeOfDay = (itemCounts: ItemDocument[]): TimeOfDayData[] => {
  const hourlyData = Array.from({ length: 24 }, (_, i) => ({
    hour: i,
    count: 0,
    label: format(new Date().setHours(i, 0, 0, 0), 'ha')
  }));

  itemCounts.forEach(item => {
    if (!item.timestamp) return;
    const hour = getHours(parseISO(item.timestamp));
    const totalCount = ITEM_TYPES.reduce((sum, type) => sum + (item[type] || 0), 0);
    const index = hourlyData.findIndex(d => d.hour === hour);
    if (index !== -1) {
      hourlyData[index].count += totalCount;
    }
  });

  return hourlyData;
};

export const getItemCountsByType = (itemCounts: ItemDocument[]): ChartData[] => {
  const grouped = itemCounts.reduce((acc: Record<string, number>, item) => {
    ITEM_TYPES.forEach(type => {
      acc[type] = (acc[type] || 0) + (item[type] || 0);
    });
    return acc;
  }, {} as Record<string, number>);

  return Object.entries(grouped)
    .map(([type, count]) => ({
      type,
      count,
    }))
    .sort((a, b) => b.count - a.count);
};

// Lost Property data processing
export const getLostPropertyByTimeOfDay = (items: LostPropertyItem[]): TimeOfDayData[] => {
  const hourlyData = Array.from({ length: 24 }, (_, i) => ({
    hour: i,
    count: 0,
    label: format(new Date().setHours(i, 0, 0, 0), 'ha')
  }));

  items.forEach(item => {
    if (!item.timeFound) return;
    const hour = getHours(parseISO(item.timeFound));
    const index = hourlyData.findIndex(d => d.hour === hour);
    if (index !== -1) {
      hourlyData[index].count++;
    }
  });

  return hourlyData;
};

export const getLostPropertyByCategory = (items: LostPropertyItem[]): ChartData[] => {
  const grouped = items.reduce((acc: Record<string, number>, item) => {
    acc[item.category] = (acc[item.category] || 0) + 1;
    return acc;
  }, {});

  return Object.entries(grouped)
    .map(([category, count]) => ({
      category,
      count,
    }))
    .sort((a, b) => b.count - a.count);
};

export const getLostPropertyByStatus = (items: LostPropertyItem[]): ChartData[] => {
  const grouped = items.reduce((acc: Record<string, number>, item) => {
    const status = item.itemReturned ? 'Returned' : 'Pending';
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {});

  return Object.entries(grouped).map(([status, count]) => ({
    status,
    count,
  }));
};

// Sensory Hub data processing
export const getSensoryHubVisitsByTimeOfDay = (visits: SensoryHubVisit[]): TimeOfDayData[] => {
  const hourlyData = Array.from({ length: 24 }, (_, i) => ({
    hour: i,
    count: 0,
    label: format(new Date().setHours(i, 0, 0, 0), 'ha')
  }));

  visits.forEach(visit => {
    if (!visit.visitTimestamp) return;
    const hour = getHours(parseISO(visit.visitTimestamp));
    const index = hourlyData.findIndex(d => d.hour === hour);
    if (index !== -1) {
      hourlyData[index].count++;
    }
  });

  return hourlyData;
};

export const getSensoryHubVisitsByPurpose = (visits: SensoryHubVisit[]): ChartData[] => {
  const grouped = visits.reduce((acc: Record<string, number>, visit) => {
    const purpose = visit.purpose === 'look_around' ? 'Look Around' : 'Use Service';
    acc[purpose] = (acc[purpose] || 0) + 1;
    return acc;
  }, {});

  return Object.entries(grouped).map(([purpose, count]) => ({
    purpose,
    count,
  }));
};

export const getSensoryHubVisitsByUserType = (visits: SensoryHubVisit[]): ChartData[] => {
  const grouped = visits.reduce((acc: Record<string, number>, visit) => {
    const userType = visit.userType === 'crew' ? 'Crew' : 'Public';
    acc[userType] = (acc[userType] || 0) + 1;
    return acc;
  }, {});

  return Object.entries(grouped).map(([userType, count]) => ({
    userType,
    count,
  }));
};

export const getSensoryHubVisitsByTeam = (visits: SensoryHubVisit[]): ChartData[] => {
  const crewVisits = visits.filter(visit => visit.userType === 'crew' && visit.teamName);
  const grouped = crewVisits.reduce((acc: Record<string, number>, visit) => {
    const teamName = visit.teamName || 'Unknown';
    acc[teamName] = (acc[teamName] || 0) + 1;
    return acc;
  }, {});

  return Object.entries(grouped)
    .map(([teamName, count]) => ({
      teamName,
      count,
    }))
    .sort((a, b) => b.count - a.count);
};
