import { FilterType, ChartData, TimeOfDayData } from '../types/filters';
import { WelfareAdmission } from '../types/admission';

/**
 * Chart interaction utilities for handling long press and click events
 * 
 * This module provides factory functions and utilities for creating
 * consistent chart interaction handlers across different chart types.
 */

/**
 * Configuration for chart click handlers
 */
export interface ChartClickHandlerConfig {
  filterType: FilterType;
  onFilterSelect: (filterType: FilterType, value: string) => void;
  onLongPress: (filterType: FilterType, value: string, data: WelfareAdmission[]) => void;
  getFilteredData: (filterValue: string) => WelfareAdmission[];
}

/**
 * Recharts event data structure
 */
export interface RechartsEventData {
  activeLabel?: string;
  activePayload?: Array<{
    payload: ChartData | TimeOfDayData;
    dataKey: string;
    value: number;
  }>;
  chartX?: number;
  chartY?: number;
}

/**
 * Creates a standardized click handler for chart components
 * 
 * @param config Configuration object for the click handler
 * @returns Function that handles chart click events
 */
export const createChartClickHandler = (config: ChartClickHandlerConfig) => {
  return (data: RechartsEventData, event: React.MouseEvent) => {
    const filterValue = extractChartDataFromEvent(data, config.filterType);
    
    if (filterValue) {
      // For regular clicks, just apply the filter
      config.onFilterSelect(config.filterType, filterValue);
    }
  };
};

/**
 * Creates a standardized long press handler for chart components
 * 
 * @param config Configuration object for the long press handler
 * @returns Function that handles chart long press events
 */
export const createChartLongPressHandler = (config: ChartClickHandlerConfig) => {
  return (data: RechartsEventData, event: React.MouseEvent | React.TouchEvent) => {
    const filterValue = extractChartDataFromEvent(data, config.filterType);
    
    if (filterValue) {
      // For long press, get the filtered data and show modal
      const filteredData = config.getFilteredData(filterValue);
      config.onLongPress(config.filterType, filterValue, filteredData);
    }
  };
};

/**
 * Extracts the relevant filter value from Recharts event data
 * 
 * @param data Event data from Recharts
 * @param filterType Type of filter to extract
 * @returns The filter value or null if not found
 */
export const extractChartDataFromEvent = (
  data: RechartsEventData,
  filterType: FilterType
): string | null => {
  if (!data.activePayload || data.activePayload.length === 0) {
    return null;
  }

  const payload = data.activePayload[0].payload;

  switch (filterType) {
    case FilterType.TIME:
      // For time charts, use the label (hour range)
      if ('label' in payload) {
        return payload.label as string;
      }
      // Fallback to activeLabel for time data
      return data.activeLabel || null;

    case FilterType.REASON:
      // For reason charts, look for reason field
      if ('reason' in payload) {
        return payload.reason as string;
      }
      // Fallback to activeLabel
      return data.activeLabel || null;

    case FilterType.AGE:
      // For age charts, look for ageGroup field
      if ('ageGroup' in payload) {
        return payload.ageGroup as string;
      }
      // Fallback to activeLabel
      return data.activeLabel || null;

    default:
      return data.activeLabel || null;
  }
};

/**
 * Formats a modal title based on filter type and value
 * 
 * @param filterType Type of filter
 * @param filterValue Value of the filter
 * @param count Number of records
 * @returns Formatted title string
 */
export const formatModalTitle = (
  filterType: FilterType,
  filterValue: string,
  count: number
): string => {
  const recordText = count === 1 ? 'record' : 'records';
  
  switch (filterType) {
    case FilterType.TIME:
      return `${count} ${recordText} admitted during ${filterValue}`;
    
    case FilterType.REASON:
      return `${count} ${recordText} with reason: ${filterValue}`;
    
    case FilterType.AGE:
      return `${count} ${recordText} in age group: ${filterValue}`;
    
    default:
      return `${count} ${recordText} for ${filterValue}`;
  }
};

/**
 * Extracts filter value from chart data for filtering operations
 * 
 * @param chartData Chart data item
 * @param filterType Type of filter to extract
 * @returns Filter value or null
 */
export const getFilterValueFromChartData = (
  chartData: ChartData | TimeOfDayData,
  filterType: FilterType
): string | null => {
  switch (filterType) {
    case FilterType.TIME:
      if ('label' in chartData) {
        return typeof chartData.label === 'string' ? chartData.label : String(chartData.label);
      }
      if ('hour' in chartData) {
        // Convert hour to time range format
        const hour = typeof chartData.hour === 'number' ? chartData.hour : parseInt(String(chartData.hour));
        const nextHour = hour + 1;
        return `${hour.toString().padStart(2, '0')}:00-${nextHour.toString().padStart(2, '0')}:00`;
      }
      return null;

    case FilterType.REASON:
      return 'reason' in chartData ? String(chartData.reason) : null;

    case FilterType.AGE:
      return 'ageGroup' in chartData ? String(chartData.ageGroup) : null;

    default:
      return null;
  }
};

/**
 * Determines if a chart data point should be highlighted based on active filters
 * 
 * @param chartData Chart data item
 * @param filterType Type of filter
 * @param activeFilters Array of active filter values
 * @returns Whether the data point should be highlighted
 */
export const shouldHighlightChartData = (
  chartData: ChartData | TimeOfDayData,
  filterType: FilterType,
  activeFilters: string[]
): boolean => {
  const filterValue = getFilterValueFromChartData(chartData, filterType);
  return filterValue ? activeFilters.includes(filterValue) : false;
};

/**
 * Creates a data accessor function for chart components
 * 
 * @param filterType Type of filter
 * @returns Function that extracts the appropriate data key
 */
export const createDataKeyAccessor = (filterType: FilterType) => {
  switch (filterType) {
    case FilterType.TIME:
      return 'label';
    case FilterType.REASON:
      return 'reason';
    case FilterType.AGE:
      return 'ageGroup';
    default:
      return 'label';
  }
};

/**
 * Validates chart event data to ensure it contains required information
 * 
 * @param data Event data from chart
 * @param filterType Expected filter type
 * @returns Whether the data is valid for processing
 */
export const validateChartEventData = (
  data: RechartsEventData,
  filterType: FilterType
): boolean => {
  if (!data.activePayload || data.activePayload.length === 0) {
    return false;
  }

  const filterValue = extractChartDataFromEvent(data, filterType);
  return filterValue !== null && filterValue.trim() !== '';
};

/**
 * Creates a debounced version of a chart interaction handler
 * 
 * @param handler Original handler function
 * @param delay Debounce delay in milliseconds
 * @returns Debounced handler function
 */
export const debounceChartHandler = <T extends (...args: any[]) => void>(
  handler: T,
  delay: number = 100
): T => {
  let timeoutId: number | null = null;
  
  return ((...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    timeoutId = window.setTimeout(() => {
      handler(...args);
    }, delay);
  }) as T;
};