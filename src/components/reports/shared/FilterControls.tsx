import React from 'react';
import {
  Box,
  Chip,
  FormControl,
  FormControlLabel,
  Switch,
  Typography,
  Button,
  Stack,
  Divider
} from '@mui/material';
import { Clear as ClearIcon, FilterList as FilterIcon } from '@mui/icons-material';
import { AdmissionsFilterState, FilterType } from '../../../types/filters';
import { formatFilterDisplayText } from '../../../utils/admissionsFilterUtils';

interface FilterControlsProps {
  filterState: AdmissionsFilterState;
  onAddFilter: (type: FilterType, value: string) => void;
  onRemoveFilter: (type: FilterType, value: string) => void;
  onClearAllFilters: () => void;
  onToggleMode: () => void;
}

export const FilterControls: React.FC<FilterControlsProps> = ({
  filterState,
  onAddFilter,
  onRemoveFilter,
  onClearAllFilters,
  onToggleMode
}) => {
  const hasActiveFilters =
    filterState.timeFilters.length > 0 ||
    filterState.reasonFilters.length > 0 ||
    filterState.ageFilters.length > 0;


  const getAllActiveFilters = (): Array<{ type: FilterType; value: string }> => {
    const allFilters: Array<{ type: FilterType; value: string }> = [];
    
    filterState.timeFilters.forEach(value => {
      allFilters.push({ type: FilterType.TIME, value });
    });
    
    filterState.reasonFilters.forEach(value => {
      allFilters.push({ type: FilterType.REASON, value });
    });
    
    filterState.ageFilters.forEach(value => {
      allFilters.push({ type: FilterType.AGE, value });
    });
    
    return allFilters;
  };

  const getFilterColor = (type: FilterType): 'primary' | 'secondary' | 'success' => {
    switch (type) {
      case FilterType.TIME:
        return 'primary';
      case FilterType.REASON:
        return 'secondary';
      case FilterType.AGE:
        return 'success';
      default:
        return 'primary';
    }
  };

  const getFilterTypeLabel = (type: FilterType): string => {
    switch (type) {
      case FilterType.TIME:
        return 'Time';
      case FilterType.REASON:
        return 'Reason';
      case FilterType.AGE:
        return 'Age';
      default:
        return type;
    }
  };

  return (
    <Box sx={{ mb: 3 }}>
      {/* Filter Mode Toggle */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <FilterIcon color="action" />
          <Typography variant="h6" component="h3">
            Filters
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <FormControl>
            <FormControlLabel
              control={
                <Switch
                  checked={filterState.filterMode === 'OR'}
                  onChange={onToggleMode}
                  color="primary"
                />
              }
              label={
                <Typography variant="body2">
                  {filterState.filterMode === 'AND' ? 'Match All (AND)' : 'Match Any (OR)'}
                </Typography>
              }
            />
          </FormControl>
          
          {hasActiveFilters && (
            <Button
              variant="outlined"
              size="small"
              startIcon={<ClearIcon />}
              onClick={onClearAllFilters}
              sx={{ minWidth: 'auto' }}
            >
              Clear All
            </Button>
          )}
        </Box>
      </Box>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <>
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              Active Filters ({filterState.filterMode} logic):
            </Typography>
            
            <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
              {getAllActiveFilters().map(({ type, value }) => (
                <Chip
                  key={`${type}-${value}`}
                  label={`${getFilterTypeLabel(type)}: ${formatFilterDisplayText(type, value)}`}
                  color={getFilterColor(type)}
                  variant="filled"
                  size="small"
                  onDelete={() => onRemoveFilter(type, value)}
                  deleteIcon={<ClearIcon />}
                  sx={{
                    '& .MuiChip-deleteIcon': {
                      fontSize: '16px'
                    }
                  }}
                />
              ))}
            </Stack>
          </Box>
          
          <Divider sx={{ mb: 2 }} />
        </>
      )}

      {/* Filter Instructions */}
      {!hasActiveFilters && (
        <Box sx={{ 
          p: 2, 
          bgcolor: 'grey.50', 
          borderRadius: 1,
          border: '1px dashed',
          borderColor: 'grey.300'
        }}>
          <Typography variant="body2" color="text.secondary" align="center">
            Click on chart elements to add filters. Use long press for detailed information.
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default FilterControls;