import React from 'react';
import {
  Box,
  CircularProgress,
  Fade,
  useTheme,
  alpha
} from '@mui/material';

interface LongPressIndicatorProps {
  /** Whether the indicator should be visible */
  isVisible: boolean;
  /** Size of the indicator in pixels */
  size?: number;
  /** Color of the progress indicator */
  color?: 'primary' | 'secondary' | 'inherit';
  /** Position of the indicator relative to its container */
  position?: {
    top?: number | string;
    left?: number | string;
    right?: number | string;
    bottom?: number | string;
  };
  /** Duration of the long press in milliseconds (for progress calculation) */
  duration?: number;
  /** Custom z-index for the indicator */
  zIndex?: number;
}

/**
 * Visual feedback component for long press interactions
 * 
 * Features:
 * - Circular progress indicator with smooth Material-UI transitions
 * - Configurable positioning and appearance
 * - Overlay component that doesn't interfere with chart interactions
 * - Accessible design with proper contrast
 * - Performance optimized with minimal re-renders
 */
export const LongPressIndicator: React.FC<LongPressIndicatorProps> = ({
  isVisible,
  size = 40,
  color = 'primary',
  position = { top: '50%', left: '50%' },
  duration = 500,
  zIndex = 1000
}) => {
  const theme = useTheme();

  // Calculate transform to center the indicator
  const transform = React.useMemo(() => {
    const translateX = position.left === '50%' ? '-50%' : '0';
    const translateY = position.top === '50%' ? '-50%' : '0';
    return `translate(${translateX}, ${translateY})`;
  }, [position.left, position.top]);

  // Get theme color for the indicator
  const indicatorColor = React.useMemo(() => {
    switch (color) {
      case 'primary':
        return theme.palette.primary.main;
      case 'secondary':
        return theme.palette.secondary.main;
      case 'inherit':
      default:
        return theme.palette.text.primary;
    }
  }, [color, theme.palette]);

  return (
    <Fade
      in={isVisible}
      timeout={{
        enter: 150,
        exit: 300
      }}
      unmountOnExit
    >
      <Box
        sx={{
          position: 'absolute',
          top: position.top,
          left: position.left,
          right: position.right,
          bottom: position.bottom,
          transform,
          zIndex,
          pointerEvents: 'none', // Don't interfere with chart interactions
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: size,
          height: size,
          borderRadius: '50%',
          backgroundColor: alpha(theme.palette.background.paper, 0.9),
          boxShadow: theme.shadows[4],
          border: `2px solid ${alpha(indicatorColor, 0.2)}`,
        }}
      >
        <CircularProgress
          size={size - 12}
          thickness={4}
          sx={{
            color: indicatorColor,
            '& .MuiCircularProgress-circle': {
              strokeLinecap: 'round',
            },
          }}
          variant="indeterminate"
        />
        
        {/* Inner dot for visual clarity */}
        <Box
          sx={{
            position: 'absolute',
            width: 6,
            height: 6,
            borderRadius: '50%',
            backgroundColor: indicatorColor,
            opacity: 0.8,
          }}
        />
      </Box>
    </Fade>
  );
};

export default LongPressIndicator;