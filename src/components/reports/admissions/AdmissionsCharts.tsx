import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
} from 'recharts';
import {
  Paper,
  Typography,
  Grid,
  Box
} from '@mui/material';
import { WelfareAdmission } from '../../../types/admission';
import {
  COLORS,
  getAdmissionsByTimeOfDay,
  getAdmissionsByReason,
  getAgeDistribution
} from '../../../utils/reportDataProcessing';
import { FilterType, AdmissionsFilterState, FilterActions, FilteredData } from '../../../types/filters';
import { useLongPress } from '../../../hooks/useLongPress';
import { LongPressIndicator } from '../shared/LongPressIndicator';
import { 
  createChartClickHandler,
  createChartLongPressHandler,
  extractChartDataFromEvent
} from '../../../utils/chartInteractionUtils';

interface AdmissionsChartsProps {
  admissions: WelfareAdmission[];
  filterState: AdmissionsFilterState;
  filterActions: FilterActions;
  filteredData: FilteredData;
  onLongPressModal: (data: WelfareAdmission[], filterValue: string, filterType: FilterType) => void;
}

export const AdmissionsCharts: React.FC<AdmissionsChartsProps> = ({ 
  admissions, 
  filterState, 
  filterActions, 
  filteredData, 
  onLongPressModal 
}) => {
  // Create chart interaction handlers for time chart
  const timeChartConfig = {
    filterType: FilterType.TIME,
    onFilterSelect: (filterType: FilterType, value: string) => {
      filterActions.addFilter(filterType, value);
    },
    onLongPress: (filterType: FilterType, value: string, data: WelfareAdmission[]) => {
      onLongPressModal(data, value, filterType);
    },
    getFilteredData: (filterValue: string) => {
      // Filter admissions for the selected time period
      return admissions.filter(admission => {
        if (!admission.Attended) return false;
        const hour = new Date(admission.Attended).getHours();
        const timeLabel = `${hour.toString().padStart(2, '0')}:00-${(hour + 1).toString().padStart(2, '0')}:00`;
        return timeLabel === filterValue;
      });
    }
  };

  const reasonChartConfig = {
    filterType: FilterType.REASON,
    onFilterSelect: (filterType: FilterType, value: string) => {
      filterActions.addFilter(filterType, value);
    },
    onLongPress: (filterType: FilterType, value: string, data: WelfareAdmission[]) => {
      onLongPressModal(data, value, filterType);
    },
    getFilteredData: (filterValue: string) => {
      return admissions.filter(admission => admission.ReasonCategory === filterValue);
    }
  };

  const ageChartConfig = {
    filterType: FilterType.AGE,
    onFilterSelect: (filterType: FilterType, value: string) => {
      filterActions.addFilter(filterType, value);
    },
    onLongPress: (filterType: FilterType, value: string, data: WelfareAdmission[]) => {
      onLongPressModal(data, value, filterType);
    },
    getFilteredData: (filterValue: string) => {
      return admissions.filter(admission => {
        if (!admission.Age) return false;
        const age = admission.Age;
        // Parse age group ranges like "18-25", "26-35", etc.
        const [min, max] = filterValue.split('-').map(Number);
        return age >= min && age <= max;
      });
    }
  };

  // Long press hooks for each chart
  const timeLongPress = useLongPress({
    onLongPress: (event: React.MouseEvent | React.TouchEvent) => {
      // For long press, we need to extract data from the event target
      // This is a simplified approach - in a real implementation you'd need
      // to track which chart element was pressed
      const filteredData = timeChartConfig.getFilteredData('');
      onLongPressModal(filteredData, '', FilterType.TIME);
    },
    threshold: 500
  });

  const reasonLongPress = useLongPress({
    onLongPress: (event: React.MouseEvent | React.TouchEvent) => {
      const filteredData = reasonChartConfig.getFilteredData('');
      onLongPressModal(filteredData, '', FilterType.REASON);
    },
    threshold: 500
  });

  const ageLongPress = useLongPress({
    onLongPress: (event: React.MouseEvent | React.TouchEvent) => {
      const filteredData = ageChartConfig.getFilteredData('');
      onLongPressModal(filteredData, '', FilterType.AGE);
    },
    threshold: 500
  });

  // Click handlers
  const handleTimeClick = createChartClickHandler(timeChartConfig);
  const handleReasonClick = createChartClickHandler(reasonChartConfig);
  const handleAgeClick = createChartClickHandler(ageChartConfig);

  return (
    <Grid container spacing={3}>
      {/* Admissions by Time of Day */}
      <Grid size={{ xs: 12 }}>
        <Paper
          elevation={3}
          sx={{
            p: 3,
            bgcolor: 'background.paper',
            backdropFilter: 'blur(8px)',
            borderRadius: 2,
            position: 'relative'
          }}
        >
          <Typography variant="h6" color="primary" gutterBottom>
            Admissions by Time of Day
          </Typography>
          <div
            style={{ height: 300, width: '100%' }}
            onMouseDown={timeLongPress.onMouseDown}
            onMouseUp={timeLongPress.onMouseUp}
            onMouseLeave={timeLongPress.onMouseLeave}
            onTouchStart={timeLongPress.onTouchStart}
            onTouchEnd={timeLongPress.onTouchEnd}
            onTouchMove={timeLongPress.onTouchMove}
          >
            <ResponsiveContainer>
              <BarChart
                data={getAdmissionsByTimeOfDay(filteredData.filteredAdmissions)}
                onClick={handleTimeClick}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="label" interval={2} />
                <YAxis />
                <Tooltip formatter={(value: number) => [value, 'Admissions']} />
                <Bar
                  dataKey="count"
                  fill="#662D91"
                  name="Admissions"
                  cursor="pointer"
                >
                  {getAdmissionsByTimeOfDay(filteredData.filteredAdmissions).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.count > 0 ? '#662D91' : '#E0E0E0'} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
          {timeLongPress.isLongPressing && (
            <LongPressIndicator 
              isVisible={timeLongPress.isLongPressing}
              position={{ top: '50%', left: '50%' }}
            />
          )}
        </Paper>
      </Grid>

      {/* Admissions by Reason */}
      <Grid size={{ xs: 12, md: 6 }}>
        <Paper
          elevation={3}
          sx={{
            p: 3,
            bgcolor: 'background.paper',
            backdropFilter: 'blur(8px)',
            borderRadius: 2,
            height: '100%',
            position: 'relative'
          }}
        >
          <Typography variant="h6" color="primary" gutterBottom>
            Admissions by Reason
          </Typography>
          <div
            style={{ height: 300, width: '100%' }}
            onMouseDown={reasonLongPress.onMouseDown}
            onMouseUp={reasonLongPress.onMouseUp}
            onMouseLeave={reasonLongPress.onMouseLeave}
            onTouchStart={reasonLongPress.onTouchStart}
            onTouchEnd={reasonLongPress.onTouchEnd}
            onTouchMove={reasonLongPress.onTouchMove}
          >
            <ResponsiveContainer>
              <BarChart
                data={getAdmissionsByReason(filteredData.filteredAdmissions)}
                onClick={handleReasonClick}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="reason" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="count" name="Count" cursor="pointer">
                  {getAdmissionsByReason(filteredData.filteredAdmissions).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
          {reasonLongPress.isLongPressing && (
            <LongPressIndicator 
              isVisible={reasonLongPress.isLongPressing}
              position={{ top: '50%', left: '50%' }}
            />
          )}
        </Paper>
      </Grid>

      {/* Age Distribution */}
      <Grid size={{ xs: 12, md: 6 }}>
        <Paper
          elevation={3}
          sx={{
            p: 3,
            bgcolor: 'background.paper',
            backdropFilter: 'blur(8px)',
            borderRadius: 2,
            height: '100%',
            position: 'relative'
          }}
        >
          <Typography variant="h6" color="primary" gutterBottom>
            Age Distribution
          </Typography>
          <div
            style={{ height: 300, width: '100%' }}
            onMouseDown={ageLongPress.onMouseDown}
            onMouseUp={ageLongPress.onMouseUp}
            onMouseLeave={ageLongPress.onMouseLeave}
            onTouchStart={ageLongPress.onTouchStart}
            onTouchEnd={ageLongPress.onTouchEnd}
            onTouchMove={ageLongPress.onTouchMove}
          >
            <ResponsiveContainer>
              <PieChart
                onClick={handleAgeClick}
              >
                <Pie
                  data={getAgeDistribution(filteredData.filteredAdmissions)}
                  dataKey="count"
                  nameKey="ageGroup"
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  label
                  cursor="pointer"
                >
                  {getAgeDistribution(filteredData.filteredAdmissions).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
          {ageLongPress.isLongPressing && (
            <LongPressIndicator 
              isVisible={ageLongPress.isLongPressing}
              position={{ top: '50%', left: '50%' }}
            />
          )}
        </Paper>
      </Grid>
    </Grid>
  );
};
