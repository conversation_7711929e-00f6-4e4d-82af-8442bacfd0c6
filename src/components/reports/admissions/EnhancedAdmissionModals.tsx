import React from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Box,
  IconButton,
  useTheme,
  useMediaQuery,
  Divider
} from '@mui/material';
import {
  Close as CloseIcon,
  AccessTime as TimeIcon,
  Person as PersonIcon,
  Category as CategoryIcon
} from '@mui/icons-material';
import { WelfareAdmission } from '../../../types/admission';
import { FilterType } from '../../../types/filters';
import { formatModalTitle } from '../../../utils/chartInteractionUtils';

/**
 * Base props for all enhanced admission modals
 */
interface BaseModalProps {
  open: boolean;
  onClose: () => void;
  data: WelfareAdmission[];
  filterValue: string;
  filterType: FilterType;
}

/**
 * Props for the time range modal
 */
interface TimeRangeModalProps extends BaseModalProps {
  filterType: FilterType.TIME;
}

/**
 * Props for the reason modal
 */
interface ReasonModalProps extends BaseModalProps {
  filterType: FilterType.REASON;
}

/**
 * Props for the age group modal
 */
interface AgeGroupModalProps extends BaseModalProps {
  filterType: FilterType.AGE;
}

/**
 * Union type for all modal props
 */
type EnhancedModalProps = TimeRangeModalProps | ReasonModalProps | AgeGroupModalProps;

/**
 * Formats a date to a readable string
 */
const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

/**
 * Formats admission time to HH:MM format
 */
const formatTime = (date: Date): string => {
  return new Intl.DateTimeFormat('en-GB', {
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

/**
 * Calculates age from date of birth string or uses provided age
 */
const getAge = (admission: WelfareAdmission): number => {
  // Use provided age if available
  if (admission.Age && admission.Age > 0) {
    return admission.Age;
  }
  
  // Calculate from DOB if available
  if (admission.DOB) {
    const today = new Date();
    const birthDate = new Date(admission.DOB);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  }
  
  return 0; // Unknown age
};

/**
 * Gets the full name from FirstName and Surname
 */
const getFullName = (admission: WelfareAdmission): string => {
  return `${admission.FirstName} ${admission.Surname}`.trim();
};

/**
 * Parses the Attended field to get admission time
 */
const getAdmissionTime = (admission: WelfareAdmission): Date => {
  // The Attended field should contain the admission timestamp
  return new Date(admission.Attended);
};

/**
 * Gets the appropriate icon for the filter type
 */
const getFilterIcon = (filterType: FilterType) => {
  switch (filterType) {
    case FilterType.TIME:
      return <TimeIcon />;
    case FilterType.REASON:
      return <CategoryIcon />;
    case FilterType.AGE:
      return <PersonIcon />;
    default:
      return <CategoryIcon />;
  }
};

/**
 * Gets the appropriate color for the filter type
 */
const getFilterColor = (filterType: FilterType): 'primary' | 'secondary' | 'default' => {
  switch (filterType) {
    case FilterType.TIME:
      return 'primary';
    case FilterType.REASON:
      return 'secondary';
    case FilterType.AGE:
      return 'default';
    default:
      return 'default';
  }
};

/**
 * Enhanced modal component for displaying filtered admission data
 * 
 * Features:
 * - Context-aware display based on filter type
 * - Responsive design with mobile optimization
 * - Accessible table with proper headers
 * - Consistent Material-UI styling
 * - Performance optimized for large datasets
 */
export const EnhancedAdmissionModal: React.FC<EnhancedModalProps> = ({
  open,
  onClose,
  data,
  filterValue,
  filterType
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const modalTitle = formatModalTitle(filterType, filterValue, data.length);
  const filterIcon = getFilterIcon(filterType);
  const filterColor = getFilterColor(filterType);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      fullScreen={isMobile}
      PaperProps={{
        sx: {
          minHeight: isMobile ? '100vh' : '60vh',
          maxHeight: isMobile ? '100vh' : '90vh',
        }
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          pb: 1
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {filterIcon}
          <Typography variant="h6" component="div">
            {modalTitle}
          </Typography>
        </Box>
        
        <IconButton
          onClick={onClose}
          size="small"
          aria-label="Close modal"
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ p: 2, pb: 1 }}>
          <Chip
            icon={filterIcon}
            label={`Filter: ${filterValue}`}
            color={filterColor}
            variant="outlined"
            size="small"
          />
        </Box>

        <TableContainer
          component={Paper}
          elevation={0}
          sx={{
            maxHeight: isMobile ? 'calc(100vh - 200px)' : '60vh',
            '& .MuiTableCell-root': {
              borderBottom: `1px solid ${theme.palette.divider}`,
            }
          }}
        >
          <Table stickyHeader aria-label="Filtered admissions table">
            <TableHead>
              <TableRow>
                <TableCell>
                  <Typography variant="subtitle2" fontWeight="bold">
                    Name
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="subtitle2" fontWeight="bold">
                    Age
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="subtitle2" fontWeight="bold">
                    Admission Time
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="subtitle2" fontWeight="bold">
                    Reason
                  </Typography>
                </TableCell>
                {!isMobile && (
                  <TableCell>
                    <Typography variant="subtitle2" fontWeight="bold">
                      Date
                    </Typography>
                  </TableCell>
                )}
              </TableRow>
            </TableHead>
            <TableBody>
              {data.map((admission) => (
                <TableRow
                  key={admission._id}
                  hover
                  sx={{
                    '&:nth-of-type(odd)': {
                      backgroundColor: theme.palette.action.hover,
                    },
                  }}
                >
                  <TableCell>
                    <Typography variant="body2">
                      {getFullName(admission)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {getAge(admission)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {formatTime(getAdmissionTime(admission))}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={admission.ReasonCategory}
                      size="small"
                      variant="outlined"
                      sx={{
                        fontSize: '0.75rem',
                        height: '24px',
                      }}
                    />
                  </TableCell>
                  {!isMobile && (
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {formatDate(getAdmissionTime(admission))}
                      </Typography>
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {data.length === 0 && (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              minHeight: '200px',
              textAlign: 'center',
              p: 3
            }}
          >
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No admissions found
            </Typography>
            <Typography variant="body2" color="text.secondary">
              There are no admission records matching the selected filter criteria.
            </Typography>
          </Box>
        )}
      </DialogContent>

      <Divider />

      <DialogActions sx={{ p: 2 }}>
        <Typography variant="body2" color="text.secondary" sx={{ flexGrow: 1 }}>
          Showing {data.length} {data.length === 1 ? 'record' : 'records'}
        </Typography>
        <Button onClick={onClose} variant="contained">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

/**
 * Time Range Modal - Shows patients admitted in a specific time range
 */
export const TimeRangeModal: React.FC<Omit<TimeRangeModalProps, 'filterType'>> = (props) => (
  <EnhancedAdmissionModal {...props} filterType={FilterType.TIME} />
);

/**
 * Reason Modal - Shows patients with a specific admission reason
 */
export const ReasonModal: React.FC<Omit<ReasonModalProps, 'filterType'>> = (props) => (
  <EnhancedAdmissionModal {...props} filterType={FilterType.REASON} />
);

/**
 * Age Group Modal - Shows patients in a specific age group
 */
export const AgeGroupModal: React.FC<Omit<AgeGroupModalProps, 'filterType'>> = (props) => (
  <EnhancedAdmissionModal {...props} filterType={FilterType.AGE} />
);

export default EnhancedAdmissionModal;