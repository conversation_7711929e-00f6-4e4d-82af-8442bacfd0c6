import React, { useEffect } from 'react';
import {
  Grid,
  TextField,
  Alert,
  MenuItem,
  Typography,
  Divider,
  SelectChangeEvent,
  Select,
  FormControl,
  InputLabel,
} from '@mui/material';
import { Section, SectionTitle } from './StyledComponents';
import { Gender, Ethnicity } from '../../types/admission';

interface PersonalInfoSectionProps {
  firstName: string;
  surname: string;
  dob: string;
  age: number | undefined;
  gender: Gender;
  pronoun: string;
  ethnicity: Ethnicity;
  contactName: string;
  contactNumber: string;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSelectChange: (e: SelectChangeEvent<unknown>) => void;
  onAgeChange: (age: number | undefined) => void;
}

export const PersonalInfoSection: React.FC<PersonalInfoSectionProps> = ({
  firstName,
  surname,
  dob,
  age,
  gender,
  pronoun,
  ethnicity,
  contactName,
  contactNumber,
  onInputChange,
  onSelectChange,
  onAgeChange,
}) => {
  // Calculate age whenever DOB changes
  useEffect(() => {
    if (dob) {
      const birthDate = new Date(dob);
      const today = new Date();
      let calculatedAge = today.getFullYear() - birthDate.getFullYear();
      const m = today.getMonth() - birthDate.getMonth();
      if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
        calculatedAge--;
      }
      onAgeChange(calculatedAge);
    }
  }, [dob, onAgeChange]);

  const handleAgeInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newAge = e.target.value ? Number(e.target.value) : undefined;
    onAgeChange(newAge);
  };

  return (
    <Section>
      <SectionTitle variant="h6">Personal Information</SectionTitle>
      <Grid container spacing={3}>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <TextField
            fullWidth
            required
            label="First Name"
            name="FirstName"
            value={firstName}
            onChange={onInputChange}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <TextField
            fullWidth
            label="Surname"
            name="Surname"
            value={surname}
            onChange={onInputChange}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <TextField
            fullWidth
            type="date"
            label="Date of Birth"
            name="DOB"
            value={dob}
            onChange={onInputChange}
            InputLabelProps={{ shrink: true }}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <TextField
            fullWidth
            type="number"
            label="Age"
            name="Age"
            value={age || ''}
            onChange={handleAgeInputChange}
          />
          {age !== undefined && age < 18 && (
            <Alert severity="warning" sx={{ mt: 1 }}>
              Person is under 18 years old
            </Alert>
          )}
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <FormControl fullWidth>
            <InputLabel id="gender-label">Gender</InputLabel>
            <Select
              labelId="gender-label"
              label="Gender"
              name="Gender"
              value={gender}
              onChange={onSelectChange}
            >
              <MenuItem value="Male">Male</MenuItem>
              <MenuItem value="Female">Female</MenuItem>
              <MenuItem value="Other">Other</MenuItem>
              <MenuItem value="Prefer not to say">Prefer not to say</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <TextField
            fullWidth
            label="Pronoun"
            name="Pronoun"
            value={pronoun}
            onChange={onInputChange}
            placeholder="e.g., he/him, she/her, they/them"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <FormControl fullWidth>
            <InputLabel id="ethnicity-label">Ethnicity</InputLabel>
            <Select
              labelId="ethnicity-label"
              label="Ethnicity"
              name="Ethnicity"
              value={ethnicity}
              onChange={onSelectChange}
            >
              <MenuItem value="White">White</MenuItem>
              <MenuItem value="Mixed">Mixed</MenuItem>
              <MenuItem value="Asian">Asian</MenuItem>
              <MenuItem value="Black">Black</MenuItem>
              <MenuItem value="Other">Other</MenuItem>
              <MenuItem value="Prefer not to say">Prefer not to say</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        {/* Contact Information Section */}
        <Grid size={{ xs: 12 }}>
          <Divider sx={{ my: 2 }} />
          <Typography variant="subtitle1" sx={{ mb: 2 }}>Contact Information</Typography>
          <Grid container spacing={3}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Contact Name"
                name="ContactName"
                value={contactName}
                onChange={onInputChange}
                placeholder="Name of friend/family member"
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Contact Number"
                name="ContactNumber"
                value={contactNumber}
                onChange={onInputChange}
                placeholder="e.g., 07700 900000"
              />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Section>
  );
};