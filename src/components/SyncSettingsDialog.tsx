import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  Dialog<PERSON>ctions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  Chip,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { databaseService } from '../services/database';

interface SyncSettingsDialogProps {
  open: boolean;
  onClose: () => void;
}

export const SyncSettingsDialog: React.FC<SyncSettingsDialogProps> = ({ open, onClose }) => {
  const [currentProfile, setCurrentProfile] = useState('');
  const [availableProfiles, setAvailableProfiles] = useState<Record<string, any>>({});
  const [persistentState, setPersistentState] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  useEffect(() => {
    if (open) {
      loadSyncConfig();
    }
  }, [open]);

  const loadSyncConfig = async () => {
    try {
      const config = databaseService.getSyncConfig();
      setCurrentProfile(config.profile);
      setAvailableProfiles(config.availableProfiles);
      setPersistentState(config.persistentState);
    } catch (error) {
      console.error('Failed to load sync config:', error);
      setMessage({ type: 'error', text: 'Failed to load sync configuration' });
    }
  };

  const handleProfileChange = async (profileName: string) => {
    setIsLoading(true);
    try {
      const success = databaseService.setSyncProfile(profileName);
      if (success) {
        setCurrentProfile(profileName);
        setMessage({ type: 'success', text: `Sync profile changed to ${profileName}` });
        await loadSyncConfig(); // Reload to get updated state
      } else {
        setMessage({ type: 'error', text: 'Failed to change sync profile' });
      }
    } catch (error) {
      console.error('Failed to change profile:', error);
      setMessage({ type: 'error', text: 'Failed to change sync profile' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleForceSync = async () => {
    setIsLoading(true);
    try {
      await databaseService.forceSync();
      setMessage({ type: 'success', text: 'Force sync completed successfully' });
      await loadSyncConfig(); // Reload to get updated state
    } catch (error) {
      console.error('Force sync failed:', error);
      setMessage({ type: 'error', text: 'Force sync failed' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPersistentState = () => {
    try {
      databaseService.resetPersistentState();
      setMessage({ type: 'success', text: 'Persistent state reset successfully' });
      loadSyncConfig(); // Reload to get updated state
    } catch (error) {
      console.error('Failed to reset persistent state:', error);
      setMessage({ type: 'error', text: 'Failed to reset persistent state' });
    }
  };

  const formatTimestamp = (timestamp: number) => {
    if (!timestamp) return 'Never';
    return new Date(timestamp).toLocaleString();
  };

  const formatDuration = (milliseconds: number) => {
    if (!milliseconds) return 'N/A';
    const minutes = Math.floor(milliseconds / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}d ${hours % 24}h`;
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    return `${minutes}m`;
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        Sync Settings
        <Typography variant="body2" color="text.secondary">
          Configure sync behavior and view persistent initialization state
        </Typography>
      </DialogTitle>
      
      <DialogContent>
        {message && (
          <Alert 
            severity={message.type} 
            onClose={() => setMessage(null)}
            sx={{ mb: 2 }}
          >
            {message.text}
          </Alert>
        )}

        {/* Sync Profile Selection */}
        <Box sx={{ mb: 3 }}>
          <FormControl fullWidth>
            <InputLabel>Sync Profile</InputLabel>
            <Select
              value={currentProfile}
              label="Sync Profile"
              onChange={(e) => handleProfileChange(e.target.value)}
              disabled={isLoading}
            >
              {Object.entries(availableProfiles).map(([key, profile]) => (
                <MenuItem key={key} value={key}>
                  <Box>
                    <Typography variant="body1">{profile.name}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {profile.description}
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Persistent State Information */}
        {persistentState && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Persistent Initialization State
            </Typography>
            
            <List dense>
              <ListItem>
                <ListItemText 
                  primary="Initialized" 
                  secondary={persistentState.isInitialized ? 'Yes' : 'No'}
                />
                <Chip 
                  label={persistentState.isInitialized ? 'Yes' : 'No'} 
                  color={persistentState.isInitialized ? 'success' : 'default'}
                  size="small"
                />
              </ListItem>
              
              <ListItem>
                <ListItemText 
                  primary="Has Local Data" 
                  secondary={persistentState.hasLocalData ? 'Yes' : 'No'}
                />
                <Chip 
                  label={persistentState.hasLocalData ? 'Yes' : 'No'} 
                  color={persistentState.hasLocalData ? 'success' : 'default'}
                  size="small"
                />
              </ListItem>
              
              <ListItem>
                <ListItemText 
                  primary="Last Sync" 
                  secondary={formatTimestamp(persistentState.lastSyncTime)}
                />
              </ListItem>
              
              <ListItem>
                <ListItemText 
                  primary="Last Successful Sync" 
                  secondary={formatTimestamp(persistentState.lastSuccessfulSync)}
                />
              </ListItem>
              
              <ListItem>
                <ListItemText 
                  primary="Page Load Count" 
                  secondary={`${persistentState.syncCount} loads since last sync`}
                />
              </ListItem>
              
              <ListItem>
                <ListItemText 
                  primary="Data Version" 
                  secondary={persistentState.dataVersion || 'Not set'}
                />
              </ListItem>
            </List>
          </Box>
        )}

        <Divider sx={{ my: 2 }} />

        {/* Actions */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="h6" gutterBottom>
            Actions
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleForceSync}
              disabled={isLoading}
            >
              Force Sync
            </Button>
            
            <Button
              variant="outlined"
              color="warning"
              startIcon={<DeleteIcon />}
              onClick={handleResetPersistentState}
              disabled={isLoading}
            >
              Reset Persistent State
            </Button>
            
            <Tooltip title="Refresh configuration from storage">
              <IconButton onClick={loadSyncConfig} disabled={isLoading}>
                <InfoIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Current Profile Info */}
        {availableProfiles[currentProfile] && (
          <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
            <Typography variant="subtitle2" gutterBottom>
              Current Profile: {availableProfiles[currentProfile].name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {availableProfiles[currentProfile].description}
            </Typography>
          </Box>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};
