import React, { useState, useEffect, useCallback } from 'react';
import { WelfareAdmission } from '../types/admission';
import { ItemDocument, LostPropertyItem } from '../types/item';
import { useFestival } from '../contexts/FestivalContext';
import { useSmartAdmissions, useSmartItems, useSmartLostProperty } from '../hooks/useSmartData';
import { parseISO } from 'date-fns';
import {
  Box,
  Grid,
  Paper,
  Typography,
  List,
  ListItem,
  CircularProgress,
  Alert,
  Button,
  Chip,
} from '@mui/material';
import WarningIcon from '@mui/icons-material/Warning';
import CachedIcon from '@mui/icons-material/Cached';
import SyncIcon from '@mui/icons-material/Sync';
import ErrorIcon from '@mui/icons-material/Error';
import SettingsIcon from '@mui/icons-material/Settings';
import { Tooltip } from '@mui/material';
import { databaseService } from '../services/database';
import { SyncSettingsDialog } from './SyncSettingsDialog';

interface DashboardTile {
  id: string;
  title: string;
  value: number;
  data: any[];
  alert?: boolean;
  subtitle?: string;
}

const ITEM_FIELDS = [
  'Suncream',
  'Poncho',
  'Water',
  'SanitaryProducts',
  'Earplugs',
  'Condoms',
  'ChildrensWristbands',
  'GeneralWristbands',
  'Charging',
  'Sanitizer',
  'ToiletRoll',
  'GeneralEnqs'
] as const;

type ItemField = typeof ITEM_FIELDS[number];

export const Dashboard: React.FC = () => {
  const { activeFestival } = useFestival();
  const [selectedTile, setSelectedTile] = useState<string | null>(null);
  const [dashboardTiles, setDashboardTiles] = useState<DashboardTile[]>([]);

  // Sync-related state
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncError, setSyncError] = useState<string | null>(null);
  const [syncSettingsOpen, setSyncSettingsOpen] = useState(false);
  const [hasPendingChanges, setHasPendingChanges] = useState(false);

  // Use smart data hooks for caching
  const admissionsResult = useSmartAdmissions({
    onError: (error) => console.error('Error loading admissions:', error)
  });
  
  const itemsResult = useSmartItems({
    onError: (error) => console.error('Error loading items:', error)
  });
  
  const lostPropertyResult = useSmartLostProperty({
    onError: (error) => console.error('Error loading lost property:', error)
  });

  // Listen for initial sync completion to refresh data
  useEffect(() => {
    const handleInitialSyncComplete = (event: CustomEvent) => {
      console.log('Dashboard: Initial sync completed, refreshing data...', event.detail);
      // Trigger a refresh of all data
      Promise.all([
        admissionsResult.refetch(),
        itemsResult.refetch(),
        lostPropertyResult.refetch()
      ]).catch(error => console.error('Error refreshing data after initial sync:', error));
    };

    window.addEventListener('initialSyncComplete', handleInitialSyncComplete as EventListener);
    
    return () => {
      window.removeEventListener('initialSyncComplete', handleInitialSyncComplete as EventListener);
    };
  }, [admissionsResult.refetch, itemsResult.refetch, lostPropertyResult.refetch]);

  // Derived state from smart data hooks
  const admissions = admissionsResult.data || [];
  const items = itemsResult.data || [];
  const lostPropertyItems = lostPropertyResult.data || [];
  
  const isLoading = admissionsResult.isLoading || itemsResult.isLoading || lostPropertyResult.isLoading;
  const error = admissionsResult.error || itemsResult.error || lostPropertyResult.error;
  const isFromCache = admissionsResult.isFromCache || itemsResult.isFromCache || lostPropertyResult.isFromCache;

  const handleRefresh = async () => {
    await Promise.all([
      admissionsResult.refetch(),
      itemsResult.refetch(),
      lostPropertyResult.refetch()
    ]);
  };

  // Sync functionality
  const handleSync = useCallback(async () => {
    if (isSyncing) return;

    try {
      setIsSyncing(true);
      setSyncError(null);

      // Use forceSync to bypass persistent initialization checks for manual sync
      await databaseService.forceSync();
      await handleRefresh(); // Refresh data after sync

      console.log('[DASHBOARD] Manual sync completed successfully');
    } catch (error) {
      console.error('Sync failed:', error);
      setSyncError('Failed to sync with server');
    } finally {
      setIsSyncing(false);
    }
  }, [isSyncing]);

  // Check for pending changes
  useEffect(() => {
    const checkPendingChanges = async () => {
      try {
        const pending = await databaseService.hasPendingChanges();
        setHasPendingChanges(pending);
      } catch (error) {
        console.error('Error checking pending changes:', error);
      }
    };

    checkPendingChanges();
    const interval = setInterval(checkPendingChanges, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Sync button helpers
  const getSyncButtonColor = () => {
    if (syncError) return 'error';
    if (hasPendingChanges) return 'warning';
    return 'primary';
  };

  const getSyncButtonIcon = () => {
    if (isSyncing) return <CachedIcon className="spin" />;
    if (syncError) return <ErrorIcon />;
    return <SyncIcon />;
  };

  const getSyncButtonText = () => {
    if (isSyncing) return 'Syncing...';
    if (syncError) return 'Sync Error';
    if (hasPendingChanges) return 'Sync Pending';
    return 'Sync Now';
  };

  const calculateAge = (dob: string): number | null => {
    try {
      const birthDate = new Date(dob);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      
      return age;
    } catch {
      return null;
    }
  };

  const isUnder18 = (admission: WelfareAdmission): boolean => {
    if (typeof admission.Age === 'number') {
      return admission.Age < 18;
    }
    if (admission.DOB) {
      const age = calculateAge(admission.DOB);
      return age !== null && age < 18;
    }
    return false;
  };

  const hasSubstanceIssues = (admission: WelfareAdmission): boolean => {
    return Array.isArray(admission.SubstanceUsed) && 
           admission.SubstanceUsed.length > 0 &&
           !admission.SubstanceUsed.includes('Nothing');
  };

  const hasSafeguardingIssues = (admission: WelfareAdmission): boolean => {
    return typeof admission.Safeguarding === 'string' && 
           admission.Safeguarding.trim() !== '';
  };

  const getUnder18WithSubstance = (admissions: WelfareAdmission[]): WelfareAdmission[] => {
    const filtered = admissions.filter(admission => {
      const isU18 = isUnder18(admission);
      const hasSubstance = hasSubstanceIssues(admission);
      console.log('Checking U18 substance:', {
        id: admission._id,
        age: admission.Age,
        dob: admission.DOB,
        isU18,
        substances: admission.SubstanceUsed,
        hasSubstance,
        inBayNow: admission.InBayNow
      });
      return isU18 && hasSubstance && admission.InBayNow;
    });
    console.log('U18 with substance:', filtered);
    return filtered;
  };

  const getUnder18WithSafeguarding = (admissions: WelfareAdmission[]): WelfareAdmission[] => {
    const filtered = admissions.filter(admission => {
      const isU18 = isUnder18(admission);
      const hasSafeguarding = hasSafeguardingIssues(admission);
      console.log('Checking U18 safeguarding:', {
        id: admission._id,
        age: admission.Age,
        dob: admission.DOB,
        isU18,
        safeguarding: admission.Safeguarding,
        hasSafeguarding,
        inBayNow: admission.InBayNow
      });
      return isU18 && hasSafeguarding && admission.InBayNow;
    });
    console.log('U18 with safeguarding:', filtered);
    return filtered;
  };

  const getSubstancesSummary = (admissions: WelfareAdmission[]): string => {
    const substances = new Set<string>();
    admissions.forEach(admission => {
      if (Array.isArray(admission.SubstanceUsed)) {
        admission.SubstanceUsed.forEach(substance => {
          if (substance !== 'Nothing') {
            substances.add(substance);
          }
        });
      }
    });
    return Array.from(substances).join(', ');
  };

  const getTotalItemsHandedOut = (items: ItemDocument[]): number => {
    return items.reduce((total, item) => {
      return total + ITEM_FIELDS.reduce((itemTotal, field) => {
        return itemTotal + (typeof item[field] === 'number' ? item[field] : 0);
      }, 0);
    }, 0);
  };

  const getDashboardTiles = (): DashboardTile[] => {
    if (!activeFestival) {
      return [];
    }

    console.log('Getting dashboard tiles for admissions:', admissions);

    const currentInBay = admissions.filter(a => a.InBayNow).length;
    const totalAdmissions = admissions.length;
    const under18Substance = getUnder18WithSubstance(admissions);
    const under18Safeguarding = getUnder18WithSafeguarding(admissions);
    const totalItemsHandedOut = getTotalItemsHandedOut(items);
    const currentLostPropertyItems = lostPropertyItems.filter(item => item.status === 'unclaimed').length;

    console.log('Dashboard stats:', {
      currentInBay,
      totalAdmissions,
      under18Substance: under18Substance.length,
      under18Safeguarding: under18Safeguarding.length
    });

    return [
      {
        id: 'current-in-bay',
        title: 'Currently In Bay',
        value: currentInBay,
        data: admissions.filter(a => a.InBayNow)
      },
      {
        id: 'total-admissions',
        title: 'Total Admissions',
        value: totalAdmissions,
        data: admissions
      },
      {
        id: 'under-18-substance',
        title: 'U18s with Substance Issues',
        value: under18Substance.length,
        data: under18Substance,
        alert: under18Substance.length > 0,
        subtitle: under18Substance.length > 0 ? `Substances: ${getSubstancesSummary(under18Substance)}` : undefined
      },
      {
        id: 'under-18-safeguarding',
        title: 'U18s with Safeguarding',
        value: under18Safeguarding.length,
        data: under18Safeguarding,
        alert: under18Safeguarding.length > 0
      },
      {
        id: 'total-items',
        title: 'Total Items Handed Out',
        value: totalItemsHandedOut,
        data: items
      },
      {
        id: 'lost-property',
        title: 'Current Lost Property Items',
        value: currentLostPropertyItems,
        data: lostPropertyItems.filter(item => item.status === 'unclaimed')
      }
    ];
  };

  // Memoize the dashboard tiles calculation to prevent infinite loops
  const memoizedDashboardTiles = useCallback(() => {
    return getDashboardTiles();
  }, [admissions.length, items.length, lostPropertyItems.length, activeFestival?._id]);

  useEffect(() => {
    const tiles = memoizedDashboardTiles();
    console.log('Setting dashboard tiles:', tiles);
    setDashboardTiles(tiles);
  }, [memoizedDashboardTiles]);

  const handleTileClick = async (tileId: string) => {
    setSelectedTile(selectedTile === tileId ? null : tileId);
  };

  const renderTileDetails = (tile: DashboardTile) => {
    return (
      <Paper
        elevation={1}
        sx={{
          mt: 2,
          p: { xs: 2, sm: 3 },
          bgcolor: 'background.paper',
        }}
      >
        <Typography
          variant="h6"
          sx={{
            mb: { xs: 2, sm: 3 },
            color: 'text.primary',
            fontWeight: 500,
          }}
        >
          {tile.title} Details
        </Typography>
        {(tile.id === 'current-in-bay' || tile.id === 'total-admissions') && (
          <List sx={{ py: 0 }}>
            {tile.data.map((admission: WelfareAdmission) => (
              <ListItem
                key={admission._id}
                sx={{
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  borderBottom: 1,
                  borderColor: 'divider',
                  py: { xs: 1.5, sm: 2 },
                }}
              >
                <Typography variant="subtitle2" color="text.primary">
                  {admission.FirstName} {admission.Surname}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {admission.BaysOrChairs} {admission.Location ? `#${admission.Location}` : ''}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Admitted: {admission.Attended ? new Date(admission.Attended).toLocaleTimeString() : 'N/A'}
                </Typography>
              </ListItem>
            ))}
          </List>
        )}
        {(tile.id === 'under-18-substance' || tile.id === 'under-18-safeguarding') && (
          <List sx={{ py: 0 }}>
            {tile.data.map((admission: WelfareAdmission) => (
              <ListItem
                key={admission._id}
                sx={{
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  borderBottom: 1,
                  borderColor: 'divider',
                  py: { xs: 1.5, sm: 2 },
                }}
              >
                <Typography variant="subtitle2" color="text.primary">
                  {admission.FirstName} {admission.Surname}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Age: {admission.Age || calculateAge(admission.DOB)}
                </Typography>
                {tile.id === 'under-18-substance' && admission.SubstanceUsed && (
                  <Typography variant="body2" color="text.secondary">
                    Substances: {admission.SubstanceUsed.join(', ')}
                  </Typography>
                )}
                {tile.id === 'under-18-safeguarding' && admission.Safeguarding && (
                  <Typography variant="body2" color="text.secondary">
                    Safeguarding: {admission.Safeguarding}
                  </Typography>
                )}
                <Typography variant="body2" color="text.secondary">
                  Admitted: {admission.Attended ? new Date(admission.Attended).toLocaleString() : 'N/A'}
                </Typography>
              </ListItem>
            ))}
          </List>
        )}
        {tile.id === 'total-items' && (
          <List sx={{ py: 0 }}>
            {ITEM_FIELDS.map(field => {
              const total = tile.data.reduce((sum, item: ItemDocument) => 
                sum + (typeof item[field] === 'number' ? item[field] : 0), 0
              );
              if (total > 0) {
                return (
                  <ListItem
                    key={field}
                    sx={{
                      borderBottom: 1,
                      borderColor: 'divider',
                      py: { xs: 1.5, sm: 2 },
                    }}
                  >
                    <Typography variant="subtitle2" color="text.primary">
                      {field}: {total}
                    </Typography>
                  </ListItem>
                );
              }
              return null;
            })}
          </List>
        )}
        {tile.id === 'lost-property' && (
          <List sx={{ py: 0 }}>
            {tile.data.map((item: LostPropertyItem) => (
              <ListItem
                key={item._id}
                sx={{
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  borderBottom: 1,
                  borderColor: 'divider',
                  py: { xs: 1.5, sm: 2 },
                }}
              >
                <Typography variant="subtitle2" color="text.primary">
                  {item.category} - {item.quickDescription}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Found: {new Date(item.timeFound).toLocaleString()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Location: {item.whereFound}
                </Typography>
                {item.description && (
                  <Typography variant="body2" color="text.secondary">
                    Details: {item.description}
                  </Typography>
                )}
              </ListItem>
            ))}
          </List>
        )}
      </Paper>
    );
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  if (!activeFestival) {
    return (
      <Box sx={{ p: 4, textAlign: 'center' }}>
        <Typography color="text.secondary">
          Please select a festival to view dashboard data.
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      {/* Header with cache status and refresh button */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" sx={{ mb: 1 }}>
            Dashboard - {activeFestival.name}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {isFromCache && (
              <Chip
                icon={<CachedIcon />}
                label="Cached Data"
                size="small"
                color="info"
                variant="outlined"
              />
            )}
            <Typography variant="body2" color="text.secondary">
              {new Date(activeFestival.startDate).toLocaleDateString()} - {new Date(activeFestival.endDate).toLocaleDateString()}
            </Typography>
          </Box>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            onClick={handleRefresh}
            disabled={isLoading}
            startIcon={<CachedIcon />}
          >
            Refresh
          </Button>

          <Tooltip title={syncError || ''}>
            <Button
              variant="contained"
              color={getSyncButtonColor()}
              startIcon={getSyncButtonIcon()}
              onClick={handleSync}
              disabled={isSyncing}
            >
              {getSyncButtonText()}
            </Button>
          </Tooltip>

          <Tooltip title="Sync Settings">
            <Button
              variant="outlined"
              startIcon={<SettingsIcon />}
              onClick={() => setSyncSettingsOpen(true)}
              disabled={isSyncing}
            >
              Settings
            </Button>
          </Tooltip>
        </Box>
      </Box>

      <Grid container spacing={{ xs: 2, sm: 3 }}>
        {dashboardTiles.map((tile) => (
          <Grid size={{ xs: 12, sm: 6, lg: 4 }} key={tile.id}>
            <Box sx={{ position: 'relative' }}>
              <Button
                onClick={() => handleTileClick(tile.id)}
                sx={{
                  width: '100%',
                  textAlign: 'left',
                  p: { xs: 2, sm: 3 },
                  bgcolor: 'background.paper',
                  borderRadius: 1,
                  boxShadow: 1,
                  '&:hover': {
                    bgcolor: 'action.hover',
                  },
                }}
              >
                <Box sx={{ width: '100%' }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: 500,
                        color: 'text.secondary',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {tile.title}
                    </Typography>
                    {tile.alert && (
                      <WarningIcon
                        sx={{
                          color: 'error.main',
                          fontSize: 20,
                        }}
                      />
                    )}
                  </Box>
                  <Typography
                    variant="h4"
                    sx={{
                      mt: 1,
                      fontWeight: 600,
                      color: tile.alert ? 'error.main' : 'text.primary',
                    }}
                  >
                    {tile.value}
                  </Typography>
                  {tile.subtitle && (
                    <Typography
                      variant="body2"
                      sx={{
                        mt: 1,
                        color: 'text.secondary',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {tile.subtitle}
                    </Typography>
                  )}
                </Box>
              </Button>
              {selectedTile === tile.id && renderTileDetails(tile)}
            </Box>
          </Grid>
        ))}
      </Grid>

      {/* Sync error alert */}
      {syncError && (
        <Alert severity="error" sx={{ mt: 2 }} onClose={() => setSyncError(null)}>
          {syncError}
        </Alert>
      )}

      {/* Sync settings dialog */}
      <SyncSettingsDialog
        open={syncSettingsOpen}
        onClose={() => setSyncSettingsOpen(false)}
      />
    </Box>
  );
};
