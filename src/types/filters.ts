/**
 * Type definitions for the Admissions Cross-Filtering System
 * Based on the architectural specification in docs/admissions-cross-filtering-architecture.md
 */

import { WelfareAdmission } from './admission';

/**
 * Enum defining the different types of filters available
 */
export enum FilterType {
  TIME = 'TIME',
  REASON = 'REASON',
  AGE = 'AGE'
}

/**
 * Interface defining the structure of filter state
 */
export interface AdmissionsFilterState {
  /** Array of active time filters (e.g., ["10:00-11:00", "14:00-15:00"]) */
  timeFilters: string[];
  /** Array of active reason filters (e.g., ["Substance Use", "Mental Health"]) */
  reasonFilters: string[];
  /** Array of active age filters (e.g., ["18-25", "26-35"]) */
  ageFilters: string[];
  /** Filter mode: 'AND' for intersection, 'OR' for union */
  filterMode: 'AND' | 'OR';
}

/**
 * Interface defining the actions available for filter management
 */
export interface FilterActions {
  /** Add a filter of the specified type */
  addFilter: (type: FilterType, value: string) => void;
  /** Remove a specific filter */
  removeFilter: (type: FilterType, value: string) => void;
  /** Clear all active filters */
  clearFilters: () => void;
  /** Clear all filters of a specific type */
  clearFilterType: (type: FilterType) => void;
  /** Toggle between AND/OR filter modes */
  toggleFilterMode: () => void;
  /** Check if a specific filter is active */
  isFilterActive: (type: FilterType, value: string) => boolean;
}

/**
 * Interface for chart data structure used across different chart types
 */
export interface ChartData {
  count: number;
  [key: string]: string | number;
}

/**
 * Interface for time-specific chart data
 */
export interface TimeOfDayData {
  hour: number;
  count: number;
  label: string;
}

/**
 * Interface defining the filtered data returned by the filtering system
 */
export interface FilteredData {
  /** Filtered admission records based on active filters */
  filteredAdmissions: WelfareAdmission[];
  /** Time-based chart data after filtering */
  timeData: TimeOfDayData[];
  /** Reason-based chart data after filtering */
  reasonData: ChartData[];
  /** Age-based chart data after filtering */
  ageData: ChartData[];
  /** Summary statistics about the filtering results */
  filterSummary: FilterSummary;
}

/**
 * Interface for filter summary statistics
 */
export interface FilterSummary {
  /** Total number of records before filtering */
  totalRecords: number;
  /** Number of records after filtering */
  filteredRecords: number;
  /** Number of active filters */
  activeFilterCount: number;
  /** Current filter mode */
  filterMode: 'AND' | 'OR';
}

/**
 * Configuration interface for chart interactions
 */
export interface ChartInteractionConfig {
  /** Type of filter this chart applies */
  filterType: FilterType;
  /** Threshold in milliseconds for long press detection */
  threshold: number;
  /** Whether click interactions are enabled */
  enableClick: boolean;
  /** Whether long press interactions are enabled */
  enableLongPress: boolean;
}

/**
 * Props interface for enhanced chart components
 */
export interface EnhancedChartProps {
  /** Chart data to display */
  data: ChartData[] | TimeOfDayData[];
  /** Callback when a filter is selected via click */
  onFilterSelect: (filterType: FilterType, value: string) => void;
  /** Callback when a long press occurs */
  onLongPress: (filterType: FilterType, value: string, data: any[]) => void;
  /** Array of currently active filters for this chart type */
  activeFilters: string[];
  /** Whether this chart's data is currently filtered */
  isFiltered: boolean;
  /** Current filter mode */
  filterMode: 'AND' | 'OR';
  /** Chart interaction configuration */
  config: ChartInteractionConfig;
}

/**
 * Interface for filter chip component props
 */
export interface FilterChipProps {
  /** Display label for the filter */
  label: string;
  /** Type of filter this chip represents */
  filterType: FilterType;
  /** Callback when the chip is removed */
  onRemove: () => void;
  /** Color variant for the chip */
  color?: 'primary' | 'secondary' | 'default';
}

/**
 * Interface for filter mode toggle component props
 */
export interface FilterModeToggleProps {
  /** Current filter mode */
  mode: 'AND' | 'OR';
  /** Callback when mode is toggled */
  onToggle: () => void;
  /** Whether the toggle is disabled */
  disabled?: boolean;
}

/**
 * Interface for filter summary component props
 */
export interface FilterSummaryProps {
  /** Total number of records */
  totalRecords: number;
  /** Number of filtered records */
  filteredRecords: number;
  /** Number of active filters */
  activeFilterCount: number;
  /** Current filter mode */
  filterMode: 'AND' | 'OR';
}

/**
 * Interface for filter control panel props
 */
export interface FilterControlsProps {
  /** Current filter state */
  filterState: AdmissionsFilterState;
  /** Filter actions */
  filterActions: FilterActions;
  /** Filter summary data */
  filterSummary: FilterSummary;
}

/**
 * Type for filter application function
 */
export type FilterApplicator = (
  admissions: WelfareAdmission[],
  filterState: AdmissionsFilterState
) => WelfareAdmission[];

/**
 * Type for chart data generator functions
 */
export type ChartDataGenerator<T = ChartData> = (
  admissions: WelfareAdmission[]
) => T[];