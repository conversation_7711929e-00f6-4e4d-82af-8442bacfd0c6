// Production secrets file - Updated to use Cloudflare Worker proxy
// This configuration resolves CORS issues by using the worker proxy instead of direct CouchDB connection

console.log('✅ secrets.ts loaded - using worker proxy for CORS compatibility');

export const secrets = {
  database: {
    // Database configuration - Updated to use worker proxy
    localName: 'ithinc_welfare', // Local database name
    remoteUrl: 'https://database-proxy.brisflix.workers.dev', // Worker proxy URL with CORS support
    remoteName: 'ithinc_welfare', // Remote database name
    
    // Authentication is handled by the worker proxy, not directly by the app
    username: undefined,
    password: undefined,
    
    // Selective sync configuration (optional)
    selectiveSync: {
      enabled: false, // Disabled by default for production
      defaultFestivalScope: undefined // No default festival scope
    }
  }
};

// NOTE: This configuration now uses the Cloudflare Worker proxy which:
// - Handles CORS headers properly for browser requests
// - Manages authentication with CouchDB internally
// - Resolves the "Response to preflight request doesn't pass access control check" error