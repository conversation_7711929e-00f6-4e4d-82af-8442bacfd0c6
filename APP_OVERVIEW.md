# iThinc Welfare Management System

## Overview
The iThinc Welfare Management System is a comprehensive web application designed to manage welfare services at festivals and events. It provides tools for managing admissions, shift scheduling, inventory tracking, lost property management, sensory hub visitor tracking, and comprehensive reporting with advanced analytics. The system features robust authentication, role-based access control, and optimized performance for iPad deployment in field conditions.

## Project Structure

```
/
├── public/                      # Static assets
│   ├── _redirects              # Netlify redirect rules
│   ├── favicon.svg             # Site favicon
│   ├── index.html              # Main HTML file
│   ├── ithink-logo.svg         # Brand logo
│   ├── manifest.json           # PWA manifest
│   └── robots.txt              # Search engine rules
│
├── src/                        # Source code
│   ├── __tests__/             # Test files
│   │   ├── admissions.test.tsx # Admission component tests
│   │   └── database.test.ts    # Database service tests
│   │
│   ├── assets/                 # Project assets
│   │   └── ithink-logo.svg     # Brand logo for components
│   │
│   ├── components/             # React components
│   │   ├── reports/            # Reporting components
│   │   │   ├── admissions/     # Admission report components
│   │   │   ├── frontOfHouse/   # Front of house report components
│   │   │   ├── lostProperty/   # Lost property report components
│   │   │   ├── sensoryHub/     # Sensory hub report components
│   │   │   └── shared/         # Shared report components
│   │   │
│   │   ├── admission/          # Admission form components
│   │   │   ├── AdmissionModals.tsx    # Modal dialogs
│   │   │   ├── AdmissionNotesSection.tsx # Notes section
│   │   │   ├── LocationSection.tsx    # Location management
│   │   │   ├── PersonalInfoSection.tsx # Personal details
│   │   │   ├── PhysicalDescriptionSection.tsx # Physical description
│   │   │   ├── ReferralSection.tsx    # Referral information
│   │   │   ├── SafeguardingSection.tsx # Safeguarding concerns
│   │   │   ├── StyledComponents.tsx   # Shared styles
│   │   │   └── SubstanceUseSection.tsx # Substance use info
│   │   │
│   │   ├── sensory-hub/       # Sensory hub components
│   │   │   └── SensoryHubVisitForm.tsx # Visit logging form
│   │   │
│   │   ├── shifts/            # Shift management components
│   │   │   ├── ShiftConfigForm.tsx    # Shift pattern configuration
│   │   │   ├── ShiftScheduleTable.tsx # Shift schedule display/edit
│   │   │   └── TeamLeaderForm.tsx     # Team leader management
│   │   │
│   │   ├── festival/          # Festival management components
│   │   │   ├── AdminPanelSection.tsx  # Admin controls
│   │   │   ├── FestivalForm.tsx       # Festival creation/editing
│   │   │   ├── FestivalHeader.tsx     # Festival info display
│   │   │   ├── FestivalLinks.tsx      # Festival URL management
│   │   │   └── FestivalList.tsx       # Festival listing
│   │   │
│   │   ├── AdminPanel.tsx     # Admin control panel
│   │   ├── AdmissionForm.tsx  # Main admission form container
│   │   ├── AdmissionList.tsx  # Admission listing
│   │   ├── Dashboard.tsx      # Main dashboard
│   │   ├── ErrorBoundary.tsx  # Error handling
│   │   ├── FestivalSelector.tsx # Festival selection
│   │   ├── LostPropertyPage.tsx # Lost property management
│   │   ├── ShiftNotes.tsx     # Shift notes management
│   │   └── Sidebar.tsx        # Navigation sidebar
│   │
│   ├── config/                # Configuration files
│   │   ├── secrets.ts         # API keys and credentials
│   │   └── secrets.example.ts # Template for secrets
│   │
│   ├── contexts/              # React contexts
│   │   ├── AuthContext.tsx    # Authentication and access control
│   │   ├── FestivalContext.tsx # Festival state management
│   │   └── SiteLocationContext.tsx # Site location management
│   │
│   ├── hooks/                 # Custom React hooks
│   │   ├── useAdmissions.ts   # Admission data management
│   │   ├── useAdmissionForm.ts # Admission form logic
│   │   ├── useAdmissionsFilters.ts # Advanced filtering for reports
│   │   ├── useLongPress.ts    # Long press gesture detection
│   │   ├── useReportData.ts   # Report data handling
│   │   ├── useSmartData.ts    # Simplified data loading
│   │   └── useSyncStatus.ts   # Database sync status monitoring
│   │
│   ├── pages/                 # Page components
│   │   ├── AccessManagementPage.tsx # User access and role management
│   │   ├── AdmissionsPage.tsx     # Admissions listing
│   │   ├── ChangelogPage.tsx      # Application changelog
│   │   ├── DashboardPage.tsx      # Main dashboard
│   │   ├── FeedbackManagementPage.tsx # Feedback administration
│   │   ├── FestivalManagementPage.tsx # Festival management
│   │   ├── FrontOfHousePage.tsx   # Front desk operations
│   │   ├── KnowledgeBasePage.tsx  # Knowledge base resources
│   │   ├── NewAdmissionPage.tsx   # New admission entry
│   │   ├── ReportsPage.tsx        # Reports and analytics
│   │   ├── SensoryHubPage.tsx     # Sensory hub visitor tracking
│   │   ├── ShiftsPage.tsx         # Shift management
│   │   ├── UserGuidePage.tsx      # User documentation
│   │   └── UserGuidePageSimple.tsx # Simplified user guide
│   │
│   ├── services/              # Service layer
│   │   └── database/          # Database services
│   │       ├── access-manager.ts       # User access and role management
│   │       ├── admission-manager.ts    # Admission operations
│   │       ├── bay-manager.ts          # Bay/chair management
│   │       ├── config.ts               # Database configuration
│   │       ├── feedback-manager.ts     # Feedback operations
│   │       ├── festival-manager.ts     # Festival operations
│   │       ├── item-manager.ts         # Inventory operations
│   │       ├── knowledgebase-manager.ts # Knowledge base operations
│   │       ├── lost-property-manager.ts # Lost property operations
│   │       ├── sensory-hub-manager.ts  # Sensory hub operations
│   │       ├── shift-manager.ts        # Shift operations
│   │       └── index.ts                # Service exports
│   │
│   ├── types/                 # TypeScript type definitions
│   │   ├── admission.ts       # Admission types
│   │   ├── base.ts           # Common base types and interfaces
│   │   ├── festival.ts       # Festival types
│   │   ├── item.ts           # Inventory types
│   │   ├── sensory-hub.ts    # Sensory hub types
│   │   ├── shift.ts          # Shift types
│   │   └── index.ts          # Type exports
│   │
│   ├── utils/                 # Utility functions
│   │   ├── reportDataProcessing.ts # Report data handling
│   │   ├── validation.ts     # Form validation
│   │   └── version.ts        # Version management
│   │
│   ├── App.tsx               # Main application component
│   └── index.tsx             # Application entry point
│
├── rsbuild.config.ts          # Rsbuild configuration
├── nginx.conf                 # Nginx server configuration
├── package.json              # Project dependencies
├── supervisord.conf          # Process management
└── tsconfig.json             # TypeScript configuration
```

## Core Features

### Festival Management
- Create and manage multiple festivals/events
- Track festival dates, locations, and important URLs
- Maintain festival-specific notes and information
- Toggle active status for current events
- Multi-site support with Arena and Campsite locations:
  - Site-specific data separation
  - Easy site switching from festival header
  - Site location persistence across sessions
  - Site-specific inventory and admission tracking

### Welfare Admissions
- Record and track welfare admissions with detailed information
- Modular form structure with specialized sections:
  - Personal Information (with simplified ethnicity options and optional surname)
  - Location Management
  - Physical Description
  - Substance Use (with optimized substance order)
  - Safeguarding Concerns
  - Referral Information
  - Admission Notes
- Custom hooks for form logic and state management
- Separate modal components for actions like discharge and re-admission
- Track substance use, mental health, and safeguarding concerns
- Manage bay/chair assignments and occupancy
- Maintain admission history and additional notes
- Enhanced search and filtering capabilities:
  - Quick search functionality
  - Column-specific filtering
  - Advanced sorting options
  - Proper data type handling for dates and numbers
- Required discharge notes with configurable discharge time

### Shift Management
- Configure shift patterns with customizable parameters
- Manage team leaders and their assigned teams
- Generate and maintain shift schedules
- Support for multiple shifts per day
- Automatic shift rotation pattern (A -> B -> C -> D -> E)
- Track team members and shift notes
- Site-specific shift assignments and notes

### Inventory Management
- Track essential items:
  - Medical supplies (Sanitizer, ToiletRoll)
  - Weather protection (Suncream, Poncho)
  - Safety items (Earplugs, Condoms)
  - Wristbands (Children's and General)
  - Other supplies (Water, Charging facilities)
- Monitor stock levels and usage
- Site-specific inventory tracking
- Long-press functionality for entering specific quantities:
  - Optimized for iPad and touch devices
  - Maintains backward compatibility with single-click increments
  - Improves efficiency for distributing multiple items at once

### Lost Property
- Record found items with detailed descriptions
- Categorize items (Phone, Passport, Keys, etc.)
- Track item status (claimed/unclaimed)
- Record item return information
- Support for item photos/attachments
- Enhanced search and filtering:
  - Quick search functionality
  - Advanced filtering options
  - Improved sorting capabilities
- Ability to reverse returned item status with "Unmark as Returned" functionality

### Sensory Hub Visitor Tracking
- Comprehensive visitor tracking system for sensory hub services
- Record visitor interactions with detailed categorization:
  - Visit purpose tracking (looking around vs. using services)
  - User type classification (crew vs. public visitors)
  - Team name tracking for crew visits
  - Timestamp-based visit logging
- Site-specific tracking for multi-location festivals
- Real-time visit data management and display
- Integration with existing festival and location management systems

### Reporting & Analytics
- **Comprehensive Report Generation** for all data types:
  - Admissions with detailed patient analytics
  - Front of House service tracking
  - Lost Property management
  - Sensory Hub visitor analytics
- **Advanced Interactive Cross-Filtering System** (Admissions Reports):
  - **Click-to-filter functionality** across all chart types (Time of Day, Admissions by Reason, Age Distribution)
  - **Cross-chart coordination** with real-time updates based on active filters
  - **Dual interaction modes**: Click for filtering vs. long press (500ms) for detailed modals
  - **Flexible filter logic** with AND/OR mode toggle for intersection vs union operations
  - **Visual filter management** with color-coded filter chips and comprehensive controls
  - **Context-aware modal system** showing detailed patient data based on chart type
  - **Performance-optimized** for large datasets with memoized data processing
- **Bulk Operations**:
  - Bulk delete functionality across all report tables
  - Checkbox selection system with "Select All" option
  - Confirmation dialogs for safety and data protection
- **Export Capabilities**:
  - PDF export functionality for all report types
  - CSV export for spreadsheet analysis
  - Complete database export (JSON/CSV formats)
- **Interactive Features**:
  - Clickable table rows for detailed record information
  - Advanced search and filtering options
  - Date range filtering and data aggregation
  - Site-specific reporting capabilities

### Feedback System
- **User Feedback Collection**:
  - Floating feedback button available across all pages
  - Automatic capture of current page context
  - Name and comment fields for detailed feedback
- **Admin Management Interface**:
  - Comprehensive feedback management in admin panel
  - Feedback status tracking (resolved/unresolved)
  - Feedback categorization and organization
- **Database Integration**:
  - Optimized database performance with indexed queries
  - Sync support with existing database infrastructure
  - Included in data cleanup and export functionality

### Access Management & Security
- **Role-Based Access Control**:
  - Four-tier access system (Admin, Partner, User, Public)
  - Page-level access permissions
  - User role assignment and management
- **Authentication System**:
  - Cloudflare Access email-based authentication
  - IP-based access control
  - Database-backed access control with role hierarchy
- **Access Management Interface**:
  - Dedicated Access Management page for administrators
  - User role assignment and modification
  - Page access permission configuration
  - Real-time access control updates

### Knowledge Base
- **Organized Resource Repository** for staff reference:
  - Category and subcategory organization system
  - Initial sections: Substance Info, Mental Health, Support Contacts
  - Custom categories can be created as needed
- **Resource Management**:
  - Phone number and URL support for external resources
  - Cross-festival resource sharing with visual indicators
  - Quick descriptions for easy resource identification
- **User Interface**:
  - Tile-based dashboard layout with improved usability
  - One-click access to external sites and resources
  - Easy navigation and resource discovery

## Technical Architecture

### Current Version: 1.14.0
- **Latest Release**: February 7, 2025
- **Major Features**: Interactive cross-filtering system for admissions reports
- **Version Management**:
  - Version information maintained in [`package.json`](package.json:3)
  - Changes documented in [`CHANGELOG.md`](CHANGELOG.md:1)
  - Sidebar automatically displays current version
- **Semantic Versioning**: MAJOR.MINOR.PATCH format
  - MAJOR: Breaking changes
  - MINOR: New features (current: advanced reporting features)
  - PATCH: Bug fixes and minor improvements

### Database Architecture
- **SimplifiedDatabaseService**: Direct PouchDB access with immediate initialization for optimal performance
- **iPad-Optimized Configuration**: Conservative settings designed for resource-constrained devices
  - 8-second timeouts for slower network conditions
  - Optimized batch sizes (increased from 3 to 75 items for improved throughput)
  - Enhanced revision limits (100 revisions) to prevent conflicts
  - Auto-compaction enabled to prevent database bloat
- **CouchDB Integration**: Remote storage with enhanced reliability and conflict resolution
- **Cloudflare Workers Proxy**: Secure database access with authentication and CORS support
  - Handles authentication and authorization
  - Provides CORS support and security layer
  - Manages database connections and rate limiting
  - Improved cold start performance and reliability
- **Soft Deletion Infrastructure**: Comprehensive tombstone record system for data integrity
  - **Tombstone Records**: Deleted records marked with `isDeleted: true` and `deletedAt` timestamp
  - **Database Index Integration**: All indexes include `isDeleted` field for efficient filtering
  - **Automatic Query Filtering**: Queries automatically exclude deleted records unless explicitly requested
  - **Cascading Soft Deletion**: Festival deletion triggers soft deletion of all associated data
  - **Two-Tier Cleanup Policy**: Graduated deletion approach with 6-month hard deletion
  - **iPad Cache Persistence Resolution**: Eliminates deleted record reappearance issues
- **Enhanced Sync Strategy**: Non-blocking operations with improved reliability
  - Local-first operations for immediate responsiveness
  - Background sync with comprehensive error handling
  - Sync failures don't block data access
  - Progressive loading with immediate data availability
  - Enhanced batch processing with monitoring and diagnostics

### Data Models
- **Festivals**: Event details, status, and configuration
- **Admissions**: Comprehensive welfare cases and patient information
- **Shifts**: Staff scheduling and team assignments
- **Inventory**: Supplies and equipment tracking with usage analytics
- **Lost Property**: Found items with detailed categorization and status
- **Feedback**: User feedback collection with admin management
- **Sensory Hub Visits**: Visitor interactions and service usage tracking
- **Access Control**: User roles and page-level permissions
- **Knowledge Base**: Organized resource repository with categorization

### Security & Authentication
- **Multi-Layer Authentication**:
  - Cloudflare Access email-based authentication
  - IP-based access control for additional security
  - Basic authentication for database access
- **Role-Based Access Control (RBAC)**:
  - Four-tier role system: Admin, Partner, User, Public
  - Page-level access permissions
  - Database-backed access control with role hierarchy
- **Access Management System**:
  - Dedicated admin interface for user and permission management
  - Real-time access control updates
  - Comprehensive audit trail for access changes
- **Data Security**:
  - Secure data synchronization with encrypted connections
  - Soft deletion for data integrity and recovery
  - Regular automated cleanup with data retention policies

### User Interface & Experience
- **Modern React Architecture**:
  - React 19.1.0 with latest features and optimizations
  - Material UI 7.0.2 components and styling system
  - Modular component architecture with clear separation of concerns
  - Custom hooks for logic separation and reusability
- **Responsive Design**:
  - Optimized for various devices (desktop, tablet, mobile)
  - iPad-specific optimizations for field deployment
  - Collapsible sidebar for improved space utilization
  - Touch-friendly interactions with long press detection
- **Enhanced User Experience**:
  - **Advanced Sync Status Indicator**: Always-visible status in sidebar
    - Real-time connection monitoring with color-coded status
    - Initial sync detection with appropriate loading indicators
    - Manual sync capability via click interaction
    - Responsive design adapting to sidebar state
  - **Interactive Data Visualization**: Advanced cross-filtering system
    - Click-to-filter functionality across chart types
    - Dual interaction modes (click vs. long press)
    - Visual filter management with color-coded chips
  - **Comprehensive Navigation**: Integrated user guide and documentation
    - Tabbed sections with comprehensive FAQ
    - Context-sensitive help and tooltips
    - Easy access to changelog and version information
- **Performance Optimizations**:
  - Code splitting with lazy-loaded components
  - Suspense with fallback loading indicators
  - Memoized data processing for large datasets
  - Debounced interactions to prevent excessive re-renders

## Deployment & Infrastructure
- **Containerization**: Docker-based deployment with optimized configuration
- **Web Server**: Nginx configuration for production deployment
- **Build System**: Rsbuild for modern bundling and optimization
- **Environment Configuration**:
  - Environment variable support for flexible deployment
  - Fallback system: environment variables → secrets file → default config
  - Support for platforms like Sevalla with ITHINC_DB_* variables
- **Cloudflare Integration**:
  - Workers proxy for database access and CORS handling
  - Access authentication for secure user management
  - Rate limiting and performance optimization

## Data Management & Operations
- **Automated Database Cleanup System**:
  - **Graduated Cleanup Policy**: Two-tier approach with soft deletion followed by hard deletion after 6 months
  - **Intelligent Cleanup Rules**:
    - Discharged admissions older than 3 months are soft deleted
    - Resolved feedback older than 3 months are soft deleted
    - Claimed lost property items older than 3 months are soft deleted
    - Old item aggregation counts are soft deleted following same policy
  - **Safe Operations**: Comprehensive validation prevents accidental deletion of active records
  - **Performance Optimization**: Regular maintenance maintains optimal query performance
- **Comprehensive Export System**:
  - **Multiple Format Support**: JSON (complete metadata) and CSV (spreadsheet analysis)
  - **Complete Data Coverage**: All system data types included in exports
  - **Export Metadata**: Timestamps, version information, and statistics
  - **Enhanced Database Managers**: Consistent export interfaces across all services
- **Admin Operations Interface**:
  - **Database Operations Panel**: Integrated into Festival Management page
  - **One-Click Operations**: Database cleanup and export functionality
  - **User-Friendly Feedback**: Clear status indicators and confirmation dialogs
  - **Bulk Operations**: Bulk delete functionality across all report tables
- **Sync & Reliability**:
  - Enhanced sync reliability with improved error handling
  - Background sync operations that don't block user interactions
  - Comprehensive conflict resolution and data consistency
  - Backup and recovery support with soft deletion awareness

## Recent Major Enhancements (v1.14.0)

### Interactive Cross-Filtering System
- **Advanced Analytics**: Click-to-filter functionality across all chart types in admissions reports
- **Dual Interaction Modes**: Click for filtering vs. long press (500ms) for detailed modals
- **Flexible Filter Logic**: AND/OR mode toggle for intersection vs union operations
- **Visual Filter Management**: Color-coded filter chips with comprehensive controls
- **Performance Optimized**: Memoized data processing for large datasets

### Bulk Operations & Data Management
- **Bulk Delete Functionality**: Comprehensive bulk delete across all report tables
- **Enhanced Export Capabilities**: Complete database export in JSON and CSV formats
- **Improved Sync Performance**: Optimized batch processing with monitoring and diagnostics

### User Experience Improvements
- **Enhanced Sync Status**: Real-time sync monitoring with visual indicators
- **Responsive Design**: Collapsible sidebar and improved mobile experience
- **Advanced Search & Filtering**: Comprehensive filtering across all data types

## Universal Festival Selection
- **Always-Visible Access**: "Active Festival" button in sidebar for easy switching
- **Modal Selection Interface**: Festival selection without requiring management access
- **Multi-User Support**: Browser-specific festival selection for shared environments
- **Site Location Persistence**: Maintained site selection for multi-site festivals

## Future Development Roadmap
- **Advanced Analytics**: Custom report generation and advanced data visualization
- **Mobile Optimization**: Enhanced touch-friendly controls and mobile-specific features
- **Integration Capabilities**: External system integration and API development
- **Internationalization**: Multi-language support and localization features
- **Enhanced Automation**: Automated reporting and notification systems
