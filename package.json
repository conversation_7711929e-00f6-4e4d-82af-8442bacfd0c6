{"name": "ithinc-welfare", "version": "1.15.0", "private": true, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@heroicons/react": "^2.1.1", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@mui/x-data-grid": "^8.0.0", "@react-pdf/renderer": "^4.3.0", "date-fns": "^4.1.0", "lodash": "^4.17.21", "pouchdb-browser": "^9.0.0", "pouchdb-find": "^9.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-responsive": "^10.0.1", "react-router-dom": "^7.5.1", "recharts": "^2.15.3", "uuid": "^11.1.0", "workbox-core": "^7.3.0", "workbox-precaching": "^7.3.0", "workbox-routing": "^7.3.0", "workbox-strategies": "^7.3.0"}, "scripts": {"start": "rsbuild dev", "build": "rsbuild build", "build:analyze": "ANALYZE=true rsbuild build", "deploy": "wrangler deploy", "test": "react-app-rewired test", "setup-symlinks": "node scripts/setup-symlinks.js", "postinstall": "npm run setup-symlinks", "prebuild": "npm run setup-symlinks"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@rsbuild/core": "^1.3.9", "@rsbuild/plugin-react": "^1.2.0", "@rsbuild/plugin-svgr": "^1.2.0", "@rsbuild/plugin-type-check": "^1.2.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/node": "^22.14.1", "@types/pouchdb-browser": "^6.1.5", "@types/pouchdb-find": "^7.3.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/uuid": "^10.0.0", "typescript": "^5.8.3", "web-vitals": "^4.2.4", "webpack-bundle-analyzer": "^4.10.2", "wrangler": "^4.12.0"}}